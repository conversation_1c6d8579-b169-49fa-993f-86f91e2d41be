/**
 * 测试环境配置文件
 * 配置 Vue Test Utils 和相关测试工具
 */

import { config } from '@vue/test-utils'
import { createPinia } from 'pinia'
import { ElButton, ElForm, ElFormItem, ElInput, ElDialog } from 'element-plus'

// 全局组件注册
config.global.components = {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElDialog
}

// 全局插件配置
config.global.plugins = [createPinia()]

// 全局 mocks
config.global.mocks = {
  $t: (key: string) => key,
  $router: {
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn()
  },
  $route: {
    path: '/',
    query: {},
    params: {}
  }
}

// 全局 stubs
config.global.stubs = {
  ContentWrap: true,
  StatisticCards: true,
  DataTable: true,
  SimpleFormField: true,
  CreateForm: true,
  Dialog: true
}
