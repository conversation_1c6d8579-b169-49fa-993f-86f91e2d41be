/**
 * 测试运行脚本
 * 提供不同类型的测试运行命令
 */

import { execSync } from 'child_process'
import { existsSync, mkdirSync } from 'fs'
import { resolve } from 'path'

// 确保测试结果目录存在
const resultsDir = resolve(__dirname, 'results')
if (!existsSync(resultsDir)) {
  mkdirSync(resultsDir, { recursive: true })
}

// 测试命令配置
const testCommands = {
  // 运行所有测试
  all: 'vitest run',
  
  // 运行单元测试
  unit: 'vitest run tests/unit',
  
  // 运行组件测试
  component: 'vitest run tests/components',
  
  // 运行集成测试
  integration: 'vitest run tests/integration',
  
  // 运行测试并生成覆盖率报告
  coverage: 'vitest run --coverage',
  
  // 监听模式运行测试
  watch: 'vitest',
  
  // 运行特定文件的测试
  file: (filename: string) => `vitest run ${filename}`,
  
  // 运行测试并生成 JUnit 报告
  ci: 'vitest run --reporter=junit --outputFile=tests/results/junit.xml'
}

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function colorLog(message: string, color: keyof typeof colors = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// 执行测试命令
function runTest(command: string, description: string) {
  try {
    colorLog(`\n🚀 ${description}`, 'cyan')
    colorLog('=' .repeat(50), 'blue')
    
    const startTime = Date.now()
    execSync(command, { stdio: 'inherit' })
    const endTime = Date.now()
    
    colorLog(`\n✅ ${description} 完成`, 'green')
    colorLog(`⏱️  耗时: ${(endTime - startTime) / 1000}s`, 'yellow')
    
  } catch (error) {
    colorLog(`\n❌ ${description} 失败`, 'red')
    console.error(error)
    process.exit(1)
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2)
  const command = args[0]
  
  switch (command) {
    case 'all':
      runTest(testCommands.all, '运行所有测试')
      break
      
    case 'unit':
      runTest(testCommands.unit, '运行单元测试')
      break
      
    case 'component':
      runTest(testCommands.component, '运行组件测试')
      break
      
    case 'integration':
      runTest(testCommands.integration, '运行集成测试')
      break
      
    case 'coverage':
      runTest(testCommands.coverage, '运行测试并生成覆盖率报告')
      break
      
    case 'watch':
      colorLog('\n👀 启动监听模式...', 'cyan')
      execSync(testCommands.watch, { stdio: 'inherit' })
      break
      
    case 'ci':
      runTest(testCommands.ci, '运行 CI 测试')
      break
      
    case 'file':
      const filename = args[1]
      if (!filename) {
        colorLog('❌ 请提供文件名', 'red')
        process.exit(1)
      }
      runTest(testCommands.file(filename), `运行文件测试: ${filename}`)
      break
      
    default:
      colorLog('\n📋 可用的测试命令:', 'bright')
      colorLog('  all        - 运行所有测试', 'cyan')
      colorLog('  unit       - 运行单元测试', 'cyan')
      colorLog('  component  - 运行组件测试', 'cyan')
      colorLog('  integration- 运行集成测试', 'cyan')
      colorLog('  coverage   - 运行测试并生成覆盖率报告', 'cyan')
      colorLog('  watch      - 监听模式运行测试', 'cyan')
      colorLog('  ci         - 运行 CI 测试', 'cyan')
      colorLog('  file <name>- 运行特定文件的测试', 'cyan')
      colorLog('\n示例:', 'yellow')
      colorLog('  npm run test all', 'green')
      colorLog('  npm run test unit', 'green')
      colorLog('  npm run test file SearchForm.test.ts', 'green')
      break
  }
}

// 运行主函数
main()
