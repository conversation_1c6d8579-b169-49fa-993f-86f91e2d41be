/**
 * 测试工具函数
 * 提供常用的测试数据和辅助函数
 */

import type { 
  SearchFieldConfig, 
  ExhibitionOperate, 
  TransformMetadataItem,
  TableDataItem,
  StatisticItem
} from '@/views/Exhibition/types'

/**
 * 创建模拟的搜索字段配置
 */
export function createMockSearchFieldConfig(overrides: Partial<SearchFieldConfig> = {}): SearchFieldConfig {
  return {
    id: '1',
    manageId: 'test-manage-id',
    fieldCodes: 'testField',
    fieldIds: 'field-id-1',
    fieldNames: '测试字段',
    queryType: 0,
    defaultValue: '',
    hint: '请输入测试字段',
    sort: 1,
    ...overrides
  }
}

/**
 * 创建模拟的操作配置
 */
export function createMockExhibitionOperate(overrides: Partial<ExhibitionOperate> = {}): ExhibitionOperate {
  return {
    id: '1',
    manageId: 'test-manage-id',
    operateName: '新增',
    operateType: 0,
    showFlag: 1,
    sort: 1,
    type: 'create',
    ...overrides
  }
}

/**
 * 创建模拟的表格数据
 */
export function createMockTableDataItem(overrides: Partial<TableDataItem> = {}): TableDataItem {
  return {
    id: '1',
    name: '测试数据',
    status: 1,
    createTime: '2024-01-01 10:00:00',
    ...overrides
  }
}

/**
 * 创建模拟的统计数据
 */
export function createMockStatisticItem(overrides: Partial<StatisticItem> = {}): StatisticItem {
  return {
    id: '1',
    uuid: 'stat-uuid-1',
    name: '总数统计',
    value: 100,
    ...overrides
  }
}

/**
 * 创建模拟的转换元数据
 */
export function createMockTransformMetadataItem(overrides: Partial<TransformMetadataItem> = {}): TransformMetadataItem {
  return {
    fieldCodes: 'testField',
    queryType: 0,
    ...overrides
  }
}

/**
 * 等待 Vue 组件更新
 */
export async function flushPromises(): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, 0))
}

/**
 * 模拟异步操作
 */
export function mockAsyncOperation<T>(data: T, delay = 100): Promise<T> {
  return new Promise(resolve => {
    setTimeout(() => resolve(data), delay)
  })
}

/**
 * 创建模拟的 API 响应
 */
export function createMockApiResponse<T>(data: T, success = true) {
  return {
    code: success ? 0 : 500,
    data: success ? data : null,
    message: success ? 'success' : 'error'
  }
}

/**
 * 模拟 Element Plus 消息提示
 */
export const mockElMessage = {
  success: vi.fn(),
  error: vi.fn(),
  warning: vi.fn(),
  info: vi.fn()
}

/**
 * 模拟 Element Plus 确认框
 */
export const mockElMessageBox = {
  confirm: vi.fn().mockResolvedValue('confirm'),
  alert: vi.fn().mockResolvedValue('confirm'),
  prompt: vi.fn().mockResolvedValue({ value: 'test' })
}
