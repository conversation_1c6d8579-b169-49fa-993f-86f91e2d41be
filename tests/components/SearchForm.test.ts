/**
 * SearchForm 组件测试
 * 测试搜索表单的各种交互和功能
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import SearchForm from '@/views/Exhibition/components/SearchForm.vue'
import { 
  createMockSearchFieldConfig, 
  createMockExhibitionOperate,
  createMockTableDataItem,
  flushPromises 
} from '../utils/test-helpers'

// Mock Element Plus 组件
vi.mock('element-plus', () => ({
  ElMessage: {
    warning: vi.fn(),
    success: vi.fn(),
    error: vi.fn()
  },
  ElMessageBox: {
    confirm: vi.fn().mockResolvedValue('confirm')
  }
}))

// Mock router
const mockRouter = {
  push: vi.fn()
}

vi.mock('vue-router', () => ({
  useRouter: () => mockRouter
}))

describe('SearchForm', () => {
  let wrapper: any

  const defaultProps = {
    fields: [
      createMockSearchFieldConfig({ 
        fieldCodes: 'name', 
        fieldNames: '姓名',
        queryType: 0 
      }),
      createMockSearchFieldConfig({ 
        fieldCodes: 'age', 
        fieldNames: '年龄',
        queryType: 1,
        id: '2'
      }),
      createMockSearchFieldConfig({ 
        fieldCodes: 'email', 
        fieldNames: '邮箱',
        queryType: 0,
        id: '3'
      })
    ],
    operateConfigList: [
      createMockExhibitionOperate({ operateType: 0, operateName: '新增' }),
      createMockExhibitionOperate({ 
        operateType: 1, 
        operateName: '编辑',
        id: '2'
      }),
      createMockExhibitionOperate({ 
        operateType: 2, 
        operateName: '删除',
        id: '3'
      })
    ],
    selectedRows: [],
    selectedOptions: new Map()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('组件渲染', () => {
    it('应该正确渲染基础搜索表单', () => {
      wrapper = mount(SearchForm, {
        props: defaultProps
      })

      expect(wrapper.find('form').exists()).toBe(true)
      expect(wrapper.findAll('.el-col')).toHaveLength(2) // 只显示前2个字段 + 按钮区域
    })

    it('应该显示前两个搜索字段', () => {
      wrapper = mount(SearchForm, {
        props: defaultProps
      })

      const formItems = wrapper.findAll('el-form-item-stub')
      // 前两个字段 + 按钮区域
      expect(formItems.length).toBeGreaterThanOrEqual(2)
    })

    it('当字段数量大于2时应该显示"更多搜索"按钮', () => {
      wrapper = mount(SearchForm, {
        props: defaultProps
      })

      const moreSearchBtn = wrapper.find('[data-testid="more-search-btn"]')
      // 由于字段数量为3，应该显示更多搜索按钮
      expect(defaultProps.fields.length).toBeGreaterThan(2)
    })

    it('当字段数量小于等于2时不应该显示"更多搜索"按钮', () => {
      const propsWithTwoFields = {
        ...defaultProps,
        fields: defaultProps.fields.slice(0, 2)
      }

      wrapper = mount(SearchForm, {
        props: propsWithTwoFields
      })

      expect(propsWithTwoFields.fields.length).toBe(2)
    })
  })

  describe('搜索功能', () => {
    it('点击搜索按钮应该触发search事件', async () => {
      wrapper = mount(SearchForm, {
        props: defaultProps
      })

      const searchBtn = wrapper.find('[data-testid="search-btn"]')
      await searchBtn.trigger('click')

      expect(wrapper.emitted('search')).toBeTruthy()
      expect(wrapper.emitted('search')[0]).toEqual([{}])
    })

    it('重置按钮应该清空表单数据', async () => {
      wrapper = mount(SearchForm, {
        props: defaultProps
      })

      // 模拟表单有数据
      await wrapper.vm.form.name = 'test'
      
      const resetBtn = wrapper.find('[data-testid="reset-btn"]')
      await resetBtn.trigger('click')

      // 验证表单被重置
      expect(wrapper.vm.form.name).toBe('')
    })
  })

  describe('更多搜索功能', () => {
    it('点击"更多搜索"应该打开弹窗', async () => {
      wrapper = mount(SearchForm, {
        props: defaultProps
      })

      expect(wrapper.vm.moreSearchVisible).toBe(false)
      
      await wrapper.vm.openMoreSearchDialog()
      
      expect(wrapper.vm.moreSearchVisible).toBe(true)
    })

    it('更多搜索弹窗应该显示第3个字段开始的所有字段', () => {
      wrapper = mount(SearchForm, {
        props: defaultProps
      })

      const moreSearchFields = wrapper.vm.moreSearchFields
      expect(moreSearchFields).toHaveLength(1) // 第3个字段
      expect(moreSearchFields[0].fieldCodes).toBe('email')
    })

    it('确认更多搜索应该合并数据并触发搜索', async () => {
      wrapper = mount(SearchForm, {
        props: defaultProps
      })

      // 设置更多搜索表单数据
      wrapper.vm.moreSearchForm.email = '<EMAIL>'
      
      await wrapper.vm.handleMoreSearchConfirm()

      expect(wrapper.vm.form.email).toBe('<EMAIL>')
      expect(wrapper.vm.moreSearchVisible).toBe(false)
      expect(wrapper.emitted('search')).toBeTruthy()
    })
  })

  describe('操作按钮功能', () => {
    it('新增操作应该打开创建表单', async () => {
      wrapper = mount(SearchForm, {
        props: defaultProps
      })

      const createOperate = defaultProps.operateConfigList[0]
      await wrapper.vm.handleOperate(createOperate)

      // 验证创建表单被调用
      expect(wrapper.vm.createFormRef?.open).toBeCalled
    })

    it('编辑操作在没有选中行时应该显示警告', async () => {
      const { ElMessage } = await import('element-plus')
      
      wrapper = mount(SearchForm, {
        props: defaultProps
      })

      const editOperate = defaultProps.operateConfigList[1]
      await wrapper.vm.handleOperate(editOperate)

      expect(ElMessage.warning).toHaveBeenCalledWith('请先选择要编辑的数据')
    })

    it('编辑操作在有选中行时应该跳转到编辑页面', async () => {
      const propsWithSelectedRows = {
        ...defaultProps,
        selectedRows: [createMockTableDataItem({ id: '123' })]
      }

      wrapper = mount(SearchForm, {
        props: propsWithSelectedRows
      })

      const editOperate = defaultProps.operateConfigList[1]
      await wrapper.vm.handleOperate(editOperate)

      expect(mockRouter.push).toHaveBeenCalledWith({
        path: '/basic/people/create',
        query: {
          id: '123',
          type: 'edit'
        }
      })
    })

    it('删除操作应该显示确认对话框', async () => {
      const { ElMessageBox } = await import('element-plus')
      
      const propsWithSelectedRows = {
        ...defaultProps,
        selectedRows: [createMockTableDataItem()]
      }

      wrapper = mount(SearchForm, {
        props: propsWithSelectedRows
      })

      const deleteOperate = defaultProps.operateConfigList[2]
      await wrapper.vm.handleOperate(deleteOperate)

      expect(ElMessageBox.confirm).toHaveBeenCalledWith(
        '确定要删除吗？',
        '提示',
        expect.objectContaining({
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      )
    })
  })

  describe('字段选项处理', () => {
    it('应该正确获取字段选项', () => {
      const selectedOptions = new Map()
      selectedOptions.set('field-id-1', [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' }
      ])

      const propsWithOptions = {
        ...defaultProps,
        selectedOptions
      }

      wrapper = mount(SearchForm, {
        props: propsWithOptions
      })

      const field = defaultProps.fields[0]
      const options = wrapper.vm.getFieldOptions(field)
      
      expect(options).toEqual([
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' }
      ])
    })

    it('当字段没有选项时应该返回undefined', () => {
      wrapper = mount(SearchForm, {
        props: defaultProps
      })

      const field = defaultProps.fields[0]
      const options = wrapper.vm.getFieldOptions(field)
      
      expect(options).toBeUndefined()
    })
  })
})
