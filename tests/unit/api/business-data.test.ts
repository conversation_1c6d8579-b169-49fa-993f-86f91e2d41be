/**
 * Business Data API 测试
 * 测试业务数据相关的 API 调用
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import type { BusinessDataListRequest, SearchCondition } from '@/views/Exhibition/types'

// Mock axios
const mockRequest = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn()
}

vi.mock('@/config/axios', () => ({
  default: mockRequest
}))

// 模拟 API 函数（基于项目结构）
const getBusinessDataList = async (params: BusinessDataListRequest) => {
  return await mockRequest.post({ url: '/data/business-data/list', data: params })
}

const deleteBusinessData = async (ids: string[]) => {
  return await mockRequest.delete({ url: '/data/business-data/delete', data: { ids } })
}

const createBusinessData = async (data: any) => {
  return await mockRequest.post({ url: '/data/business-data/create', data })
}

const updateBusinessData = async (data: any) => {
  return await mockRequest.put({ url: '/data/business-data/update', data })
}

describe('Business Data API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getBusinessDataList', () => {
    it('应该正确调用业务数据列表接口', async () => {
      const mockResponse = {
        code: 0,
        data: {
          list: [
            { id: '1', name: '测试数据1' },
            { id: '2', name: '测试数据2' }
          ],
          total: 2
        }
      }

      mockRequest.post.mockResolvedValue(mockResponse)

      const params: BusinessDataListRequest = {
        manageId: 'test-manage-id',
        pageNo: 1,
        pageSize: 10,
        searchList: []
      }

      const result = await getBusinessDataList(params)

      expect(mockRequest.post).toHaveBeenCalledWith({
        url: '/data/business-data/list',
        data: params
      })
      expect(result).toEqual(mockResponse)
    })

    it('应该正确处理搜索条件', async () => {
      const mockResponse = {
        code: 0,
        data: { list: [], total: 0 }
      }

      mockRequest.post.mockResolvedValue(mockResponse)

      const searchConditions: SearchCondition[] = [
        { code: 'name', type: 0, value: 'John' },
        { code: 'age', type: 1, value: '25' }
      ]

      const params: BusinessDataListRequest = {
        manageId: 'test-manage-id',
        pageNo: 1,
        pageSize: 10,
        searchList: searchConditions
      }

      await getBusinessDataList(params)

      expect(mockRequest.post).toHaveBeenCalledWith({
        url: '/data/business-data/list',
        data: expect.objectContaining({
          searchList: searchConditions
        })
      })
    })

    it('应该正确处理分页参数', async () => {
      const mockResponse = {
        code: 0,
        data: { list: [], total: 0 }
      }

      mockRequest.post.mockResolvedValue(mockResponse)

      const params: BusinessDataListRequest = {
        manageId: 'test-manage-id',
        pageNo: 2,
        pageSize: 20,
        searchList: []
      }

      await getBusinessDataList(params)

      expect(mockRequest.post).toHaveBeenCalledWith({
        url: '/data/business-data/list',
        data: expect.objectContaining({
          pageNo: 2,
          pageSize: 20
        })
      })
    })

    it('应该正确处理 API 错误', async () => {
      const mockError = new Error('Network Error')
      mockRequest.post.mockRejectedValue(mockError)

      const params: BusinessDataListRequest = {
        manageId: 'test-manage-id',
        pageNo: 1,
        pageSize: 10,
        searchList: []
      }

      await expect(getBusinessDataList(params)).rejects.toThrow('Network Error')
    })
  })

  describe('deleteBusinessData', () => {
    it('应该正确调用删除接口', async () => {
      const mockResponse = {
        code: 0,
        data: { success: true }
      }

      mockRequest.delete.mockResolvedValue(mockResponse)

      const ids = ['1', '2', '3']
      const result = await deleteBusinessData(ids)

      expect(mockRequest.delete).toHaveBeenCalledWith({
        url: '/data/business-data/delete',
        data: { ids }
      })
      expect(result).toEqual(mockResponse)
    })

    it('应该正确处理单个ID删除', async () => {
      const mockResponse = {
        code: 0,
        data: { success: true }
      }

      mockRequest.delete.mockResolvedValue(mockResponse)

      const ids = ['1']
      await deleteBusinessData(ids)

      expect(mockRequest.delete).toHaveBeenCalledWith({
        url: '/data/business-data/delete',
        data: { ids: ['1'] }
      })
    })

    it('应该正确处理空ID数组', async () => {
      const mockResponse = {
        code: 0,
        data: { success: true }
      }

      mockRequest.delete.mockResolvedValue(mockResponse)

      const ids: string[] = []
      await deleteBusinessData(ids)

      expect(mockRequest.delete).toHaveBeenCalledWith({
        url: '/data/business-data/delete',
        data: { ids: [] }
      })
    })
  })

  describe('createBusinessData', () => {
    it('应该正确调用创建接口', async () => {
      const mockResponse = {
        code: 0,
        data: { id: '123', success: true }
      }

      mockRequest.post.mockResolvedValue(mockResponse)

      const createData = {
        name: '新建数据',
        type: 'test',
        manageId: 'test-manage-id'
      }

      const result = await createBusinessData(createData)

      expect(mockRequest.post).toHaveBeenCalledWith({
        url: '/data/business-data/create',
        data: createData
      })
      expect(result).toEqual(mockResponse)
    })

    it('应该正确处理复杂的创建数据', async () => {
      const mockResponse = {
        code: 0,
        data: { id: '123', success: true }
      }

      mockRequest.post.mockResolvedValue(mockResponse)

      const createData = {
        name: '复杂数据',
        businessJson: {
          field1: 'value1',
          field2: ['option1', 'option2'],
          field3: {
            nested: 'value'
          }
        },
        manageId: 'test-manage-id'
      }

      await createBusinessData(createData)

      expect(mockRequest.post).toHaveBeenCalledWith({
        url: '/data/business-data/create',
        data: createData
      })
    })
  })

  describe('updateBusinessData', () => {
    it('应该正确调用更新接口', async () => {
      const mockResponse = {
        code: 0,
        data: { success: true }
      }

      mockRequest.put.mockResolvedValue(mockResponse)

      const updateData = {
        id: '123',
        name: '更新后的数据',
        manageId: 'test-manage-id'
      }

      const result = await updateBusinessData(updateData)

      expect(mockRequest.put).toHaveBeenCalledWith({
        url: '/data/business-data/update',
        data: updateData
      })
      expect(result).toEqual(mockResponse)
    })

    it('应该正确处理部分字段更新', async () => {
      const mockResponse = {
        code: 0,
        data: { success: true }
      }

      mockRequest.put.mockResolvedValue(mockResponse)

      const updateData = {
        id: '123',
        name: '仅更新名称'
      }

      await updateBusinessData(updateData)

      expect(mockRequest.put).toHaveBeenCalledWith({
        url: '/data/business-data/update',
        data: updateData
      })
    })
  })

  describe('API 错误处理', () => {
    it('应该正确处理网络错误', async () => {
      const networkError = new Error('Network Error')
      mockRequest.post.mockRejectedValue(networkError)

      const params: BusinessDataListRequest = {
        manageId: 'test-manage-id',
        pageNo: 1,
        pageSize: 10,
        searchList: []
      }

      await expect(getBusinessDataList(params)).rejects.toThrow('Network Error')
    })

    it('应该正确处理服务器错误响应', async () => {
      const serverError = {
        code: 500,
        message: 'Internal Server Error'
      }
      mockRequest.post.mockResolvedValue(serverError)

      const params: BusinessDataListRequest = {
        manageId: 'test-manage-id',
        pageNo: 1,
        pageSize: 10,
        searchList: []
      }

      const result = await getBusinessDataList(params)
      expect(result.code).toBe(500)
    })

    it('应该正确处理超时错误', async () => {
      const timeoutError = new Error('Request timeout')
      mockRequest.post.mockRejectedValue(timeoutError)

      const params: BusinessDataListRequest = {
        manageId: 'test-manage-id',
        pageNo: 1,
        pageSize: 10,
        searchList: []
      }

      await expect(getBusinessDataList(params)).rejects.toThrow('Request timeout')
    })
  })
})
