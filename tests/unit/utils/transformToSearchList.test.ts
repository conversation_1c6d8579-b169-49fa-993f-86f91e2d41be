/**
 * transformToSearchList 函数单元测试
 * 测试数据转换逻辑的各种场景
 */

import { describe, it, expect } from 'vitest'
import type { TransformMetadataItem, SearchItem } from '@/views/Exhibition/types'

// 模拟 transformToSearchList 函数（从 Exhibition/index.vue 提取）
const transformToSearchList = (
  filterData: Record<string, string>,
  metadataList: TransformMetadataItem[],
  separator = ','
): SearchItem[] =>
  metadataList.flatMap(({ fieldCodes = '', queryType }) => {
    const values = (filterData[fieldCodes]?.trim() || '')
      .split(separator)
      .map(v => v.trim())
      .filter(Boolean);

    return fieldCodes
      .split(separator)
      .map((code, i) => ({
        code: code.trim(),
        type: queryType,
        value: values[i] ?? values[values.length - 1] ?? ''
      }));
  });

describe('transformToSearchList', () => {
  describe('基础功能测试', () => {
    it('应该正确转换单个字段的数据', () => {
      const filterData = { 'name': '<PERSON>' }
      const metadataList: TransformMetadataItem[] = [
        { fieldCodes: 'name', queryType: 0 }
      ]

      const result = transformToSearchList(filterData, metadataList)

      expect(result).toEqual([
        { code: 'name', type: 0, value: 'John' }
      ])
    })

    it('应该正确处理多个字段的数据', () => {
      const filterData = { 
        'name': 'John',
        'age': '25'
      }
      const metadataList: TransformMetadataItem[] = [
        { fieldCodes: 'name', queryType: 0 },
        { fieldCodes: 'age', queryType: 1 }
      ]

      const result = transformToSearchList(filterData, metadataList)

      expect(result).toEqual([
        { code: 'name', type: 0, value: 'John' },
        { code: 'age', type: 1, value: '25' }
      ])
    })

    it('应该正确处理逗号分隔的字段编码', () => {
      const filterData = { 'name,email': 'John,<EMAIL>' }
      const metadataList: TransformMetadataItem[] = [
        { fieldCodes: 'name,email', queryType: 0 }
      ]

      const result = transformToSearchList(filterData, metadataList)

      expect(result).toEqual([
        { code: 'name', type: 0, value: 'John' },
        { code: 'email', type: 0, value: '<EMAIL>' }
      ])
    })
  })

  describe('边界情况测试', () => {
    it('应该处理空的过滤数据', () => {
      const filterData = {}
      const metadataList: TransformMetadataItem[] = [
        { fieldCodes: 'name', queryType: 0 }
      ]

      const result = transformToSearchList(filterData, metadataList)

      expect(result).toEqual([
        { code: 'name', type: 0, value: '' }
      ])
    })

    it('应该处理空的元数据列表', () => {
      const filterData = { 'name': 'John' }
      const metadataList: TransformMetadataItem[] = []

      const result = transformToSearchList(filterData, metadataList)

      expect(result).toEqual([])
    })

    it('应该处理空字符串值', () => {
      const filterData = { 'name': '' }
      const metadataList: TransformMetadataItem[] = [
        { fieldCodes: 'name', queryType: 0 }
      ]

      const result = transformToSearchList(filterData, metadataList)

      expect(result).toEqual([
        { code: 'name', type: 0, value: '' }
      ])
    })

    it('应该处理只有空格的值', () => {
      const filterData = { 'name': '   ' }
      const metadataList: TransformMetadataItem[] = [
        { fieldCodes: 'name', queryType: 0 }
      ]

      const result = transformToSearchList(filterData, metadataList)

      expect(result).toEqual([
        { code: 'name', type: 0, value: '' }
      ])
    })
  })

  describe('值映射逻辑测试', () => {
    it('当字段数量多于值数量时，应该重复使用最后一个值', () => {
      const filterData = { 'name,email,phone': 'John,<EMAIL>' }
      const metadataList: TransformMetadataItem[] = [
        { fieldCodes: 'name,email,phone', queryType: 0 }
      ]

      const result = transformToSearchList(filterData, metadataList)

      expect(result).toEqual([
        { code: 'name', type: 0, value: 'John' },
        { code: 'email', type: 0, value: '<EMAIL>' },
        { code: 'phone', type: 0, value: '<EMAIL>' }
      ])
    })

    it('当值数量多于字段数量时，应该忽略多余的值', () => {
      const filterData = { 'name,email': 'John,<EMAIL>,extra' }
      const metadataList: TransformMetadataItem[] = [
        { fieldCodes: 'name,email', queryType: 0 }
      ]

      const result = transformToSearchList(filterData, metadataList)

      expect(result).toEqual([
        { code: 'name', type: 0, value: 'John' },
        { code: 'email', type: 0, value: '<EMAIL>' }
      ])
    })
  })

  describe('自定义分隔符测试', () => {
    it('应该支持自定义分隔符', () => {
      const filterData = { 'name|email': 'John|<EMAIL>' }
      const metadataList: TransformMetadataItem[] = [
        { fieldCodes: 'name|email', queryType: 0 }
      ]

      const result = transformToSearchList(filterData, metadataList, '|')

      expect(result).toEqual([
        { code: 'name', type: 0, value: 'John' },
        { code: 'email', type: 0, value: '<EMAIL>' }
      ])
    })
  })

  describe('查询类型测试', () => {
    it('应该正确保持不同的查询类型', () => {
      const filterData = { 
        'textField': 'text',
        'selectField': 'option1',
        'dateField': '2024-01-01'
      }
      const metadataList: TransformMetadataItem[] = [
        { fieldCodes: 'textField', queryType: 0 },    // 搜索
        { fieldCodes: 'selectField', queryType: 1 },  // 单选
        { fieldCodes: 'dateField', queryType: 3 }     // 日期区间
      ]

      const result = transformToSearchList(filterData, metadataList)

      expect(result).toEqual([
        { code: 'textField', type: 0, value: 'text' },
        { code: 'selectField', type: 1, value: 'option1' },
        { code: 'dateField', type: 3, value: '2024-01-01' }
      ])
    })
  })
})
