/**
 * Exhibition 页面集成测试
 * 测试整个页面的数据流和组件交互
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import Exhibition from '@/views/Exhibition/index.vue'
import { 
  createMockSearchFieldConfig,
  createMockExhibitionOperate,
  createMockTableDataItem,
  createMockStatisticItem,
  mockAsyncOperation,
  createMockApiResponse
} from '../utils/test-helpers'

// Mock APIs
const mockBusinessDataApi = {
  getBusinessDataList: vi.fn(),
  deleteBusinessData: vi.fn()
}

const mockFieldConfApi = {
  getFieldConfigListByManageId: vi.fn()
}

const mockQueryConfApi = {
  getQueryConfList: vi.fn()
}

const mockCountConfApi = {
  getCountConfList: vi.fn()
}

const mockOperateConfApi = {
  getOperateConfList: vi.fn()
}

const mockDictDataApi = {
  getDictDataList: vi.fn()
}

// Mock API modules
vi.mock('@/api/system/business-data', () => mockBusinessDataApi)
vi.mock('@/api/system/data/field-conf', () => mockFieldConfApi)
vi.mock('@/api/system/data/query-conf', () => mockQueryConfApi)
vi.mock('@/api/system/data/count-conf', () => mockCountConfApi)
vi.mock('@/api/system/data/operate-conf', () => mockOperateConfApi)
vi.mock('@/api/system/dict/dict.data', () => mockDictDataApi)

// Mock router
const mockRouter = {
  push: vi.fn()
}

vi.mock('vue-router', () => ({
  useRouter: () => mockRouter,
  useRoute: () => ({
    query: { manageId: 'test-manage-id' }
  })
}))

// Mock utils
vi.mock('@/utils', () => ({
  formatToDate: vi.fn((date, format) => date)
}))

describe('Exhibition 页面集成测试', () => {
  let wrapper: any

  const mockData = {
    statisticItems: [
      createMockStatisticItem({ name: '总数', value: 100 }),
      createMockStatisticItem({ name: '今日新增', value: 10, id: '2' })
    ],
    queryConfig: [
      createMockSearchFieldConfig({ fieldCodes: 'name', fieldNames: '姓名' }),
      createMockSearchFieldConfig({ fieldCodes: 'age', fieldNames: '年龄', id: '2' }),
      createMockSearchFieldConfig({ fieldCodes: 'email', fieldNames: '邮箱', id: '3' })
    ],
    operateConfig: [
      createMockExhibitionOperate({ operateType: 0, operateName: '新增' }),
      createMockExhibitionOperate({ operateType: 1, operateName: '编辑', id: '2' })
    ],
    tableData: [
      createMockTableDataItem({ id: '1', name: '张三' }),
      createMockTableDataItem({ id: '2', name: '李四' })
    ],
    columns: [
      { code: 'name', name: '姓名' },
      { code: 'age', name: '年龄' }
    ]
  }

  beforeEach(() => {
    vi.clearAllMocks()
    
    // 设置 API mock 返回值
    mockCountConfApi.getCountConfList.mockResolvedValue(
      createMockApiResponse(mockData.statisticItems)
    )
    mockQueryConfApi.getQueryConfList.mockResolvedValue(
      createMockApiResponse(mockData.queryConfig)
    )
    mockOperateConfApi.getOperateConfList.mockResolvedValue(
      createMockApiResponse(mockData.operateConfig)
    )
    mockFieldConfApi.getFieldConfigListByManageId.mockResolvedValue(
      createMockApiResponse(mockData.columns)
    )
    mockBusinessDataApi.getBusinessDataList.mockResolvedValue(
      createMockApiResponse({
        list: mockData.tableData,
        total: 2
      })
    )
    mockDictDataApi.getDictDataList.mockResolvedValue(
      createMockApiResponse([])
    )
  })

  describe('页面初始化', () => {
    it('应该正确加载页面数据', async () => {
      wrapper = mount(Exhibition)
      
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 100))

      // 验证 API 调用
      expect(mockCountConfApi.getCountConfList).toHaveBeenCalled()
      expect(mockQueryConfApi.getQueryConfList).toHaveBeenCalled()
      expect(mockOperateConfApi.getOperateConfList).toHaveBeenCalled()
      expect(mockFieldConfApi.getFieldConfigListByManageId).toHaveBeenCalled()
      expect(mockBusinessDataApi.getBusinessDataList).toHaveBeenCalled()
    })

    it('应该正确设置响应式数据', async () => {
      wrapper = mount(Exhibition)
      
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 100))

      expect(wrapper.vm.countConfigDate).toHaveLength(2)
      expect(wrapper.vm.queryConfig).toHaveLength(3)
      expect(wrapper.vm.operateConfigList).toHaveLength(2)
      expect(wrapper.vm.tableData).toHaveLength(2)
      expect(wrapper.vm.loading).toBe(false)
    })
  })

  describe('搜索功能集成测试', () => {
    it('应该正确处理搜索事件', async () => {
      wrapper = mount(Exhibition)
      
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 100))

      const searchParams = {
        name: 'John',
        age: '25'
      }

      await wrapper.vm.onSearch(searchParams)

      // 验证搜索参数被正确转换
      expect(wrapper.vm.queryParams.searchList).toBeDefined()
      expect(mockBusinessDataApi.getBusinessDataList).toHaveBeenCalledTimes(2) // 初始化 + 搜索
    })

    it('应该正确过滤空值', async () => {
      wrapper = mount(Exhibition)
      
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 100))

      const searchParams = {
        name: 'John',
        age: '', // 空值应该被过滤
        email: '<EMAIL>'
      }

      await wrapper.vm.onSearch(searchParams)

      const searchList = wrapper.vm.queryParams.searchList
      // 验证空值被过滤掉
      expect(searchList.every((item: any) => item.value)).toBe(true)
    })

    it('应该正确格式化日期类型字段', async () => {
      const { formatToDate } = await import('@/utils')
      
      wrapper = mount(Exhibition)
      
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 100))

      // 模拟日期类型字段
      wrapper.vm.queryConfig = [
        createMockSearchFieldConfig({ 
          fieldCodes: 'createDate', 
          queryType: 3 // 日期区间类型
        })
      ]

      const searchParams = {
        createDate: '2024-01-01'
      }

      await wrapper.vm.onSearch(searchParams)

      expect(formatToDate).toHaveBeenCalledWith('2024-01-01', 'YYYY-MM-DD')
    })
  })

  describe('表格操作集成测试', () => {
    it('应该正确处理表格行选择', async () => {
      wrapper = mount(Exhibition)
      
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 100))

      const selectedRows = [mockData.tableData[0]]
      
      await wrapper.vm.handleSelectionChange(selectedRows)

      expect(wrapper.vm.selectedRows).toEqual(selectedRows)
    })

    it('应该正确处理查看详情操作', async () => {
      wrapper = mount(Exhibition)
      
      await nextTick()

      const row = mockData.tableData[0]
      
      await wrapper.vm.onAction('view', row)

      expect(mockRouter.push).toHaveBeenCalledWith({
        path: '/basic/people/detail',
        query: { id: row.id }
      })
    })
  })

  describe('删除功能集成测试', () => {
    it('应该正确处理删除操作', async () => {
      mockBusinessDataApi.deleteBusinessData.mockResolvedValue(
        createMockApiResponse({ success: true })
      )

      wrapper = mount(Exhibition)
      
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 100))

      const selectedRows = [mockData.tableData[0]]
      
      await wrapper.vm.onDelete(selectedRows)

      expect(mockBusinessDataApi.deleteBusinessData).toHaveBeenCalledWith(
        selectedRows.map(row => row.id)
      )
      // 删除后应该重新加载列表
      expect(mockBusinessDataApi.getBusinessDataList).toHaveBeenCalledTimes(2)
    })
  })

  describe('分页功能集成测试', () => {
    it('应该正确处理分页变化', async () => {
      wrapper = mount(Exhibition)
      
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 100))

      // 模拟分页变化
      wrapper.vm.queryParams.pageNo = 2
      wrapper.vm.queryParams.pageSize = 20

      await wrapper.vm.getList()

      expect(mockBusinessDataApi.getBusinessDataList).toHaveBeenCalledWith(
        expect.objectContaining({
          pageNo: 2,
          pageSize: 20
        })
      )
    })
  })

  describe('错误处理集成测试', () => {
    it('应该正确处理 API 错误', async () => {
      mockBusinessDataApi.getBusinessDataList.mockRejectedValue(
        new Error('API Error')
      )

      wrapper = mount(Exhibition)
      
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 100))

      // 验证错误被正确处理，loading 状态被重置
      expect(wrapper.vm.loading).toBe(false)
    })
  })
})
