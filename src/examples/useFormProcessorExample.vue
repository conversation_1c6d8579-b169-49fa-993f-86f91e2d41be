<template>
  <div class="form-example">
    <h2>表单处理 Hook 使用示例</h2>
    
    <!-- 表单 -->
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
      <template v-for="fieldGroup in fieldGroups" :key="fieldGroup.id">
        <el-row :gutter="20">
          <el-col
            v-for="field in fieldGroup.fields"
            :key="field.id"
            :span="12"
          >
            <el-form-item :label="field.name" :prop="field.code" :required="field.required">
              <SimpleFormField
                :field="field"
                v-model="formData[field.code]"
                :field-options="getFieldOptions(field)"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 标签选择 -->
    <div v-if="labelType" class="label-info">
      <h3>当前标签类型：{{ getLabelTypeText() }}</h3>
      <p>标签字段：{{ labelConfigData.map(f => f.name).join(', ') }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useFormProcessor } from '@/hooks/useFormProcessor'
import { useLabelConfig } from '@/hooks/useLabelConfig'
import FormProcessor from '@/utils/formProcessor'
import SimpleFormField from '@/components/FormField/SimpleFormField.vue'

// 使用表单处理 hook
const {
  formData,
  formRules,
  fieldOptionsMap,
  FIELD_TYPES,
  processFieldGroups,
  initializeFormData,
  generateFormValidationRules,
  processSubmitData,
  resetFormData
} = useFormProcessor()

// 使用标签配置 hook
const {
  labelType,
  labelConfigData,
  initializeLabelType,
  getLabelTypeText,
  resetLabelConfig
} = useLabelConfig()

const formRef = ref()
const fieldGroups = ref<any[]>([])

// 模拟字段配置数据
const mockFieldConfigs = [
  {
    id: '1',
    code: 'name',
    name: '姓名',
    fieldType: FIELD_TYPES.TEXT,
    required: true,
    fieldConfExtDOList: []
  },
  {
    id: '2', 
    code: 'age',
    name: '年龄',
    fieldType: FIELD_TYPES.NUMBER,
    required: false,
    fieldConfExtDOList: []
  },
  {
    id: '3',
    code: 'gender',
    name: '性别',
    fieldType: FIELD_TYPES.RADIO,
    required: true,
    fieldConfExtDOList: []
  },
  {
    id: '4',
    code: 'hobbies',
    name: '爱好',
    fieldType: FIELD_TYPES.CHECKBOX,
    required: false,
    fieldConfExtDOList: []
  },
  {
    id: '5',
    code: 'dateRange',
    name: '日期范围',
    fieldType: FIELD_TYPES.DATE_RANGE,
    required: false,
    fieldConfExtDOList: [
      { name: 'code2', value: 'endDate' }
    ]
  }
]

// 模拟字段组数据
const mockFieldGroups = [
  {
    id: 'group1',
    fields: mockFieldConfigs
  }
]

// 获取字段选项（模拟）
const fetchFieldOptions = async (field: any) => {
  // 模拟异步获取选项数据
  const mockOptions = {
    gender: [
      { label: '男', value: '1' },
      { label: '女', value: '2' }
    ],
    hobbies: [
      { label: '读书', value: 'reading' },
      { label: '运动', value: 'sports' },
      { label: '音乐', value: 'music' },
      { label: '旅行', value: 'travel' }
    ]
  }
  
  if (mockOptions[field.code]) {
    fieldOptionsMap.value.set(field.code, mockOptions[field.code])
  }
}

// 获取字段选项
const getFieldOptions = (field: any): any[] => {
  if (field.fieldType === FIELD_TYPES.RADIO || field.fieldType === FIELD_TYPES.CHECKBOX) {
    return fieldOptionsMap.value.get(field.code) || []
  }
  return []
}

// 处理提交
const handleSubmit = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (valid) {
      // 使用 hook 处理提交数据
      const submitData = processSubmitData(fieldGroups.value)
      console.log('提交数据：', submitData)
      ElMessage.success('提交成功')
    }
  } catch (error) {
    console.error('表单验证失败：', error)
    ElMessage.error('表单验证失败')
  }
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
  resetFormData()
}

// 初始化
const init = async () => {
  try {
    // 1. 处理字段组配置
    const processedGroups = processFieldGroups(mockFieldGroups, [], fetchFieldOptions)
    fieldGroups.value = processedGroups

    // 2. 初始化表单数据
    const mockStoreData = { name: '张三', age: 25 }
    const mockBusinessData = { gender: '1' }
    initializeFormData(processedGroups, mockStoreData, mockBusinessData)

    // 3. 生成验证规则
    generateFormValidationRules(processedGroups)

    // 4. 初始化标签类型（如果有相关字段）
    initializeLabelType(mockFieldConfigs)

    console.log('初始化完成')
    console.log('表单数据：', formData.value)
    console.log('验证规则：', formRules)
    console.log('标签类型：', labelType.value)
  } catch (error) {
    console.error('初始化失败：', error)
    ElMessage.error('初始化失败')
  }
}

// 演示工具类的使用
const demonstrateUtils = () => {
  console.log('=== FormProcessor 工具类演示 ===')
  
  // 快速初始化表单数据
  const quickFormData = FormProcessor.initFormData(
    mockFieldGroups,
    { name: '李四' },
    { age: 30 }
  )
  console.log('快速初始化的表单数据：', quickFormData)
  
  // 快速处理提交数据
  const quickSubmitData = FormProcessor.processSubmitData(
    mockFieldGroups,
    quickFormData
  )
  console.log('快速处理的提交数据：', quickSubmitData)
  
  // 检查字段是否需要选项
  const needsOptions = FormProcessor.needsOptions(mockFieldConfigs[2]) // gender 字段
  console.log('gender 字段是否需要选项：', needsOptions)
  
  // 获取字段扩展配置
  const extValue = FormProcessor.getFieldExtValue(mockFieldConfigs[4], 'code2')
  console.log('dateRange 字段的 code2 配置：', extValue)
}

onMounted(() => {
  init()
  demonstrateUtils()
})
</script>

<style scoped>
.form-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.label-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.label-info h3 {
  margin: 0 0 10px 0;
  color: #409eff;
}

.label-info p {
  margin: 0;
  color: #666;
}
</style>
