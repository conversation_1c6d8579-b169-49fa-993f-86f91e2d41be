<template>
  <div class="my-component">
    <header>
      <el-button class="ml-5 mr-5" type="primary" plain @click="goBack">返回</el-button>
      <p>{{ pageTitle }}</p>
    </header>
    <main class="border-style">
      <DynamicForm ref="commonFormRef" :schema="commonFormShema" v-model="commonForm" />
      <!-- 拓展信息表单（根据资产来源切换） -->
      <div style="margin-bottom: 20px; border: 2px solid rgb(243 243 243)"> </div>
      <DynamicForm
        v-if="commonForm?.assetsSource && sourceMap[commonForm?.assetsSource] === 'hik'"
        :schema="hikFormShema"
        v-model="hikForm"
        ref="extendFormRef"
      />
      <DynamicForm
        v-else-if="commonForm?.assetsSource && sourceMap[commonForm?.assetsSource] === 'iot'"
        :schema="iotFormShema"
        v-model="iotForm"
        ref="extendFormRef"
      />
      <DynamicForm
        v-else-if="
          commonForm?.assetsSource && sourceMap[commonForm?.assetsSource] === 'ShanghaiElectric'
        "
        :schema="shElectricFormShema"
        v-model="shElectricForm"
        ref="extendFormRef"
      />
      <DynamicForm
        v-else-if="commonForm?.assetsSource && sourceMap[commonForm?.assetsSource] === 'zabbix'"
        :schema="zabbixFormShema"
        v-model="zabbixForm"
        ref="extendFormRef"
      />
      <div class="bottom-btn">
        <el-button type="primary" @click="onSave">保存</el-button>
        <el-button @click="onReset">重置</el-button>
      </div>
    </main>
  </div>
</template>

<script lang="ts" setup>
// ==========================
import { commonFormShema, commonFormData } from '@/components/DynamicForm/src/commonDictForm'
import { hikFormShema, hikData } from '@/components/DynamicForm/src/hikForm'
import { shElectricFormShema, shElectricData } from '@/components/DynamicForm/src/shElectricForm'
import { zabbixFormShema, zabbixData } from '@/components/DynamicForm/src/zabbixForm'
import { iotFormShema, iotData } from '@/components/DynamicForm/src/iotForm'
//===========================
import { reactive, ref, onMounted, computed } from 'vue'
import * as EquipmentApi from '@/api/operations/equipment'
import { getDictList } from '@/api/infra/deviceDict'
import { useRoute, onBeforeRouteLeave } from 'vue-router'
import * as TicketsPushApi from '@/api/operations/ticketsPush'

const route = useRoute()
const isID = reactive({ id: '' }) //是否是编辑id
onMounted(() => {
  // 从路由查询参数中获取 id 值
  isID.id = sessionStorage.getItem('focusDetailId')
  // console.log(isID.id, 'isID.id')
  if (isID.id) {
    getDetail()
    // 获取数据并回填
  }
})
// 使用 beforeRouteLeave 导航守卫
onBeforeRouteLeave((to, from) => {
  // 清除 sessionStorage 中的 focusDetailId
  sessionStorage.removeItem('focusDetailId')
})
const detailList = ref([])
const getDetail = async () => {
  const res = await TicketsPushApi.getDetail(isID)
  detailList.value = res
  // 回填通用表单数据
  commonForm.value = res.deviceOverviewInfo
  if (res.hikInfo != null) {
    hikForm.value = res.hikInfo
  } else if (res.iotInfo != null) {
    iotForm.value = res.iotInfo
  } else if (res.shElectricInfo != null) {
    shElectricForm.value = res.shElectricInfo
  } else if (res.zabbixInfo != null) {
    zabbixForm.value = res.zabbixInfo
  }
}

const sourceMap = ref({})
const getSource = async () => {
  const data = await getDictList('deviceSource')
  // console.log(data, 'getSource')
  data.forEach((d) => {
    sourceMap.value[d.id] = d.label
  })
}
getSource()
// 标头文字
const pageTitle = computed(() => {
  return isID.id ? '重点保障设备列表-编辑' : '重点保障设备列表-新增'
})

const goBack = () => {
  window.history.back()
}
// =======================
// 通用表单
const commonFormRef = ref()
const commonForm = ref({ ...commonFormData })
// 设备来源（assetsSource）表单

const hikForm = ref({ ...hikData })
const iotForm = ref({ ...iotData })
const zabbixForm = ref({ ...zabbixData })
const shElectricForm = ref({ ...shElectricData })

const extendFormRef = ref()
//重置
function onReset() {
  commonFormRef.value?.reset()
  extendFormRef.value?.reset()
}

// 保存按钮
const onSave = async () => {
  // console.log('通用表单:', commonForm.value)
  // console.log('其他表单:', hikForm.value, iotForm.value, zabbixForm.value, shElectricForm.value)

  const params = {
    deviceOverviewInfo: { ...commonForm.value, id: isID.id }
    // hikInfo: hikForm.value,
    // iotInfo: iotForm.value,
    // zabbixInfo: zabbixForm.value,
    // shElectricInfo: shElectricForm.value
  }
  // console.log('打印1', sourceMap, commonForm.value?.assetsSource)
  // console.log('打印2', sourceMap.value[+commonForm.value?.assetsSource])
  if (sourceMap.value[commonForm.value?.assetsSource] === 'iot') {
    params.iotInfo = iotForm.value
  } else if (sourceMap.value[commonForm.value?.assetsSource] === 'hik') {
    params.hikInfo = hikForm.value
    // sourceMap[commonForm?.assetsSource] === 'zabbix'
  } else if (sourceMap.value[commonForm.value?.assetsSource] === 'zabbix') {
    params.zabbixInfo = zabbixForm.value
  } else if (sourceMap.value[commonForm.value?.assetsSource] === 'ShanghaiElectric') {
    params.shElectricInfo = shElectricForm.value
  }
  // console.log('总表单:', hikForm.value, iotForm.value, zabbixForm.value, shElectricForm.value)
  const results = await Promise.all([commonFormRef.value.validate()])
  const isAllValid = results.every((res) => res === true)
  // console.log('打印results', results)
  // console.log(isAllValid, '是否通过')

  if (isAllValid) {
    if (isID.id) {
      try {
        await EquipmentApi.updateItem(params)
        ElMessage.success('编辑成功')
        goBack()
      } catch (error) {
        console.error('编辑失败，错误信息:', error)
        ElMessage.error('编辑失败')
      }
    } else {
      try {
        await EquipmentApi.addList(params)
        ElMessage.success('提交成功')
        goBack()
      } catch (error) {}
    }
  } else {
    // console.log('校验未通过字段:', results)
    ElMessage.warning('请完整填写必填项')
  }
}
// =======================
</script>
<style lang="scss" scoped>
.border-style {
  border: 1px solid #ccc;
  border-radius: 1%;
}
.my-component {
  width: 100%;
  height: 100%;
  background-color: white;
  display: flex;
  flex-direction: column;
  header {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    margin-bottom: 10px;
  }
  main {
    width: 100%;
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    .bottom-btn {
      width: 100%;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      button {
        width: 40%;
      }
    }
  }
}
</style>
