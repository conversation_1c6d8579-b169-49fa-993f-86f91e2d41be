<template>
  <ContentWrap class="my-component">
    <header>
      <el-button class="ml-5 mr-5" type="primary" plain @click="goBack">返回</el-button>
      <p>重点保障设备列表-详情</p>
    </header>
    <main>
      <div class="main-left">
        <!-- 主体上方的↓ -->
        <div class="main-top border-style">
          <p class="title-box">设备详情</p>
          <div class="detail-box">
            <PrivateShow :id="params.id" :keyFlag="true" />
          </div>
        </div>
        <!-- 主体上方的↑ -->
        <div class="main-bottom border-style">
          <div class="title-box">
            <div class="title">重点保障详情</div>

            <el-button class="ml-8px" size="small" type="primary">{{
              UTILS.identificationisKey(publicList2?.isKey)
            }}</el-button>
          </div>
          <div class="main-box">
            <!--  描述列表 -->
            <el-descriptions :column="1" border>
              <el-descriptions-item
                label-class-name="my-label"
                v-for="(item, index) in descriptionsItems3"
                :key="index"
              >
                <template #label>
                  <div>{{ item.label }}</div>
                </template>
                <div>{{ item.value }}</div>
              </el-descriptions-item>
            </el-descriptions>
            <!-- 描述列表 -->
          </div>
        </div>
        <!-- 主体下方的↑ -->
      </div>
      <div class="main-right">
        <header class="border-style">
          <img v-if="publicList?.pictureUrl" :src="publicList?.pictureUrl" alt="" />
          <img v-else src="@/assets/imgs/device.png" alt="" />
        </header>
        <!-- <main class="border-style"> <img src="@/assets/imgs/loginbg.png" alt="" /></main> -->
        <footer class="border-style">
          <div class="edit-time" v-for="item in logList" :key="item">
            <p>由{{ item.userName }}{{ item.subType }}</p>
            <!-- <p>编辑信息:{{ item.action }}</p> -->
            <p>{{ UTILS.timestampToDateString(item.createTime) }}</p>
          </div>
        </footer>
      </div>
    </main>
  </ContentWrap>
</template>

<script lang="ts" setup>
import PrivateShow from '@/components/PrivateShow/index.vue'
import { ref, reactive, onMounted } from 'vue'
import { useRoute, onBeforeRouteLeave } from 'vue-router'
import * as FocusApi from '@/api/operations/safeguard/focus'
import * as UTILS from './utils/index' //引入筛选的工具函数
const route = useRoute()
const params = reactive({ id: '', keyFlag: true })

// 底部默认数据
const descriptionsItems3 = ref([
  { label: '设备分组:', value: '' },
  { label: '告警等级:', value: '' },
  { label: '告警措施:', value: '' },
  { label: '保障类型:', value: '' },
  { label: '保障时间:', value: '' }
])
onMounted(async () => {
  const storedId = sessionStorage.getItem('focusDetailId')
  if (storedId) {
    params.id = storedId
    await getDetail()
    await getLog()
  } else {
    ElMessage.warning('已离开页面,请重新选择项目')
  }
  // // 从路由查询参数中获取 id 值
  // params.id = route.query.id as string
  // await getDetail()
  // await getLog()
})

// 使用 beforeRouteLeave 导航守卫
onBeforeRouteLeave((to, from) => {
  // 清除 sessionStorage 中的 focusDetailId
  // sessionStorage.removeItem('focusDetailId')
})
const warrantyStatus = ref(null) //保障状态
const publicList2 = ref({}) //用于判断是否重点
const getDetail = async () => {
  const res = await FocusApi.getDetail(params)
  const publicList = res.deviceOverviewInfo
  // console.log('获取详情数据', publicList)
  publicList2.value = res.deviceOverviewInfo
  // 判断保障状态
  if (publicList.warrantyStatus === 1) {
    warrantyStatus.value = true
  } else {
    warrantyStatus.value = false
  }
  // console.log('获取详情数据', publicList)

  descriptionsItems3.value = [
    { label: '设备分组:', value: publicList?.groupName },
    { label: '告警方式:', value: publicList?.warnName },
    { label: '告警措施:', value: publicList?.XXX }, //字段单
    { label: '保障类型:', value: isGuaranteeType(publicList?.guaranteeType) },
    {
      label: '保障时间:',
      value: getWarrantyTime(publicList?.groupTimeDOList, publicList.guaranteeType)
    }
  ]
}

// 保障类型判断函数
const isGuaranteeType = (value) => {
  switch (value) {
    case 1:
      return '单次保障'
    case 2:
      return '按月保障'
    case 3:
      return '按年保障'
    default:
      return ''
  }
}
// 保障时间判断函数
const getWarrantyTime = (groupTimeDOList, guaranteeType) => {
  if (guaranteeType === 1) {
    return `${groupTimeDOList[0].startTime} ~ ${groupTimeDOList[0].endTime}`
  } else if (guaranteeType === 2) {
    return groupTimeDOList
      .map(
        (item) => `每月 ${item.startTime.replace(' ', '日')} - ${item.endTime.replace(' ', '日')}`
      )
      .join(' | ')
  } else if (guaranteeType === 3) {
    return groupTimeDOList
      .map(
        (item) =>
          `每年 ${item.startTime.replace('-', '月').replace(' ', '日')} - ${item.endTime.replace('-', '月').replace(' ', '日')}`
      )
      .join(' | ')
  }
  return ''
}

// 返回上一页
const goBack = () => {
  window.history.back()
}
// 获得日志
const logList = ref([])
const getLog = async () => {
  const data = await FocusApi.getLog({ bizId: params.id, type: 'DEVICE_MANAGE_CENTER' })
  // console.log('日志', data)
  logList.value = data.list
}
</script>

<style lang="scss" scoped>
.border-style {
  border: 1px solid #ccc;
  border-radius: 1%;
}

.my-component {
  background-color: white;
  // width: 100%;
  // height: 100%;
  header {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    margin-bottom: 10px;
  }
  main {
    width: 100%;
    height: 820px;
    display: flex;
    .main-left {
      flex: 1;
      margin-right: 10px;
      margin-left: 20px;
      .main-top {
        width: 100%;
        height: 69%;
        margin-bottom: 5px;
        // background-color: green;
        // ***************
        .title-box {
          // width: 100%;
          // height: 40px;
          // line-height: 40px;
          // margin-left: 40px;
          width: 100%;
          height: 40px;
          display: flex;
          align-items: center;
          position: relative;
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 16px;
          color: #3b3c3d;
          text-align: left;
          font-style: normal;
          text-transform: none;
          line-height: 24px;
          margin: 3px 0;
          padding-left: 28px;

          &::before {
            margin: 10px 0 0 10px;
            content: '';
            position: absolute;
            left: 0;
            top: 6px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #005fff;
            border: 2px solid #d4e4ff;
          }
        }

        .detail-box {
          // background-color: pink;
          width: 97%;
          height: 92%;
          margin: 0 12px;
          overflow-y: auto;
          // 隐藏滚动条
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE 10+ */
          &::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Edge */
          }
        }
        // ***************
      }
      .main-bottom {
        width: 100%;
        height: 30%;

        .title-box {
          // width: 100%;
          // height: 40px;
          // display: flex;
          // align-items: center;
          // .title {
          //   margin: 0 20px 0 40px;
          // }
          width: 100%;
          height: 40px;
          display: flex;
          align-items: center;
          position: relative;
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 16px;
          color: #3b3c3d;
          text-align: left;
          font-style: normal;
          text-transform: none;
          line-height: 24px;
          margin: 3px 0;
          padding-left: 28px;

          &::before {
            margin: 10px 0 0 10px;
            content: '';
            position: absolute;
            left: 0;
            top: 6px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #005fff;
            border: 2px solid #d4e4ff;
          }
        }
        .main-box {
          width: 97%;
          height: 84%;
          padding-left: 20px;
        }
      }
    }
    .main-right {
      width: 250px;
      height: 100%;
      border: 1px solid #ccc;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;
      header {
        width: 95%;
        height: 25%;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 95%;
          height: 90%;
        }
      }
      main {
        width: 95%;
        height: 25%;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 95%;
          height: 90%;
        }
      }
      // 日志区域
      footer {
        width: 95%;
        height: 72%;
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 6px; // 滚动条宽度
          height: 6px; // 滚动条高度
        }
        &::-webkit-scrollbar-thumb {
          background-color: #c1c1c1c1; // 滚动条颜色
          border-radius: 3px; // 滚动条圆角
        }

        &::-webkit-scrollbar-track {
          background-color: #f1f1f1f1; // 滚动条轨道颜色
          border-radius: 3px; // 滚动条轨道圆角
        }
        .edit-time {
          margin-left: 20px;
          width: 90%;
          height: 70px;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          margin-bottom: 20px;
        }
      }
    }
  }
}
</style>
