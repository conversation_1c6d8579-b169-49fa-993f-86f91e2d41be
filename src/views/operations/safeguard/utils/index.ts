// 保障类型
export function formatSafeguardType(projectType: number): string {
  switch (projectType) {
    case 1:
      return '单次保障'
    case 2:
      return '按月保障'
    case 3:
      return '按年保障'
    default:
      return '未知类型'
  }
}

// 时间拼接
export function formatTimestamp(data) {
  const formatDate = (timestamp) => {
    if (!timestamp) return '' // 如果时间戳为空，返回空字符串
    const date = new Date(timestamp)
    const year = date.getFullYear() // 获取年份
    const month = String(date.getMonth() + 1).padStart(2, '0') // 获取月份，月份从0开始计数，所以需要+1
    const day = String(date.getDate()).padStart(2, '0') // 获取日期
    return `${year}-${month}-${day}`
  }

  const formatMonthlyDate = (timestamp) => {
    if (!timestamp) return '' // 如果时间戳为空，返回空字符串
    const parts = timestamp.split(' ')
    if (parts.length !== 2) return '' // 如果格式不正确，返回空字符串
    const datePart = parts[0]
    const timePart = parts[1]
    const dateParts = datePart.split('-')
    if (dateParts.length === 1) {
      // 处理 "03 00:00" 和 "31 00:00" 格式
      const day = dateParts[0].padStart(2, '0') // 获取日期
      return `每月${day}日${timePart}`
    } else if (dateParts.length === 2) {
      // 处理 "03-18 00:00" 和 "03-11 00:00" 格式
      const month = dateParts[0].padStart(2, '0') // 获取月份
      const day = dateParts[1].padStart(2, '0') // 获取日期
      return `每年${month}月${day}日${timePart}`
    }
    return '' // 如果格式不正确，返回空字符串
  }

  return data.map((item) => {
    const startTime = formatMonthlyDate(item.startTime) || item.startTime
    const endTime = formatMonthlyDate(item.endTime) || item.endTime
    if (startTime && endTime) {
      return `${startTime}-${endTime}`
    }
    return '' // 如果 startTime 或 endTime 为空，返回空字符串
  })
}

// 是否在保
export function identificationStatus(value) {
  switch (value) {
    case 0:
      return '质保中'
    case 1:
      return '已过保'
    default:
      return '未知'
  }
}

// 是否重点
export function identificationKey(value) {
  switch (value) {
    case 0:
      return '非重点'
    case 1:
      return '重点'
    default:
      return '未知'
  }
}
// 是否保障
export function identificationisKey(value) {
  switch (value) {
    case 0:
      return '未保障'
    case 1:
      return '保障中'
    default:
      return '未知'
  }
}
// 同步状态
export function identificationSyncStatus(value) {
  switch (value) {
    case 0:
      return '未同步'
    case 1:
      return '已同步'
    default:
      return '未知'
  }
}

//时间戳转化
export function timestampToDateString(timestamp: number): string {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 表格状态文字
export function getStatusText(status: number): string {
  switch (status) {
    case 0:
      return '质保中'
    case 1:
      return '已过保'
    default:
      return ''
  }
}

// 表格状态文字
export function getDeviceStatusText(status: number): string {
  switch (status) {
    case 0:
      return '正常'
    case 1:
      return '故障'
    case 2:
      return '报备'
    case 3:
      return '告警'
    default:
      return ''
  }
}

export function getStatusText2(status: number): string {
  switch (status) {
    case 0:
      return '未同步'
    case 1:
      return '已同步'
    default:
      return ''
  }
}

// 表格状态类
export function getStatusClass(status: number): string {
  switch (status) {
    case 0:
      return 'status-normal'
    case 1:
      return 'status-fault'
    default:
      return ''
  }
}

export function getStatusClass2(status: number): string {
  switch (status) {
    case 0:
      return 'status-alarm'
    case 1:
      return ''
    default:
      return ''
  }
}

// 表格状态类
export function getStatusClass3(status: number): string {
  switch (status) {
    case 0:
      return 'status-normal'
    case 1:
      return 'status-fault'
    case 2:
      return 'status-reported'
    case 3:
      return 'status-alarm'
    default:
      return ''
  }
}

// 在项目公共工具函数中增加日期格式化工具
export const formatDate = (date: string | Date, format = 'YYYY-MM-DD HH:mm:ss') => {
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  const o = {
    'M+': d.getMonth() + 1,
    'D+': d.getDate(),
    'H+': d.getHours(),
    'm+': d.getMinutes(),
    's+': d.getSeconds(),
    'q+': Math.floor((d.getMonth() + 3) / 3),
    S: d.getMilliseconds()
  }
  if (/(Y+)/.test(format)) {
    format = format.replace(RegExp.$1, `${d.getFullYear()}`.substr(4 - RegExp.$1.length))
  }
  for (const k in o) {
    if (new RegExp(`(${k})`).test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? `${o[k]}` : `00${o[k]}`.substr(`${o[k]}`.length)
      )
    }
  }
  return format
}
