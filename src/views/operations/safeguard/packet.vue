<template>
  <ContentWrap class="my-component">
    <header>
      <p>重点保障设备分组</p>
    </header>
    <main>
      <ContentWrap>
        <div class="header-search">
          <div class="search-left">
            <el-form style="max-width: 600px; display: flex" :model="form" label-width="auto">
              <el-form-item class="mr-3">
                <el-input placeholder="输入分组名称" v-model="form.groupName" />
              </el-form-item>
              <el-form-item>
                <XButton
                  class="text-button"
                  preIcon="ep:search"
                  title="查询"
                  width="75px"
                  gradient
                  @click="onSubmit"
                  v-hasPermi="['infra:assets-group:query']"
                />

                <el-button @click="resetForm"><Icon icon="ep:refresh" />重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="search-right">
            <el-button plain @click="showDialog(false)" v-hasPermi="['infra:assets-group:create']">
              <Icon icon="ep:plus" />
              新增
            </el-button>
          </div>
        </div>
      </ContentWrap>

      <!-- 底部表格盒子↓ -->
      <div class="main-bottom">
        <el-table
          :data="tableData"
          height="530"
          v-loading="loading"
          style="width: 100%; margin-bottom: 10px"
          :header-cell-style="{
            'background-color': '#F4F6F8',
            'font-size': '14px',
            color: '#3B3C3D'
          }"
        >
          <el-table-column prop="groupName" label="分组名称" show-overflow-tooltip />
          <el-table-column prop="warnTypeName" label="告警方式" show-overflow-tooltip>
            <template #default="scope">
              <el-tooltip
                :content="scope.row.warnTypeDescription"
                placement="top"
                v-if="scope?.row?.warnTypeName"
              >
                <span @mouseenter="loadWarnTypeDescription(scope.row)">
                  {{ scope.row.warnTypeName }}
                  <img src="@/assets/imgs/icons/faultTab2.png" alt="" />
                </span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="guaranteeType" label="保障类型">
            <template #default="scope">
              <span>{{ formatSafeguardType(scope.row.guaranteeType) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="assetsGroupTimeDOList" label="保障时间" width="350">
            <template #default="scope">
              <span>
                {{
                  scope.row.assetsGroupTimeDOList && scope.row.assetsGroupTimeDOList.length > 0
                    ? formatTimestamp(scope.row.assetsGroupTimeDOList).join(' / ')
                    : '无保障时间'
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="date" label="操作" width="220">
            <template #default="scope">
              <el-button
                link
                type="primary"
                size="small"
                @click="showDialog(true, scope.row)"
                v-hasPermi="['infra:assets-group:update']"
                >编辑</el-button
              >
              <el-button
                link
                type="primary"
                size="small"
                @click="deleteRow(scope.row.id)"
                v-hasPermi="['infra:assets-group:delete']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <div class="demo-pagination-block">
          <el-pagination
            v-model:current-page="form.pageNo"
            v-model:page-size="form.pageSize"
            :page-sizes="[10, 20, 30, 50, 100]"
            :size="size"
            :disabled="disabled"
            :background="background"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
      <!-- 底部表格盒子↑ -->
    </main>
    <Dialog v-model="dialogVisible" :title="isEdit ? '编辑分组' : '新增分组'">
      <el-form :model="formDialog" ref="formDialogRef">
        <el-form-item
          label="分组名称"
          :prop="'groupName'"
          :rules="{ required: true, message: '请输入分组名称', trigger: 'blur' }"
        >
          <el-input v-model="formDialog.groupName" />
        </el-form-item>
        <el-form-item
          label="告警方式"
          :prop="'warnType'"
          :rules="{ required: true, message: '请选择告警方式', trigger: 'change' }"
        >
          <el-select v-model="formDialog.warnType" placeholder="请选择">
            <el-option
              v-for="item in selectList"
              :key="item"
              :label="item.warnTypeName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="保障类型"
          :prop="'guaranteeType'"
          :rules="{ required: true, message: '请选择保障类型', trigger: 'change' }"
        >
          <el-select
            v-model="formDialog.guaranteeType"
            placeholder="请选择"
            @change="handleGuaranteeTypeChange"
          >
            <el-option label="单次保障" :value="1" />
            <el-option label="按月保障" :value="2" />
            <el-option label="按年保障" :value="3" />
          </el-select>
        </el-form-item>

        <template v-for="(time, index) in formDialog.timeList" :key="index">
          <div>
            <el-form-item
              :label="index === 0 ? '保障开始' : '保障开始'"
              :prop="`timeList[${index}].startTime`"
              :rules="{
                required: true,
                message: '请选择保障开始时间',
                trigger: 'change'
              }"
            >
              <!-- <el-date-picker
                v-model="time.startTime"
                :type="startDatePickerType"
                :format="dateFormat"
                :value-format="dateFormat"
                placeholder="选择日期时间"
              /> -->
              <el-date-picker
                v-model="time.startTime"
                :type="startDatePickerType"
                :format="dateFormat"
                :value-format="dateFormat"
                placeholder="选择日期时间"
                @change="handleStartTimeChange(index)"
              />
            </el-form-item>

            <el-form-item
              :label="index === 0 ? '保障结束' : '保障结束'"
              :prop="`timeList[${index}].endTime`"
              :rules="{
                required: true,
                message: '请选择保障结束时间',
                trigger: 'change'
              }"
            >
              <!-- <el-date-picker
                v-model="time.endTime"
                :type="endDatePickerType"
                :format="dateFormat"
                :value-format="dateFormat"
                placeholder="选择日期时间"
              /> -->
              <el-date-picker
                v-model="time.endTime"
                :type="endDatePickerType"
                :format="dateFormat"
                :value-format="dateFormat"
                placeholder="选择日期时间"
                @change="handleEndTimeChange(index)"
              />
            </el-form-item>
          </div>
        </template>
        <div class="btn-main">
          <el-button
            v-if="formDialog.guaranteeType !== 1 && formDialog.guaranteeType !== null"
            class="add-btn"
            @click="addTimeList"
          >
            +
          </el-button>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submitForm">确认</el-button>
          <el-button @click="dialogVisible = false">取消</el-button>
        </span>
      </template>
    </Dialog>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, onBeforeUnmount, computed, watch } from 'vue'
import * as PacketApi from '@/api/operations/safeguard/packet'
import { getSelect } from '@/api/operations/list'
import { formatSafeguardType, formatTimestamp, formatDate } from './utils'
import { getWarnTypeDescription } from '../list/utils/index'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const selectList = ref([])
const fetchSelectData = async () => {
  try {
    const data = await getSelect()
    selectList.value = data
    // console.log('打印下拉数据', data)
  } catch (error) {
    console.error('获取下拉数据失败:', error)
  }
}

onMounted(() => {
  getList()
  fetchSelectData()
  window.addEventListener('keydown', handleGlobalKeyDown)
})

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleGlobalKeyDown)
})
const handleGlobalKeyDown = (event) => {
  if (event.key === 'Enter') {
    onSubmit()
  }
}
const form = reactive({
  groupName: '',
  pageNo: 1,
  pageSize: 10
})
// 缓存已请求过的 warnType
const warnTypeCache = new Map()
const loadWarnTypeDescription = async (row) => {
  if (!row.warnTypeDescription && row.warnType) {
    if (warnTypeCache.has(row.warnType)) {
      row.warnTypeDescription = warnTypeCache.get(row.warnType)
    } else {
      try {
        const description = await getWarnTypeDescription(row.warnType)
        row.warnTypeDescription = description
        warnTypeCache.set(row.warnType, description)
      } catch (error) {
        // console.error('获取告警方式描述失败:', error)
        row.warnTypeDescription = '获取失败'
      }
    }
  }
}
const getList = async () => {
  loading.value = true
  try {
    const data = await PacketApi.getList(form)
    // 提前解析告警方式描述
    // await Promise.all(
    //   data.list.map(async (item) => {
    //     item.warnTypeDescription = await getWarnTypeDescription(item?.warnType)
    //   })
    // )
    // 初始化时不请求描述，只赋空值
    data.list.forEach((item) => {
      item.warnTypeDescription = null
    })
    // console.log('分页数据', data.list)
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const onSubmit = () => {
  getList()
}
const resetForm = () => {
  form.groupName = ''
  getList()
}

// 表格相关
const loading = ref(true)
const tableData = ref([])
const total = ref(0)
const size = ref('default')
const background = ref(true) // 分页背景颜色
const disabled = ref(false)

const handleSizeChange = async (val: number) => {
  form.pageSize = val
  await getList() //重新获取
}

const handleCurrentChange = async (val: number) => {
  form.pageNo = val
  await getList() //重新获取
}

const dialogVisible = ref(false)
const isEdit = ref(false)

const dateFormat = computed(() => {
  switch (formDialog.guaranteeType) {
    case 1: // 单次保障
      return 'YYYY-MM-DD HH:mm'
    case 2: // 按月保障
      return 'DD HH:mm'
    case 3: // 按年保障
      return 'MM-DD HH:mm'
    default:
      return 'YYYY-MM-DD HH:mm'
  }
})

const startDatePickerType = computed(() => {
  switch (formDialog.guaranteeType) {
    case 1: // 单次保障
      return 'datetime'
    case 2: // 按月保障
      return 'date'
    case 3: // 按年保障
      return 'date'
    default:
      return 'datetime'
  }
})

const endDatePickerType = computed(() => {
  switch (formDialog.guaranteeType) {
    case 1: // 单次保障
      return 'datetime'
    case 2: // 按月保障
      return 'date'
    case 3: // 按年保障
      return 'date'
    default:
      return 'datetime'
  }
})

const handleGuaranteeTypeChange = () => {
  // 当保障类型改变时，清空时间选择
  formDialog.timeList = [
    {
      serialNumber: 0,
      startTime: '',
      endTime: ''
    }
  ]

  switch (formDialog.guaranteeType) {
    case 1: // 单次保障
      formDialog.timeList[0].serialNumber = 0
      break
    case 2: // 按月保障
      formDialog.timeList[0].serialNumber = 1
      break
    case 3: // 按年保障
      formDialog.timeList[0].serialNumber = 2
      break
  }
}

const formDialog = reactive({
  id: null,
  groupName: '', // 分组名称
  warnType: null, // 告警级别
  guaranteeType: null, // 保障类型
  timeList: [
    {
      serialNumber: 0, // 组内排序序号
      startTime: '', // 保障开始
      endTime: '' // 保障结束
    }
  ]
})
const startTimeMap = new Map() // 用于存储每个索引对应的开始时间
const endTimeMap = new Map() // 用于存储每个索引对应的结束时间
// =====================================
// 在 handleStartTimeChange 和 handleEndTimeChange 方法中增加日期转换逻辑
const handleStartTimeChange = (index) => {
  const time = formDialog.timeList[index]
  // 使用 formatDate 函数确保 startTime 是有效的日期格式
  startTimeMap.set(index, formatDate(time.startTime) || null)
  if (time.endTime && formatDate(time.startTime) && formatDate(time.endTime)) {
    const startTime = formatDate(time.startTime)
    const endTime = formatDate(time.endTime)
    // console.log('开始时间格式化：', startTime, '结束时间格式化：', endTime)
    if (Date.parse(startTime) > Date.parse(endTime)) {
      ElMessage.error('结束时间不能早于开始时间')
      formDialog.timeList[index].endTime = ''
      endTimeMap.set(index, null)
    }
  }
}

const handleEndTimeChange = (index) => {
  const time = formDialog.timeList[index]
  // 使用 formatDate 函数确保 endTime 是有效的日期格式
  endTimeMap.set(index, formatDate(time.endTime) || null)

  if (time.startTime && formatDate(time.startTime) && formatDate(time.endTime)) {
    const startTime = formatDate(time.startTime)
    const endTime = formatDate(time.endTime)
    // console.log('开始时间格式化：', startTime, '结束时间格式化：', endTime)
    if (Date.parse(endTime) < Date.parse(startTime)) {
      ElMessage.error('开始时间不能晚于结束时间')
      formDialog.timeList[index].startTime = ''
      startTimeMap.set(index, null)
    }
  }
}

// 增加日期有效性检查函数
const isValidDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return !isNaN(date.getTime())
}

watch(
  () => [...formDialog.timeList], // 监听 timeList 的变化
  () => {
    formDialog.timeList.forEach((time, index) => {
      const startTime = time.startTime
      const endTime = time.endTime
      if (startTime && endTime) {
        // console.log('开始时间原始：', startTime, '结束时间原始：', endTime)
        const startTimeFormatted = formatDate(startTime)
        const endTimeFormatted = formatDate(endTime)
        // console.log('开始时间格式化：', startTimeFormatted, '结束时间格式化：', endTimeFormatted)
        // 为了按月保障的特殊处理,
        const newStart = parseInt(startTime.substring(0, 2), 10)
        const newEnd = parseInt(endTime.substring(0, 2), 10)
        // console.log('截取后', newStart, newEnd)
        if (Date.parse(startTimeFormatted) > Date.parse(endTimeFormatted) || newStart > newEnd) {
          ElMessage.error('结束时间不能早于开始时间')
          formDialog.timeList[index].endTime = ''
          endTimeMap.set(index, null)
        }
      }
    })
  },
  { deep: true } // 深度监听，检测对象内部的变化
)

const addTimeList = () => {
  // 最后一个时间对象的 serialNumber 加 1
  const newSerialNumber =
    formDialog.timeList.length > 0
      ? formDialog.timeList[formDialog.timeList.length - 1].serialNumber + 1
      : 0

  formDialog.timeList.push({
    serialNumber: newSerialNumber,
    startTime: '',
    endTime: ''
  })
}

const formDialogRef = ref(null)

const resetDialogForm = () => {
  formDialog.id = ''
  formDialog.groupName = ''
  formDialog.warnType = null
  formDialog.guaranteeType = null
  formDialog.timeList[0].startTime = ''
  formDialog.timeList[0].endTime = ''
  // 清除表单验证状态
  if (formDialogRef.value) {
    formDialogRef.value.resetFields()
  }
}

const showDialog = async (edit: boolean, row?: any) => {
  isEdit.value = edit
  dialogVisible.value = true
  await resetDialogForm() // 等待清除函数执行完成

  if (edit && row) {
    formDialog.id = row.id // 编辑传id
    formDialog.groupName = row.groupName
    formDialog.warnType = row.warnType
    formDialog.guaranteeType = row.guaranteeType

    // 根据 guaranteeType 调整 startTime 和 endTime 的格式
    formDialog.timeList = row.assetsGroupTimeDOList.map((time, index) => ({
      serialNumber: index,
      startTime: time.startTime,
      endTime: time.endTime
    }))
  } else {
    // 新增时，根据 guaranteeType 初始化 timeList
    handleGuaranteeTypeChange()
  }
}

const submitForm = async () => {
  if (formDialogRef.value) {
    try {
      const valid = await formDialogRef.value.validate()
      if (valid) {
        if (isEdit.value) {
          // 更新操作
          await PacketApi.updateItem(formDialog)
          message.success(t('common.updateSuccess'))
        } else {
          // 新增操作
          await PacketApi.addList(formDialog)
          message.success(t('common.createSuccess'))
        }
        await getList()
        await resetDialogForm() //调用清除函数
        dialogVisible.value = false
      } else {
        // console.log('表单验证失败')
      }
    } catch (error) {
      console.error('表单验证或提交过程中发生错误:', error)
    }
  }
}

// 删除
const deleteRow = async (id: number) => {
  // 执行删除操作
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PacketApi.deleteList({ ids: id })
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}
</script>

<style lang="scss" scoped>
.border-style {
  border: 1px solid #ccc;
  border-radius: 1%;
}
.my-component {
  // width: 100%;
  // height: 100%;
  background-color: white;
  header {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    // border-bottom: 1px solid #ccc;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: bold;
    padding-left: 20px;
    display: flex;
    align-items: center;
  }
  main {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    // padding-left: 20px;
    // padding-right: 40px;
    .header-search {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: -12px;
      .search-left {
        width: 50%;
        // background-color: lightcoral;
      }
      .search-right {
        width: 50%;
        // background-color: lightblue;
        display: flex;
        justify-content: flex-end;
      }
    }
    .main-bottom {
      flex: 1;
      overflow: auto; // 如果内容超出，添加滚动条
      :deep(.el-table__header th) {
        text-align: left;
      }
      :deep(.el-table__body td) {
        text-align: left;
      }
      .el-table {
        width: 100% !important;
      }
      // .demo-pagination-block + .demo-pagination-block {
      //   margin-top: 10px;
      // }
      // .demo-pagination-block .demonstration {
      //   margin-bottom: 16px;
      // }
      .demo-pagination-block {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}
.btn-main {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  .add-btn {
    width: 95%;
  }
}

.dialog-footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  .el-button {
    // width: 45%;
  }
}
// 查询按钮样式
.text-button {
  color: #fff;
}
</style>
