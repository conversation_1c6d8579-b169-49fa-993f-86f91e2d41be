<template>
  <div class="mr-8">
    <ContentWrap class="my-component">
      <div class="top-title">重点保障设备列表</div>
      <div class="top-equipment">
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/camera.png" alt="" />
            </div>
            <div class="right-txt">
              <div>设备总数</div>
              <div>{{ equipmentBasis.deviceTotal }}</div>
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/camera2Big.png" alt="" />
          </div>
        </div>
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/time.png" alt="" />
            </div>
            <div class="right-txt">
              <div>设备完好率</div>
              <div
                >{{
                  (equipmentBasis.rate == null ? 0 : parseFloat(equipmentBasis.rate) || 0).toFixed(
                    2
                  )
                }}%</div
              >
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/time2.png" alt="" />
          </div>
        </div>
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/normal.png" alt="" />
            </div>
            <div class="right-txt">
              <div>正常设备</div>
              <div>{{ equipmentBasis.normal }}</div>
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/normal2.png" alt="" />
          </div>
        </div>
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/label.png" alt="" />
            </div>
            <div class="right-txt">
              <div>报备设备</div>
              <div>{{ equipmentBasis.report }}</div>
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/label2.png" alt="" />
          </div>
        </div>
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/fault.png" alt="" />
            </div>
            <div class="right-txt">
              <div>故障设备</div>
              <div>{{ equipmentBasis.malfunction }}</div>
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/fault2.png" alt="" />
          </div>
        </div>
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/warning.png" alt="" />
            </div>
            <div class="right-txt">
              <div>告警设备</div>
              <div class="fs-w">{{ WarningCount.allCount }}</div>
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/warning2.png" alt="" />
          </div>
        </div>
      </div>
      <div class="main">
        <el-row :gutter="20">
          <el-col :span="4" :xs="24">
            <div class="left-year">
              <div class="button-group">
                <button
                  class="nav-button"
                  :class="{ active: activeTab === 'first' }"
                  @click="navClick(1)"
                >
                  保障中
                </button>
                <button
                  class="nav-button"
                  :class="{ active: activeTab === 'second' }"
                  @click="navClick(0)"
                >
                  未保障
                </button>
              </div>
              <div class="tabs-content">
                <div id="first" class="tab-content" v-if="activeTab === 'first'">
                  <p
                    v-for="item in leftList"
                    :key="item.id"
                    class="text-style"
                    :class="{ highlight: selectedUnit === item }"
                    @click="clickGrouping(item, 1)"
                  >
                    <img src="@/assets/imgs/icons/date.png" alt="" class="dropdown-image" />
                    <el-tooltip
                      :content="item?.groupName"
                      placement="top"
                      :disabled="item?.groupName.length <= 10"
                    >
                      <span class="el-dropdown-link">{{
                        item?.groupName.length > 10
                          ? item?.groupName.slice(0, 10) + '...'
                          : item?.groupName
                      }}</span>
                    </el-tooltip>
                  </p>
                </div>
                <div id="second" class="tab-content" v-if="activeTab === 'second'">
                  <p
                    v-for="item in leftList"
                    :key="item.id"
                    class="text-style"
                    :class="{ highlight: selectedUnit === item }"
                    @click="clickGrouping(item, 0)"
                  >
                    <img src="@/assets/imgs/icons/date.png" alt="" class="dropdown-image" />
                    <el-tooltip
                      :content="item?.groupName"
                      placement="top"
                      :disabled="item?.groupName.length <= 10"
                    >
                      <span class="el-dropdown-link">{{
                        item?.groupName.length > 10
                          ? item?.groupName.slice(0, 10) + '...'
                          : item?.groupName
                      }}</span>
                    </el-tooltip>
                  </p>
                </div>
              </div>
            </div></el-col
          >
          <el-col :span="20" :xs="24">
            <div class="right-main">
              <!-- 表单部分 -->
              <ContentWrap class="form-box">
                <el-form
                  ref="queryFormRef"
                  :inline="true"
                  :model="form"
                  class="form-search flex flex-wrap items-start -mb-15px"
                >
                  <el-form-item label="" class="!mr-3">
                    <el-input
                      v-model="form.assets"
                      class="!w-240px"
                      clearable
                      placeholder="输入设备编号/名称"
                    />
                  </el-form-item>
                  <el-form-item label="设备类型" class="!mr-3">
                    <el-cascader
                      v-model="form.assetsTypeId"
                      :options="typeList"
                      :props="cascaderProps"
                      collapse-tags
                      clearable
                    />
                  </el-form-item>
                  <el-form-item label="区域" prop="areaId" class="!mr-3">
                    <DictionaryForArea
                      v-model="form.areaId"
                      type="cascader"
                      width="240px"
                      dict-type="oamArea"
                      :cascader-props="{
                        multiple: true,
                        checkStrictly: true,
                        label: 'name',
                        value: 'id'
                      }"
                      :max-collapse-tags="2"
                      placeholder="请选择区域"
                    />
                  </el-form-item>

                  <div class="min-w-[600px] flex-1 flex justify-between mb-[18px] pl-2">
                    <div>
                      <XButton
                        title="查询"
                        preIcon="ep:search"
                        gradient
                        @click="onSubmit"
                        v-hasPermi="['infra:device-overview-info:query']"
                      />
                      <el-button @click="resetForm(true)"><Icon icon="ep:refresh" />重置</el-button>
                      <XButton
                        class="text-button"
                        preIcon="ep:search"
                        title="高级搜索"
                        width="100px"
                        gradient
                        @click="showAdvancedSearch"
                        v-hasPermi="['infra:device-overview-info:query']"
                      />
                    </div>
                    <div>
                      <el-button plain @click="addClick" v-hasPermi="['infra:assets-group:create']">
                        <Icon icon="ep:plus" />
                        添加
                      </el-button>
                      <el-button
                        plain
                        @click="exportClick"
                        v-hasPermi="['infra:device-overview-info:export']"
                      >
                        <Icon icon="ep:download" />导出
                      </el-button>
                      <el-button
                        plain
                        @click="allDeleteClick"
                        v-hasPermi="['infra:device-overview-info:update']"
                      >
                        <Icon icon="ep:delete" />
                        移出
                      </el-button>
                    </div>
                  </div>
                </el-form>
              </ContentWrap>

              <!-- 表格部分 -->
              <div class="right-table">
                <el-table
                  :data="tableData"
                  height="530"
                  v-loading="loading"
                  style="width: 100%; margin-bottom: 10px"
                  @selection-change="handleSelectionChange"
                  :header-cell-style="{
                    'background-color': '#F4F6F8',
                    'font-size': '14px',
                    color: '#3B3C3D'
                  }"
                >
                  <!-- 多选框列 -->
                  <el-table-column type="selection" :selectable="selectable" min-width="55" />
                  <!-- 其他列 -->
                  <el-table-column type="index" :index="tableIndex" label="序号" min-width="60" />
                  <el-table-column
                    prop="assetsCode"
                    label="设备编号"
                    min-width="150"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="assetsName"
                    label="设备名称"
                    min-width="150"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="assetsTypeName"
                    label="设备类型"
                    min-width="100"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="area"
                    label="所属区域"
                    min-width="120"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="projectName"
                    label="所属项目"
                    min-width="120"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="groupName"
                    label="所属分组"
                    min-width="100"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="warrantyDate"
                    label="保障时间"
                    min-width="270"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <!--单次保障-->
                      <span v-if="scope.row.guaranteeType === 1">
                        {{ scope.row.groupTimeDOList[0].startTime }} ~
                        {{ scope.row.groupTimeDOList[0].endTime }}
                      </span>
                      <!--按月保障-->
                      <span v-if="scope.row.guaranteeType === 2">
                        <span v-for="(item, index) in scope.row.groupTimeDOList" :key="item.id">
                          每月{{ item.startTime.replace(' ', '日') }}-{{
                            item.endTime.replace(' ', '日')
                          }}
                          <span v-if="index < scope.row.groupTimeDOList.length - 1">|</span>
                        </span>
                      </span>
                      <!--按年保障-->
                      <span v-if="scope.row.guaranteeType === 3">
                        <span v-for="(item, index) in scope.row.groupTimeDOList" :key="item.id">
                          每年{{ item.startTime.replace('-', '月').replace(' ', '日') }}-{{
                            item.endTime.replace('-', '月').replace(' ', '日')
                          }}
                          <span v-if="index < scope.row.groupTimeDOList.length - 1">|</span>
                        </span>
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="operatingUnitName"
                    label="运营商"
                    min-width="120"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="maintenanceUnitName"
                    label="维保单位"
                    min-width="120"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="assetsSourceName"
                    label="设备来源"
                    min-width="120"
                    show-overflow-tooltip
                  />

                  <el-table-column
                    prop="warnName"
                    label="告警方式"
                    min-width="90"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <el-tooltip
                        :content="scope.row.warnTypeDescription"
                        placement="top"
                        v-if="scope?.row?.warnName"
                      >
                        <span @mouseenter="loadWarnTypeDescription(scope.row)">
                          {{ scope.row.warnName }}
                          <img src="@/assets/imgs/icons/faultTab2.png" alt="" />
                        </span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="warrantyStatus" label="设备状态" min-width="90">
                    <template #default="scope">
                      <div :class="UTILS.getStatusClass3(scope.row.deviceStatus)">
                        {{ UTILS.getDeviceStatusText(scope.row.deviceStatus) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="syncStatus" label="同步状态" min-width="90">
                    <template #default="scope">
                      <div :class="UTILS.getStatusClass2(scope.row.syncStatus)">
                        {{ UTILS.getStatusText2(scope.row.syncStatus) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="date" label="操作" min-width="150" fixed="right">
                    <template #default="scope">
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click="handleClick(scope.row.id)"
                        v-hasPermi="['system:operate-log:query']"
                      >
                        详情
                      </el-button>
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click="editClick(scope.row.id)"
                        v-hasPermi="['infra:device-overview-info:update']"
                      >
                        编辑
                      </el-button>
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click="deleteClick(scope.row.id)"
                        v-hasPermi="['infra:device-overview-info:update']"
                      >
                        移出
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 分页部分 -->
                <div class="demo-pagination-block">
                  <el-pagination
                    v-model:current-page="form.pageNo"
                    v-model:page-size="form.pageSize"
                    :page-sizes="[10, 20, 30, 50, 100]"
                    :size="size"
                    :disabled="disabled"
                    :background="background"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  />
                </div>
              </div> </div
          ></el-col>
        </el-row>
        <!-- 右侧盒子 -->
      </div>

      <!-- 高级搜索遮罩层 -->
      <el-dialog v-model="showDialog" title="高级搜索" width="45%">
        <el-form :model="form" class="form-search">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="设备编号:">
                <el-input v-model="form.assetsCode" placeholder="输入设备编号" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="设备名称:">
                <el-input v-model="form.assetsName" placeholder="输入设备名称" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="设备类型:">
                <el-cascader
                  style="width: 100%"
                  v-model="form.assetsTypeId"
                  :options="typeList"
                  :props="cascaderProps"
                  collapse-tags
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="所属区域:">
                <Dictionary
                  style="width: 100%"
                  v-model="form.areaId"
                  type="cascader"
                  width="240px"
                  dict-type="oamArea"
                  :cascader-props="{
                    multiple: true,
                    checkStrictly: true,
                    label: 'name',
                    value: 'id'
                  }"
                  :max-collapse-tags="2"
                  placeholder="请选择区域"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="运营商:">
                <el-select v-model="form.operatingUnit" placeholder="选择运营商">
                  <el-option
                    v-for="item in operatingUnitList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="维保单位:">
                <el-select v-model="form.maintenanceUnit" placeholder="选择维保单位">
                  <el-option
                    v-for="item in maintenanceUnitList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="设备来源:">
                <el-select v-model="form.assetsSource" placeholder="选择设备来源">
                  <el-option
                    v-for="item in assetsSourceList"
                    :key="item.id"
                    :label="item.value"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="同步状态:">
                <el-select v-model="form.syncStatus" placeholder="选择同步状态">
                  <el-option label="未同步" :value="0" />
                  <el-option label="已同步" :value="1" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24" class="bottom-btn">
              <el-button type="primary" @click="submitAdvancedSearch">确认</el-button>
              <el-button @click="resetForm(true)">重置</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-dialog>

      <!-- 添加遮罩层 -->
      <el-dialog class="add-dialog" v-model="showDialogAdd" title="添加重点设备" width="75%">
        <header class="mb-2">
          <el-form :model="addForm" label-width="auto" style="max-width: 100%">
            <el-form-item label="选择添加分组">
              <el-select v-model="addForm.groupId" placeholder="请选择分组">
                <el-option
                  v-for="item in topGroup"
                  :key="item"
                  :label="item.groupName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </header>
        <main>
          <div class="dialog-left">
            <ContentWrap class="ContentWrap-style">
              <DeptTree @node-click="handleDeptNodeClick" />
            </ContentWrap>
          </div>

          <div class="dialog-right">
            <!-- 右侧搜索表单 -->
            <div class="right-top">
              <el-form
                ref="queryFormRef"
                :inline="true"
                :model="formSearch"
                class="form-search flex flex-wrap items-start -mb-15px"
              >
                <el-form-item label="" class="!mr-3">
                  <el-input
                    v-model="formSearch.assetsCode"
                    clearable
                    placeholder="输入设备编号/名称"
                  />
                </el-form-item>
                <el-form-item label="设备类型" class="!mr-3" style="width: 240px">
                  <el-cascader
                    v-model="formSearch.assetsTypeId"
                    :options="typeList"
                    :props="cascaderProps"
                    collapse-tags
                    clearable
                  />
                </el-form-item>
                <el-form-item label="区域" prop="areaId" class="!mr-3">
                  <Dictionary
                    v-model="formSearch.areaId"
                    type="cascader"
                    width="180px"
                    dict-type="oamArea"
                    :cascader-props="{
                      multiple: true,
                      checkStrictly: true,
                      label: 'name',
                      value: 'id'
                    }"
                    :max-collapse-tags="2"
                    placeholder="请选择区域"
                  />
                </el-form-item>
                <el-form-item>
                  <XButton
                    class="text-button"
                    preIcon="ep:search"
                    title="查询"
                    width="75px"
                    gradient
                    @click="dialogSubmit"
                  />
                </el-form-item>
              </el-form>
            </div>
            <!-- 右侧表格 -->
            <div class="right-tabel">
              <el-table
                :data="dialogTableData"
                style="width: 100%; margin-bottom: 10px"
                @selection-change="handleSelectionChange"
                class="table-style"
              >
                <!-- 多选框列 -->
                <el-table-column type="selection" :selectable="selectable" width="55" />
                <!-- 其他列 -->
                <el-table-column
                  prop="assetsCode"
                  label="设备编号"
                  width="150"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="assetsName"
                  label="设备名称"
                  width="150"
                  show-overflow-tooltip
                />
                <el-table-column prop="projectName" label="所属项目" width="120" />
                <el-table-column prop="groupName" label="所属分组" width="120" />
                <el-table-column prop="assetsTypeName" label="设备类型" width="120" />
                <el-table-column prop="area" label="区域" width="120" />
                <el-table-column prop="address" label="详细位置" width="120" />
              </el-table>
            </div>
            <!-- 右侧盒子结束↓ -->
          </div>
        </main>

        <footer>
          <el-button type="primary" @click="okFun">确认</el-button>
          <el-button @click="cancelFun">取消</el-button>
        </footer>
      </el-dialog>
    </ContentWrap>
  </div>
</template>

<script lang="ts" setup>
import { hasPermission } from '@/directives/permission/hasPermi'
import * as WorkApi from '@/api/work'
import { CaretBottom, Calendar, Search } from '@element-plus/icons-vue'
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import DeptTree from './component/DeptTree.vue'
import * as EquipmentApi from '@/api/operations/equipment'
import * as FocusApi from '@/api/operations/safeguard/focus'
import * as TicketsPushApi from '@/api/operations/ticketsPush'
import * as DynamicFormApi from '@/api/DynamicForm'
import { getDictList } from '@/api/infra/deviceDict' //设备来源接口
import * as UTILS from './utils'
import { getWarnTypeDescription } from '../list/utils/index'
import download from '@/utils/download'
import { getDeviceStatusText, getStatusClass3 } from './utils'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()
const activeTab = ref('first') // 默认选中的 tab
//序号自增
const tableIndex = (index: number) => {
  return (form.pageNo - 1) * form.pageSize + index + 1
}
const getOperatingUnit = async (type: string) => {
  const unitTypeData = await DynamicFormApi.getUnitType({ code: type })
  const unitData = await DynamicFormApi.getUnit({ typeId: unitTypeData })
  return unitData
}

// 统计设备
const equipmentBasis = ref({
  deviceTotal: 0,
  rate: '0',
  normal: 0,
  malfunction: 0,
  report: 0,
  deviceType: [
    {
      id: 0,
      name: '',
      deviceTotal: 0,
      rate: 0
    }
  ],
  project: [
    {
      id: 0,
      name: '',
      deviceTotal: 0,
      rate: 0
    }
  ],
  maintenanceUnit: [
    {
      id: 0,
      name: '',
      deviceTotal: 0,
      rate: 0
    }
  ],
  area: [
    {
      id: 0,
      name: '',
      deviceTotal: 0,
      rate: 0
    }
  ]
})
// const getEquipmentBasisFun = async () => {
//   const data = await WorkApi.getEquipmentBasis()
//   equipmentBasis.value = data
//   // console.log('打印设备数量', equipmentBasis.value)
// }
// 告警数量
const WarningCount = ref({
  allCount: 0,
  dealCount: 0,
  dealingCount: 0,
  noDealCount: 0,
  sourceCount: {}, //将key和val分别取出来
  todayCount: 0
})
// const getWarningCountFun = async () => {
//   const data = await WorkApi.getWarningCount()
//   WarningCount.value = data
//   // console.log('父组件打印告警数量', data)
// }

onMounted(() => {
  getSelect()
  getGroup() //获取左侧分组
  getTopGroup() //获取对话框顶部分组下拉框数据
  // getList() //取消默认的获取数据防止二次调用闪屏刷新
  getNoPageList() //获取对话框内表格数据
  getType()
  getProject() //获得所属项目
  getOperatingUnitFun() //获得运营商
  getMaintenanceUnitFun() //获得维保单位
  getAssetsSourceList() //获得设备来源
  // getWarnIdList() //获得告警等级
  window.addEventListener('keydown', handleGlobalKeyDown)
  if (leftList.value.length) {
    clickGrouping(leftList.value[0], form.isKey)
  }
  // // 统计设备基础
  // if (hasPermission(['infra:device-overview-info:query'])) {
  //   getEquipmentBasisFun()
  // }
  // // 告警数量
  // if (hasPermission(['infra:warn-assets:query'])) {
  //   getWarningCountFun()
  // }
})

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleGlobalKeyDown)
})
const handleGlobalKeyDown = (event) => {
  if (event.key === 'Enter') {
    onSubmit()
  }
}
/** 导出按钮操作 */
const exportLoading = ref(false)
const exportClick = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await FocusApi.exportList(form)
    download.excel(data, '用户数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
// 批量移出
// 选中群体移出
const allDeleteClick = async () => {
  if (form.ids.length) {
    try {
      // 删除的二次确认
      await message.removeConfirm()
      // 发起删除
      const removeForm = {
        groupId: null, //添加顶部下拉id
        ids: [] //添加右侧表格id数组
      }
      removeForm.ids = form.ids //多选框的id赋值
      await FocusApi.removeGroup(removeForm)
      message.success(t('common.removeSuccess'))
      // 刷新列表
      await getList()
    } catch {}
  } else {
    ElMessage.warning('请选择要移出的设备')
  }
}
// 表格数据
const tableData = ref([])
// 表单数据
const form = reactive({
  keyFlag: true,
  isKey: 1,
  assets: null,
  assetsCode: null, //设备编号
  assetsTypeId: [], //设备类型
  areaId: [], //所属区域
  pageNo: 1,
  pageSize: 10,
  deptId: null,
  groupId: null,
  // ====
  assetsName: null, //设备名称
  projectId: [], //所属项目
  operatingUnit: null, //运营商
  maintenanceUnit: null, //维保单位
  assetsSource: null, //设备来源
  warnId: null, //告警等级
  syncStatus: null, //同步状态
  ids: [] //多选框选中的id 批量导出使用
})

// 分页相关
const loading = ref(true)
const size = ref('default')
const background = ref(true)
const disabled = ref(false)
const total = ref(0)

// 控制哪些行可以被选择
const selectable = (row, index) => {
  // 例如：禁用第一行的选择
  return index !== -1
}

// 其他事件
const handleSizeChange = async (val: number) => {
  form.pageSize = val
  await getList()
}

const handleCurrentChange = async (val: number) => {
  form.pageNo = val
  await getList()
}

const handleClick = (id: number) => {
  sessionStorage.setItem('focusDetailId', id.toString())
  router.push({
    path: '/operations/safeguard/safeguardDetail'
    // query: { id: id.toString() } // 将 id 值传递到详情页
  })
}
const editClick = (id: number) => {
  // sessionStorage.setItem('focusDetailId', id.toString())
  const equipmentDetail = {
    id: id.toString(),
    title: '重点保障设备列表-编辑'
  }
  sessionStorage.setItem('equipmentDetail', JSON.stringify(equipmentDetail))

  router.push({
    path: '/operations/equipmentAdd'
    // query: { id: id.toString() } // 将 id 值传递到编辑页
  })
}
// 点击删除
const deleteClick = async (id: number) => {
  // 执行删除操作
  try {
    // 删除的二次确认
    await message.removeConfirm()
    // 发起删除
    const removeForm = {
      groupId: null, //添加顶部下拉id
      ids: [] //添加右侧表格id数组
    }
    removeForm.ids.push(id)
    await FocusApi.removeGroup(removeForm)
    message.success(t('common.removeSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}
const onSubmit = async () => {
  form.ids = [] //清除选中的数据
  await getList()
}

const resetForm = async (flag: boolean) => {
  form.assets = null
  form.assetsCode = null // 设备编号
  form.assetsTypeId = [] // 设备类型
  form.areaId = [] // 所属区域
  form.pageNo = 1 // 分页页码
  form.pageSize = 10 // 分页大小
  form.deptId = null // 部门ID
  form.assetsName = null // 设备名称
  form.projectId = [] // 所属项目
  form.operatingUnit = null // 运营商
  form.maintenanceUnit = null // 维保单位
  form.assetsSource = null // 设备来源
  form.warnId = null // 告警等级
  form.syncStatus = null // 同步状态
  form.ids = [] //选中的数据
  if (flag) {
    await getList()
  }
}

// 处理项目被点击
const handleDeptNodeClick = async (row: { [key: string]: any }) => {
  formSearch.projectId = [] // 清空旧数据

  if (row.children && row.children.length > 0) {
    // 如果是父节点，收集所有子节点的 id
    formSearch.projectId = row.children.map((child) => child.id)
  } else {
    // 如果是子节点，直接使用当前 id
    formSearch.projectId.push(row.id)
  }
  getNoPageList() // 触发查询
}

// const handleDeptNodeClick = async (row: { [key: string]: any }) => {
//   // console.log('父组件接收到的参数', row)
//   // formSearch.deptId = row.id
//   formSearch.projectId = []
//   formSearch.projectId.push(row.id)
//   getNoPageList()
// }

// 高级搜索相关
const showDialog = ref(false)

const showAdvancedSearch = () => {
  showDialog.value = true
  // 每次打开遮罩层时重置表单数据
  // resetForm()
}

const submitAdvancedSearch = async () => {
  // console.log('高级搜索表单数据:', form)
  form.ids = [] //清除选中的数据
  await getList()
  showDialog.value = false
}

const dialogTableData = ref([]) //对话框内表格数据

const dialogSubmit = () => {
  getNoPageList()
}
const cancelFun = async () => {
  // await restAddForm() //清空上次选中下拉框数据
  showDialogAdd.value = false
}
// 添加重点设备相关
const showDialogAdd = ref(false)
const addForm = reactive({
  groupId: null, //添加顶部下拉id
  ids: [] //添加右侧表格id数组
})
// 清空添加重点设备相关
const restAddForm = async () => {
  addForm.groupId = null
  addForm.ids = []
}
const okFun = async () => {
  if (!addForm.groupId) {
    ElMessage.warning('请选择一个分组在进行添加')
    return // 如果未选择分组，则终止后续操作
  }
  if (selectedRows.value.length) {
    // 清空旧数据
    addForm.ids = []
    selectedRows.value.forEach((item) => {
      addForm.ids.push(item.id)
    })
    // 调用 API 更新数据
    const data = await FocusApi.addList(addForm)
    if (data === true) {
      ElMessage.success('添加成功')
    } else {
      ElMessage.error('操作失败')
    }
    showDialogAdd.value = false
    // 刷新列表
    resetForm(true) // 已有的重置方法，确保清除所有筛选条件
    await getList()
  } else {
    ElMessage.warning('请选择要添加的设备')
  }
}

// 多选框相关
const selectedRows = ref([]) // 选中的行数据
// 处理选中行变化
const handleSelectionChange = (rows) => {
  selectedRows.value = rows
  // 有选中的进行赋值用于导出使用
  if (selectedRows.value.length) {
    form.ids = rows.map((item) => item.id)
  } else {
    form.ids = [] //没有选中的置空
  }
  // console.log('选中的行数据:', selectedRows.value)
}

// 获取搜索下拉列表数据
const getSelect = async () => {
  const res = await FocusApi.getSelect()
  // console.log('获取设备类型下拉列表数据:', res)
}
const isFlag = reactive({
  guaranteeFlag: true //是否正在保障,示例值(true)
})
const leftList = ref([]) //左侧分组数据
const navClick = async (num: number) => {
  form.groupId = null //切换时也清除掉所选中的分组
  if (num === 1) {
    activeTab.value = 'first'
    form.isKey = 1
  } else {
    activeTab.value = 'second'
    form.isKey = 0
  }
  isFlag.guaranteeFlag = num === 1 ? true : false
  await getGroup()
  await getList() //重新获取列表数据
}
// 获得左侧分组数据列表 传参获取左侧列表 不传参获取弹窗顶部下拉框数据
const getGroup = async () => {
  const res = await FocusApi.getGroup(isFlag)
  // console.log('左侧分组数据', res)
  leftList.value = res
  if (leftList.value.length) {
    clickGrouping(leftList.value[0], form.isKey)
  }
}
const topGroup = ref([])
const getTopGroup = async () => {
  const res = await FocusApi.getGroup({})
  // console.log('顶部分组数据', res)
  topGroup.value = res
}

const selectedUnit = ref(null) // 用于存储当前选中的单位
// 点击某一分组
const clickGrouping = async (item: any, num: number) => {
  selectedUnit.value = item // 更新选中的单位
  // console.log('点击了分组', item, flag)
  form.groupId = item.id //赋值当前点的左侧列表id查询
  addForm.groupId = item.id //赋值添加遮罩层的下拉框值(点击左侧列表赋值弹窗点击弹窗不赋值外侧)
  onSubmit()
  // 查id item.id
}
// 获得设备类型 顶部下拉框
const cascaderProps = reactive({
  value: 'id',
  label: 'name',
  children: 'children',
  multiple: true,
  checkStrictly: true,
  emitPath: false
})

// 高级搜索项目列表树状下拉
const cascaderProps2 = reactive({
  value: 'id',
  label: 'projectName',
  children: 'children',
  multiple: true,
  checkStrictly: true,
  emitPath: false
})

const typeList = ref([])
const getType = async () => {
  const data = await TicketsPushApi.getType()
  // console.log('设备类型typeList', data)
  typeList.value = data
}

const projectList = ref([]) //所属项目
const getProject = async () => {
  const data = await DynamicFormApi.getBelongsProject()
  // console.log('所属项目', data)
  projectList.value = data
}

const operatingUnitList = ref([]) //运营商/运营单位
const getOperatingUnitFun = async () => {
  const data = await getOperatingUnit('operation')
  // console.log('运营商/运维单位', data)
  operatingUnitList.value = data
}
const maintenanceUnitList = ref([]) //维保单位
const getMaintenanceUnitFun = async () => {
  const data = await getOperatingUnit('maintenance')
  // console.log('维保单位', data)
  maintenanceUnitList.value = data
}
const assetsSourceList = ref([]) //设备来源
const getAssetsSourceList = async () => {
  const data = await getDictList('deviceSource')
  // console.log('设备来源', data)
  assetsSourceList.value = data
}

// 缓存已请求过的 warnType
const warnTypeCache = new Map()
const loadWarnTypeDescription = async (row) => {
  if (!row.warnTypeDescription && row.warnId) {
    if (warnTypeCache.has(row.warnId)) {
      row.warnTypeDescription = warnTypeCache.get(row.warnId)
    } else {
      try {
        const description = await getWarnTypeDescription(row.warnId)
        row.warnTypeDescription = description
        warnTypeCache.set(row.warnId, description)
      } catch (error) {
        // console.error('获取告警方式描述失败:', error)
        row.warnTypeDescription = '获取失败'
      }
    }
  }
}
// 获得分页数据
const getList = async () => {
  loading.value = true
  try {
    const data = await FocusApi.getList(form)
    // 初始化时不请求描述，只赋空值
    data.list.forEach((item) => {
      item.warnTypeDescription = null
    })
    total.value = data.total
    tableData.value = data.list
  } finally {
    loading.value = false
  }
  // 设备接口 获取设备详细信息
  try {
    const deviceObj = await EquipmentApi.postOverview(form)
    equipmentBasis.value = deviceObj
    // console.log('equipmentBasis.value', equipmentBasis.value)
  } finally {
    loading.value = false
  }
  // 告警接口 获取告警详细信息
  try {
    const deviceObj = await EquipmentApi.getWarn(form)
    WarningCount.value = deviceObj
    // console.log('WarningCount.value', WarningCount.value)
  } finally {
    loading.value = false
  }
}
// 对话框相关==========
// 弹窗搜索表单
const formSearch = reactive({
  keyFlag: true,
  assetsCode: null,
  assetsTypeId: [],
  areaId: [],
  projectId: [],
  deptId: '' //左侧侧边栏点击存储id
})
// 获取对话框内不分页表格数据
const getNoPageList = async () => {
  const data = await TicketsPushApi.getNoPageList(formSearch)
  // console.log('打印不分页数据', data)
  dialogTableData.value = data
}

// 重置对话框内表单数据
const restNoPage = () => {
  formSearch.assetsCode = null
  formSearch.assetsTypeId = []
  formSearch.areaId = []
  formSearch.projectId = []
  formSearch.deptId = ''
  getNoPageList()
}
const addClick = async () => {
  showDialogAdd.value = true
  await resetForm(true) //调用重置函数重新获取全部数据进行添加操作
  await restNoPage() //调用重置函数清空上次对话框内的操作
  // await restAddForm() //清空上次选中下拉框数据(不清空防止点击左侧打开清空掉)
  await getList()
  // selectArray.value = [] //清空数组在进行push防止数据叠加
  // okParams.deviceOverviewSetUnitVosList = [] //将提交的params也置为空
}
</script>

<style lang="scss" scoped>
.my-component {
  // width: 100% !important;
  // height: 100% !important;
  background-color: white;
  display: flex;
  flex-direction: column;
  .top-title {
    width: 100%;
    height: 60px;
    font-size: 18px;
    font-weight: bold;
    padding-left: 20px;
    display: flex;
    align-items: center;
  }
  .top-equipment {
    // 隐藏滚动条
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Edge */
    }
    width: 100%;
    height: 70px;
    // overflow-x: auto;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    gap: 10px; // 设置固定间距，例如 10px
    // overflow-x: auto;
    // padding: 0 20px;
    .top-equipment-main {
      // width: 200px;
      // height: 70px;
      flex: 1;
      background-color: #f7f8f8;
      display: flex;
      justify-content: space-between;
      .top-equipment-box {
        width: 130px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        .left-img {
          width: 40px;
          height: 40px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .right-txt {
          width: 50%;
          height: 100%;
          font-size: 13px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          :nth-child(1) {
            text-align: center;
          }
          :nth-child(2) {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
          }
        }
      }
      .grey-img {
        width: 64px;
        height: 64px;
        img {
          width: 64px;
          height: 64px;
        }
      }
    }
  }
  .main {
    // flex: 1;
    // display: flex;
    // padding-left: 20px;
    // height: 100%;
    // background-color: pink;
    .left-year {
      // width: 200px;
      // height: 580px;
      height: 100%;
      border: 1px solid #ccc;
      background-color: #f1f3f6;
      border-radius: 1%;
      // margin-right: 10px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      // 隐藏滚动条
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
      }
      .button-group {
        margin: 10px 0 10px 10px;
        width: 90%;
        height: 40px;
        background-color: #e3e6eb;
        border-radius: 60px;
        display: flex;
        justify-content: space-around;
        align-items: center;
      }

      .nav-button {
        width: 48%;
        height: 40px;
        border-radius: 20px;
        border: none;
        background-color: transparent;
        color: black;
        cursor: pointer;
        transition: background-color 0.3s;
      }

      .nav-button.active {
        background-color: white;
      }

      .tabs-content {
        flex-grow: 1;
        overflow-y: auto;
        overflow-y: auto;
        // 隐藏滚动条
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE 10+ */
        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Edge */
        }
        .tab-content {
          display: block;
          padding: 10px;
          .text-style {
            height: 20px !important;
            display: flex;
            align-items: center;
            cursor: pointer;
            margin-bottom: 5px; //左侧列表每个增加5px的间隔
          }
        }

        .tab-content.active {
          background-color: white;
        }

        .el-dropdown-menu .el-dropdown-item img.dropdown-image {
          width: 20px !important;
          height: 20px !important; /* 确保高度也是20px */
          margin-right: 8px; /* 添加一些右边距 */
        }
      }
      .el-dropdown-link {
        color: #606266;
        font-size: 14px;
      }
    }

    // .right-main {
    //   flex: 1;
    //   display: flex;
    //   flex-direction: column;
    //   // overflow-x: auto;
    //   min-width: 0; /* 允许内容被裁剪而不是挤压其他元素 */
    //   // 隐藏滚动条
    //   scrollbar-width: none; /* Firefox */
    //   -ms-overflow-style: none; /* IE 10+ */
    //   &::-webkit-scrollbar {
    //     display: none; /* Chrome, Safari, Edge */
    //   }
    //   .form-box {
    //     width: 100%;
    //     display: flex;
    //     flex-direction: row;
    //     align-items: center;
    //     margin-bottom: 10px;
    //     overflow-x: auto;
    //     .form-search {
    //       flex: 1;
    //       display: flex;
    //       flex-direction: row;
    //       align-items: center;
    //       .el-form-item {
    //         margin-right: 10px;
    //       }
    //     }
    //   }
    //   .right-table {
    //     width: 100%;
    //     flex: 1;
    //     padding-right: 1px; //去除黑线 操作栏没有贴满右边 导致展示部分文字形成黑线
    //     max-height: 560px;
    //     overflow-y: auto;
    //     :deep(.el-table__header th) {
    //       text-align: left;
    //     }
    //     :deep(.el-table td) {
    //       text-align: left;
    //     }
    //     .demo-pagination-block {
    //       margin-top: 10px;
    //     }
    //   }
    // }
    .demo-pagination-block {
      display: flex;
      justify-content: flex-end;
    }
  }
}
// 添加遮罩层样式
.add-dialog {
  width: 100%;
  height: 100%;
  header {
    width: 100%;
    height: 50px;
    border: solid 1px #ccc;
    // border-radius: 3px;
    display: flex;
    align-items: center;

    .el-form {
      width: 100%;
    }
    .el-form-item {
      width: 100%;
      margin: 2px 0 0 20px;
    }
    .el-select {
      width: 90%;
      margin-left: 20px;
    }
  }
  main {
    width: 100%;
    height: 400px;
    display: flex;
    justify-content: space-between;
    .dialog-left {
      width: 25%;
      height: 100%;
      border: solid 1px #ccc;
      border-radius: 3px;
      overflow-y: auto;
      // 隐藏滚动条
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
      }
      .ContentWrap-style {
        height: 100%;
        overflow-y: auto;
        // 隐藏滚动条
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE 10+ */
        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Edge */
        }
      }
    }
    .dialog-right {
      width: 74%;
      height: 100%;
      border: solid 1px #ccc;
      border-radius: 3px;
      .right-top {
        width: 100%;
        height: 50px;
        .el-form {
          width: 100%;
          padding: 10px 0 0 10px;
        }
      }
      .right-tabel {
        width: 100%;
        height: 350px;
        :deep(.el-table__header th) {
          text-align: left;
        }
        :deep(.el-table td) {
          text-align: left;
        }
        .table-style {
          width: 100%;
          height: 350px;
          overflow-y: auto;
        }
      }
    }
  }
  footer {
    margin-top: 10px;
    width: 100%;
    height: 50px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    .el-button {
      // width: 45%;
    }
  }
}
// 查询按钮样式
.text-button {
  color: #fff;
}
// 高级搜索底部按钮
.bottom-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  button {
    width: 100px;
    margin-right: 20px;
  }
}
// 状态文字颜色
.status-normal {
  color: green;
}

.status-fault {
  color: #ffd910;
}

.status-reported {
  color: orange;
}

.status-alarm {
  color: red;
}

// 左侧侧边栏高亮样式
/* 高亮样式 */
.highlight {
  background-color: #fff; /* 浅蓝色背景 */
  // color: #0000ff; /* 蓝色文字 */
  // font-weight: bold; /* 加粗字体 */
}
</style>
