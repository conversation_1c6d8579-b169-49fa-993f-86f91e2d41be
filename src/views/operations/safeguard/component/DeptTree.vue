<template>
  <div class="head-container">
    <!-- <el-input v-model="deptName" class="mb-20px" clearable placeholder="搜索项目">
      <template #prefix>
        <Icon icon="ep:search" />
      </template>
    </el-input> -->
  </div>

  <div class="head-container">
    <el-tree
      ref="treeRef"
      :data="deptList"
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
      :props="defaultProps"
      default-expand-all
      highlight-current
      node-key="id"
      @node-click="handleNodeClick"
    >
      <template #default="{ node }">
        <el-tooltip :content="node.label" placement="top" :disabled="node.label.length < 10">
          <span class="tree-node-text">
            {{ node.label.length > 10 ? node.label.slice(0, 10) + '...' : node.label }}
          </span>
        </el-tooltip>
      </template>
    </el-tree>
    <!-- <el-tree
      ref="treeRef"
      :data="deptList"
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
      :props="defaultProps"
      default-expand-all
      highlight-current
      node-key="id"
      @node-click="handleNodeClick"
    /> -->
  </div>
</template>

<script lang="ts" setup>
import { ElTree } from 'element-plus'
import * as ListApi from '@/api/operations/list'
import { defaultProps } from '@/utils/tree'

defineOptions({ name: 'SystemUserDeptTree' })

const deptName = ref('')
const deptList = ref<Tree[]>([]) // 树形结构
const treeRef = ref<InstanceType<typeof ElTree>>()
function handleTree(data: { [key: string]: any[] }): Tree[] {
  return Object.entries(data)
    .sort(([yearA], [yearB]) => parseInt(yearB) - parseInt(yearA)) // 对年份进行倒序排序
    .map(([year, projects]) => ({
      id: year, // 使用年份作为节点的 id
      name: year, // 使用年份作为节点的名称
      children: projects.map((project) => ({
        id: project.id.toString(), // 确保 id 是字符串类型
        name: project.projectName || '未知项目', // 使用 projectName 作为子节点的名称
        startDate: project.startDate.join('-'), // 可选：添加开始日期
        endDate: project.endDate.join('-') // 可选：添加结束日期
      }))
    }))
}
// function handleTree(data: { [key: string]: any[] }): Tree[] {
//   return Object.entries(data).map(([year, projects]) => ({
//     id: year, // 使用年份作为节点的 id
//     name: year, // 使用年份作为节点的名称
//     children: projects.map((project) => ({
//       id: project.id.toString(), // 确保 id 是字符串类型
//       name: project.projectName || '未知项目', // 使用 projectName 作为子节点的名称
//       startDate: project.startDate.join('-'), // 可选：添加开始日期
//       endDate: project.endDate.join('-') // 可选：添加结束日期
//     }))
//   }))
// }
// 获取左侧年份 只要开始年份固定type未0
const getYearList = async () => {
  const data = await ListApi.getYear({ type: 0 })
  deptList.value = []
  deptList.value.push(...handleTree(data))
  // console.log('拿到了左侧的开始年份', data)
}

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}

/** 处理部门被点击 */
const handleNodeClick = async (row: { [key: string]: any }, node: any) => {
  if (node.level === 1) {
    // 点击父节点：传递父节点和所有子节点
    const children = row.children || []
    emits('node-click', { ...row, children }) // 包含 children 数组
  } else {
    // 子节点：仅传递自己
    emits('node-click', row)
  }
}
// const handleNodeClick = async (row: { [key: string]: any }) => {
//   emits('node-click', row)
// }
const emits = defineEmits(['node-click'])

/** 监听deptName */
watch(deptName, (val) => {
  treeRef.value!.filter(val)
})

/** 初始化 */
onMounted(async () => {
  // await getTree()
  await getYearList()
})
</script>

<style lang="scss" scoped>
:deep(.el-tree-node__content) {
  margin-bottom: 3px; //左侧列表每个增加3px的间隔
}
</style>
