<template>
  <Dialog v-model="dialogVisible" title="导入" width="400">
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      action="#"
      :auto-upload="false"
      :disabled="formLoading"
      :headers="uploadHeaders"
      :limit="1"
      :on-exceed="handleExceed"
      accept=".xlsx, .xls"
      drag
    >
      <Icon icon="ep:upload" />
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <template #tip>
        <div class="el-upload__tip text-center">
          <!-- <div class="el-upload__tip">
            <el-checkbox v-model="updateSupport" />
            是否更新已经存在的用户数据
          </div> -->
          <span>仅允许导入 xls、xlsx 格式文件。</span>
          <el-link
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            type="primary"
            @click="importTemplate"
          >
            下载模板
          </el-link>
        </div>
      </template>
    </el-upload>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, defineExpose, defineEmits, nextTick } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import { getAccessToken, getTenantId } from '@/utils/auth'
import download from '@/utils/download'
import * as TicketsPushApi from '@/api/operations/ticketsPush'
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const uploadRef = ref()
const uploadHeaders = ref() // 上传 Header 头
const fileList = ref([]) // 文件列表
const updateSupport = ref(false) // 是否更新已经存在的用户数据
const props = defineProps({
  //deptId 部门ID,示例值(1)

  deptId: {
    type: Number,
    required: true
  },
  //deptType 运维单位=0，维保单位=1
  deptType: {
    type: Number,
    required: true
  }
})
const emit = defineEmits(['success']) // 定义 success 事件
/** 打开弹窗 */
const open = () => {
  dialogVisible.value = true
  updateSupport.value = false
  fileList.value = []
  resetForm()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const submitForm = async () => {
  if (fileList.value.length === 0) {
    message.error('请上传文件')
    return
  }
  formLoading.value = true
  const formData = new FormData()
  formData.append('file', fileList.value[0].raw)
  if (props.deptType !== null) {
    formData.append('deptType', props.deptType.toString())
  }
  if (props.deptId !== null) {
    formData.append('deptId', props.deptId.toString())
  }

  try {
    const res = await TicketsPushApi.importEquipmentSingle(formData)
    if (res.code !== 0) {
      // 拼接失败提示语
      let text = '导入失败: ' + res.msg
      message.alert(text)
      formLoading.value = false
      return
    }
    // 拼接成功提示语
    const data = res.data
    // console.log('走了成功路线', data)
    let text = ''

    text += '上传成功数量：' + data.updateAssetsCodes.length + '个\n'
    // text += '上传失败数量：' + data.failureAssetsCodes.length + '个\n'
    text += '上传失败数量：' + Object.keys(data.failureAssetsCodes).length + '个'
    // 增加对 failureAssetsCodes 的处理
    if (data.failureAssetsCodes && Object.keys(data.failureAssetsCodes).length > 0) {
      text += '失败信息\n'
      for (const code in data.failureAssetsCodes) {
        text += `${code}: ${data.failureAssetsCodes[code]}; \n`
      }
    }
    await message.alert(text) // 等待 alert 完成
    dialogVisible.value = false // 关闭弹出框
    emit('success') // 触发 success 事件
  } catch (error) {
    message.alert('导入失败')
  } finally {
    formLoading.value = false
  }
}

/** 文件数超出提示 */
const handleExceed = (): void => {
  message.error('最多只能上传一个文件！')
}

/** 重置表单 */
const resetForm = async (): Promise<void> => {
  formLoading.value = false
  await nextTick()
  uploadRef.value?.clearFiles()
}

/** 下载模板操作 */
const importTemplate = async () => {
  const res = await TicketsPushApi.exportSingleStencil()
  download.excel(res, '设备导入模板.xls')
}
</script>
