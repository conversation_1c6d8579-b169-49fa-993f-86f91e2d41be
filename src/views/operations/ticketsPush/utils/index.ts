export function transformData(data) {
  // 定义递归函数
  function transformItem(item) {
    // 创建一个新的对象，将 id 改为 label，将 name 改为 value
    const newItem = {
      label: item.name,
      value: item.id
    }
    // 如果有子节点，递归处理子节点
    if (item.children && item.children.length > 0) {
      newItem.children = item.children.map(transformItem)
    }
    return newItem
  }
  // 遍历数组，对每个元素调用递归函数
  return data.map(transformItem)
}
// 表格状态类
export function getStatusClass(status: number): string {
  switch (status) {
    case 0:
      return 'status-normal'
    case 1:
      return 'status-fault'
    case 2:
      return 'status-reporting'
    case 3:
      return 'status-alarm'
    default:
      return ''
  }
}

// 是否在保
export function identificationStatus(value) {
  switch (value) {
    case 0:
      return '正常'
    case 1:
      return '故障'
    case 2:
      return '报备'
    case 3:
      return '告警'
    default:
      return ''
  }
}
// 是否重点
export function identificationKey(value) {
  switch (value) {
    case 0:
      return '非重点'
    case 1:
      return '重点'
    default:
      return '未知'
  }
}
// 同步状态
export function identificationSyncStatus(value) {
  switch (value) {
    case 0:
      return '未同步'
    case 1:
      return '已同步'
    default:
      return '未知'
  }
}
//时间戳转化
export function timestampToDateString(timestamp: number): string {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}
