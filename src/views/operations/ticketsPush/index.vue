<template>
  <div class="mr-8">
    <ContentWrap class="my-component">
      <header>
        <p>工单推送列表</p>
      </header>
      <div class="main">
        <el-row :gutter="20">
          <el-col :span="4" :xs="24">
            <div class="left-year">
              <div class="button-group">
                <button
                  class="nav-button"
                  :class="{ active: activeTab === 'first' }"
                  @click="navClick(0)"
                >
                  运营商
                </button>
                <button
                  class="nav-button"
                  :class="{ active: activeTab === 'second' }"
                  @click="navClick(1)"
                >
                  维保单位
                </button>
              </div>
              <div class="tabs-content">
                <div id="first" class="tab-content" v-if="activeTab === 'first'">
                  <p
                    v-for="item in unitList"
                    :key="item.id"
                    class="text-style"
                    :class="{ highlight: selectedUnit === item }"
                    @click="siftClick(item)"
                  >
                    <img src="@/assets/imgs/icons/date.png" alt="" class="dropdown-image" />
                    <el-tooltip
                      :content="item?.name"
                      placement="top"
                      :disabled="item?.name.length <= 10"
                    >
                      <span class="el-dropdown-link">{{
                        item?.name.length > 10 ? item?.name.slice(0, 10) + '...' : item?.name
                      }}</span>
                    </el-tooltip>
                  </p>
                </div>
                <div id="second" class="tab-content" v-if="activeTab === 'second'">
                  <p
                    v-for="item in unitList"
                    :key="item.id"
                    class="text-style"
                    :class="{ highlight: selectedUnit === item }"
                    @click="siftClick(item)"
                  >
                    <img src="@/assets/imgs/icons/date.png" alt="" class="dropdown-image" />
                    <el-tooltip
                      :content="item?.name"
                      placement="top"
                      :disabled="item?.name.length <= 10"
                    >
                      <span class="el-dropdown-link">{{
                        item?.name.length > 10 ? item?.name.slice(0, 10) + '...' : item?.name
                      }}</span>
                    </el-tooltip>
                  </p>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="20" :xs="24">
            <!-- 右侧盒子 -->
            <div class="right-main">
              <!-- 表单部分 -->
              <ContentWrap class="form-box">
                <el-form
                  ref="queryFormRef"
                  :inline="true"
                  :model="form"
                  class="form-search flex flex-wrap items-start -mb-15px"
                >
                  <el-form-item label="" class="!mr-3">
                    <el-input
                      v-model="form.assets"
                      class="!w-240px"
                      clearable
                      placeholder="输入设备编号/名称"
                    />
                  </el-form-item>
                  <el-form-item label="设备类型" class="!mr-3">
                    <el-cascader
                      v-model="form.assetsTypeId"
                      :options="typeList"
                      :props="cascaderProps"
                      collapse-tags
                      clearable
                    />
                  </el-form-item>
                  <el-form-item label="区域" prop="areaId" class="!mr-3">
                    <Dictionary
                      v-model="form.areaId"
                      type="cascader"
                      width="240px"
                      dict-type="oamArea"
                      :cascader-props="{
                        multiple: true,
                        checkStrictly: true,
                        label: 'name',
                        value: 'id'
                      }"
                      :max-collapse-tags="2"
                      placeholder="请选择区域"
                    />
                  </el-form-item>

                  <div class="min-w-[600px] flex-1 flex justify-between mb-[18px] pl-2">
                    <div>
                      <XButton
                        title="查询"
                        preIcon="ep:search"
                        gradient
                        @click="onSubmit"
                        v-hasPermi="['system:dept:query']"
                      />
                      <el-button @click="resetForm"><Icon icon="ep:refresh" />重置</el-button>
                    </div>
                    <div>
                      <el-button
                        plain
                        @click="importClick"
                        v-hasPermi="['infra:device-overview-info:import']"
                      >
                        <Icon icon="ep:upload" /> 导入
                      </el-button>
                      <el-button
                        plain
                        @click="handleExport"
                        :loading="exportLoading"
                        v-hasPermi="['infra:device-overview-info:export']"
                      >
                        <Icon icon="ep:download" />导出
                      </el-button>
                      <el-button
                        plain
                        @click="addClick"
                        v-hasPermi="['infra:device-overview-info:update']"
                      >
                        <Icon icon="ep:plus" />
                        添加
                      </el-button>
                      <el-button
                        plain
                        @click="allDeleteClick"
                        v-hasPermi="['infra:device-overview-info:update']"
                      >
                        <Icon icon="ep:delete" />
                        移出
                      </el-button>
                    </div>
                  </div>
                </el-form>
              </ContentWrap>

              <!-- 表格部分 -->
              <div class="right-table">
                <el-table
                  ref="tableRef"
                  :data="tableData"
                  height="530"
                  v-loading="loading"
                  style="width: 100%; margin-bottom: 10px"
                  @selection-change="handleSelectionChange('tableData', $event)"
                  class="table-style"
                  :header-cell-style="{
                    'background-color': '#F4F6F8',
                    'font-size': '14px',
                    color: '#3B3C3D'
                  }"
                >
                  <!-- 多选框列 -->
                  <el-table-column type="selection" :selectable="selectable" min-width="55" />
                  <!-- 其他列 -->

                  <el-table-column type="index" :index="tableIndex" label="序号" min-width="60" />
                  <el-table-column
                    prop="assetsCode"
                    label="设备编号"
                    min-width="150"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="assetsName"
                    label="设备名称"
                    min-width="150"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="assetsTypeName"
                    label="设备类型"
                    min-width="100"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="area"
                    label="所属区域"
                    min-width="100"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="projectName"
                    label="所属项目"
                    min-width="120"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="operatingUnitName"
                    label="运营商"
                    min-width="120"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="maintenanceUnitName"
                    label="维保单位"
                    min-width="120"
                    show-overflow-tooltip
                  />
                  <el-table-column prop="ip" label="ip" min-width="100" show-overflow-tooltip />
                  <el-table-column
                    prop="assetsSourceName"
                    label="设备来源"
                    min-width="120"
                    show-overflow-tooltip
                  />

                  <el-table-column
                    prop="warnName"
                    label="告警方式"
                    min-width="90"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <el-tooltip
                        :content="scope.row.warnTypeDescription"
                        placement="top"
                        v-if="scope?.row?.warnName"
                      >
                        <span @mouseenter="loadWarnTypeDescription(scope.row)">
                          {{ scope.row.warnName }}
                          <img src="@/assets/imgs/icons/faultTab2.png" alt="" />
                        </span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="deviceStatus"
                    label="设备状态"
                    min-width="90"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <div :class="getStatusClass(scope.row.deviceStatus)">
                        {{ identificationStatus(scope.row.deviceStatus) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="date" label="操作" min-width="100" fixed="right">
                    <template #default="scope">
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click="handleClick(scope.row.id)"
                        v-hasPermi="['system:operate-log:query']"
                        >详情</el-button
                      >
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click="deleteClick(scope.row.assetsCode)"
                        v-hasPermi="['infra:device-overview-info:update']"
                        >移出</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 分页部分 -->
                <div class="demo-pagination-block">
                  <el-pagination
                    v-model:current-page="form.pageNo"
                    v-model:page-size="form.pageSize"
                    :page-sizes="[10, 20, 30, 50, 100]"
                    :size="size"
                    :disabled="disabled"
                    :background="background"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  />
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 添加遮罩层 -->
      <el-dialog class="add-dialog" v-model="showDialogAdd" title="添加设备" width="75%">
        <main>
          <div class="dialog-left">
            <el-table
              ref="dialogLeftTableRef"
              :data="dialogLeftList"
              style="width: 100%; margin-bottom: 10px"
              @selection-change="handleSelectionChange('dialogLeftList', $event)"
              class="table-style"
            >
              <!-- 多选框列 -->
              <el-table-column type="selection" :selectable="selectable" width="55" />
              <!-- 其他列 -->
              <el-table-column
                prop="projectCode"
                label="项目编号"
                width="auto"
                show-overflow-tooltip
              />
              <el-table-column
                prop="projectName"
                label="项目名称"
                width="auto"
                show-overflow-tooltip
              />
            </el-table>
          </div>

          <div class="dialog-right">
            <!-- 右侧搜索表单 -->
            <div class="right-top">
              <el-form
                ref="queryFormRef"
                :inline="true"
                :model="formSearch"
                class="form-search flex flex-wrap items-start -mb-15px"
              >
                <el-form-item label="" class="!mr-3" style="width: 160px">
                  <el-input v-model="formSearch.assets" clearable placeholder="输入设备编号/名称" />
                </el-form-item>
                <el-form-item label="设备类型" class="!mr-3" style="width: 200px">
                  <el-cascader
                    v-model="formSearch.assetsTypeId"
                    :options="typeList"
                    :props="cascaderProps"
                    collapse-tags
                    clearable
                  />
                </el-form-item>
                <el-form-item label="区域" prop="areaId" class="!mr-3">
                  <Dictionary
                    v-model="formSearch.areaId"
                    type="cascader"
                    width="160px"
                    dict-type="oamArea"
                    :cascader-props="{
                      multiple: true,
                      checkStrictly: true,
                      label: 'name',
                      value: 'id'
                    }"
                    :max-collapse-tags="2"
                    placeholder="请选择区域"
                  />
                </el-form-item>
                <el-form-item>
                  <XButton
                    class="text-button"
                    preIcon="ep:search"
                    title="查询"
                    width="75px"
                    gradient
                    @click="dialogSubmit"
                  />
                </el-form-item>
              </el-form>
            </div>
            <!-- 右侧表格 -->
            <div class="right-tabel">
              <el-table
                ref="dialogRightTableRef"
                :data="dialogTableData"
                style="width: 100%; margin-bottom: 10px"
                @selection-change="handleSelectionChange('dialogTableData', $event)"
                class="table-style"
              >
                <!-- 多选框列 -->
                <el-table-column type="selection" :selectable="selectable" width="55" />
                <!-- 其他列 -->
                <el-table-column
                  prop="assetsCode"
                  label="设备编号"
                  width="150"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="assetsName"
                  label="设备名称"
                  width="150"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="projectName"
                  label="所属项目"
                  width="120"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="operatingUnitName"
                  label="运营商"
                  width="120"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="maintenanceUnitName"
                  label="维保单位"
                  width="120"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="assetsTypeName"
                  label="设备类型"
                  width="120"
                  show-overflow-tooltip
                />
                <el-table-column prop="area" label="区域" width="120" />
              </el-table>
            </div>
            <!-- 右侧盒子结束↓ -->
          </div>
        </main>
        <footer>
          <el-button type="primary" @click="okFun">确认</el-button>
          <el-button @click="cancelFun">取消</el-button>
        </footer>
      </el-dialog>
    </ContentWrap>
  </div>

  <ImportSingle ref="importSingleRef" @success="getList" :deptId="deptId" :deptType="deptType" />
</template>

<script lang="ts" setup>
import ImportSingle from './components/ImportSingle.vue'
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import * as TicketsPushApi from '@/api/operations/ticketsPush'
import * as RegionalApi from '@/api/operations/regional'
import * as DynamicFormApi from '@/api/DynamicForm'
import { getStatusClass, identificationStatus } from './utils'
import { getWarnTypeDescription } from '../list/utils/index'
import download from '@/utils/download'
import { ElMessage } from 'element-plus'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
//序号自增
const tableIndex = (index: number) => {
  return (form.pageNo - 1) * form.pageSize + index + 1
}
// 初始化
onMounted(async () => {
  await getNavId('operation') //默认传入运营单位参数 必须先执行拿到id
  await getUnitList() //在根据id拿到左侧列表参数
  // getList()//不需要调用在后面的if判断里面调用
  getNoPageList()
  getType()
  getRegionalList()
  getDialogLeft()
  window.addEventListener('keydown', handleGlobalKeyDown)
  // 初始化后默认点击左侧列表第一个数据(不点击其他搜索条件永远为左侧第一条下面id的搜索)
  if (unitList.value.length > 0) {
    siftClick(unitList.value[0])
  } else {
    await getList() // 即使没有左侧单位，也要主动加载默认数据
  }
})

const cascaderProps = reactive({
  value: 'id',
  label: 'name',
  children: 'children',
  multiple: true,
  checkStrictly: true,
  emitPath: false
})

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleGlobalKeyDown)
})
const handleGlobalKeyDown = (event) => {
  if (event.key === 'Enter') {
    onSubmit()
  }
}

const navClick = async (num: number) => {
  if (num === 0) {
    activeTab.value = 'first'
    const data = await DynamicFormApi.getUnitType({ code: 'operation' })
    unitParams.typeId = data
    form.maintenanceUnit = '' //如果点击运营单位将维保单位搜索条件清空
    okParams.deptType = 0
  } else {
    activeTab.value = 'second'
    const data = await DynamicFormApi.getUnitType({ code: 'maintenance' })
    unitParams.typeId = data
    form.operatingUnit = '' //如果点击的维保单位将运营单位搜索条件清空
    okParams.deptType = 1
  }
  await getUnitList() //重新获取左侧列表数据
  await resetForm() //调用重置函数重新获取全部数据进行添加操作
  await getList() //重新获取列表
  // 判断是否有数据，如果有数据则点击第一条数据
  if (unitList.value.length > 0) {
    siftClick(unitList.value[0])
  } else {
    await getList() // 即使没有左侧单位，也要主动加载默认数据
  }
}

const unitList = ref([])

const unitParams = reactive({
  pageNo: 1,
  pageSize: 100,
  typeId: null
})
const getNavId = async (type: string) => {
  const data = await DynamicFormApi.getUnitType({ code: type })
  unitParams.typeId = data //拿到id赋值给unitParams用于查找运营/维护单位数据
}
// 打点

const getUnitList = async () => {
  const data = await TicketsPushApi.getUnitList(unitParams)
  // console.log('打印左侧单位列表数据', data)
  unitList.value = data
}
// 缓存已请求过的 warnType
const warnTypeCache = new Map()
const loadWarnTypeDescription = async (row) => {
  if (!row.warnTypeDescription && row.warnId) {
    if (warnTypeCache.has(row.warnId)) {
      row.warnTypeDescription = warnTypeCache.get(row.warnId)
    } else {
      try {
        const description = await getWarnTypeDescription(row.warnId)
        row.warnTypeDescription = description
        warnTypeCache.set(row.warnId, description)
      } catch (error) {
        // console.error('获取告警方式描述失败:', error)
        row.warnTypeDescription = '获取失败'
      }
    }
  }
}
const getList = async () => {
  loading.value = true
  try {
    const data = await TicketsPushApi.getList(form)
    // console.log('打印表格数据', data.list)
    // 提前解析告警方式描述
    // await Promise.all(
    //   data.list.map(async (item) => {
    //     item.warnTypeDescription = await getWarnTypeDescription(item?.warnId)
    //   })
    // )
    // 初始化时不请求描述，只赋空值
    data.list.forEach((item) => {
      item.warnTypeDescription = null
    })
    tableData.value = data?.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const getNoPageList = async () => {
  const data = await TicketsPushApi.getNoPageList(formSearch)
  // console.log('打印不分页数据', data)
  dialogTableData.value = data
}
const temporarilyID = ref() //暂存左侧ID的参数

const selectedUnit = ref(null) // 用于存储当前选中的单位
const siftClick = async (item) => {
  selectedUnit.value = item // 更新选中的单位
  if (activeTab.value === 'first') {
    form.operatingUnit = item.id
  } else if (activeTab.value === 'second') {
    form.maintenanceUnit = item.id
  }
  temporarilyID.value = item.id
  getList()
}

const addClick = async () => {
  showDialogAdd.value = true
  await resetForm() // 调用重置函数重新获取全部数据进行添加操作
  await restNoPage() // 调用重置函数清空上次对话框内的操作
  await getList()
  resetSelection() // 清空多选框的选中状态
  selectArray.value = [] // 清空数组在进行 push 防止数据叠加
  okParams.deviceOverviewSetUnitVosList = [] // 将提交的 params 也置为空
}

const tableRef = ref(null)
const dialogLeftTableRef = ref(null)
const dialogRightTableRef = ref(null)
const resetSelection = () => {
  selectArray.value = [] // 清空选中的数组

  // 清空表格的选中状态
  const tableRefs = [tableRef.value, dialogLeftTableRef.value, dialogRightTableRef.value]

  tableRefs.forEach((tableRef) => {
    if (tableRef) {
      tableRef.clearSelection()
    }
  })
}

const typeList = ref([])
const getType = async () => {
  const data = await TicketsPushApi.getType()
  // console.log('设备类型typeList', data)
  typeList.value = data
}
const regionalList = ref([])
const params = reactive({
  dictType: 'oamArea'
})
const getRegionalList = async () => {
  const data = await RegionalApi.getList(params)
  // console.log('区域数据', data)

  regionalList.value = data
}
const router = useRouter()
const activeTab = ref('first') // 默认选中的 tab
// 表格数据
const tableData = ref([])

// 表单数据
// 搜索表单数据
const form = reactive({
  project: null, //项目编号
  assets: null, //设备编号
  assetsTypeId: [], //设备类型
  areaId: [], //所属区域
  pageNo: 1,
  pageSize: 10,
  // deptId: '', //所属单位id左侧列表点击
  operatingUnit: '', //运营单位ID
  maintenanceUnit: '', //维保单位ID
  ids: [] //多选框选中的id 批量导出使用
})

// 分页相关
const loading = ref(true)
const total = ref(0)
const size = ref('default')
const background = ref(true)
const disabled = ref(false)

// 多选框相关
const selectArray = ref([]) //声明一个暂存多选框数据用于群体删除使用
const handleSelectionChange = async (flag, rows) => {
  // console.log('flag', flag)
  // console.log('多选框数据', rows)
  selectArray.value = rows //赋值暂存多选框数据用于群体删除使用
  if (flag === 'tableData') {
    // console.log('点击主页的多选')
    // 有选中的进行赋值用于导出使用
    if (selectArray.value.length) {
      form.ids = rows.map((item) => item.id)
    } else {
      form.ids = [] //没有选中的置空
    }
  } else if (flag === 'dialogLeftList') {
    let arr = []
    for (let i = 0; i < rows.length; i++) {
      arr.push(rows[i].id)
    }
    formSearch.projectId = arr
    const data = await TicketsPushApi.getNoPageList(formSearch)
    // const data = await TicketsPushApi.getNoPageList({ projectId: arr })
    dialogTableData.value = data
    // console.log('打印数据', data)
  } else if (flag === 'dialogTableData') {
    okParams.deviceOverviewSetUnitVosList.push({ assetsCode: rows[0].assetsCode })
    okParams.deptId = temporarilyID.value //将左侧暂存的ID赋值给提交所用的id
  }
}

// 控制哪些行可以被选择
const selectable = (row, index) => {
  // 例如：禁用第一行的选择
  return index !== -1
}

// 其他事件
const handleSizeChange = (val: number) => {
  form.pageSize = val
  getList()
}

const handleCurrentChange = (val: number) => {
  form.pageNo = val
  getList()
}

const handleClick = (id: number) => {
  sessionStorage.setItem('ticketsPushDetailId', id.toString())
  router.push({
    path: '/operations/ticketsPushDetail'
    // query: { id: id.toString() } // 将 id 值传递到详情页
  })
}
// 单个移出
const deleteClick = async (assetsCode: string) => {
  okParams.deviceOverviewSetUnitVosList = [] //先重置为空在进行添加所需删除的操作
  okParams.deviceOverviewSetUnitVosList.push({
    assetsCode: assetsCode
  })
  // 执行移出操作
  try {
    // 删除的二次确认
    await message.removeConfirm()
    // 发起删除
    await await TicketsPushApi.updateItem(okParams)
    message.success(t('common.removeSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}
// 选中群体移出
const allDeleteClick = async () => {
  // console.log('点击了群体移出', selectArray.value)
  okParams.deviceOverviewSetUnitVosList = [] //先重置为空在进行添加所需删除的操作
  if (selectArray.value.length) {
    // 遍历 selectArray.value，将每个 assetsCode 添加到 deviceOverviewSetUnitVosList 中
    selectArray.value.forEach((item) => {
      okParams.deviceOverviewSetUnitVosList.push({
        assetsCode: item.assetsCode
      })
    })
    // console.log('更新后的 deviceOverviewSetUnitVosList:', okParams.deviceOverviewSetUnitVosList)
    await message.removeConfirm()
    await await TicketsPushApi.updateItem(okParams)
    message.success(t('common.removeSuccess'))
    form.ids = [] //清空ids防止影响查询数据
    // 刷新列表
    await getList()
  } else {
    ElMessage.warning('请选择要移出的设备')
  }
}

const onSubmit = () => {
  form.ids = [] //选中的数据置为空
  getList()
}

const resetForm = () => {
  form.project = null //设备编号
  form.assets = null //设备编号
  form.assetsTypeId = [] //设备类型
  form.areaId = [] //所属区域
  form.pageNo = 1
  form.pageSize = 10
  // form.operatingUnit = ''//重置时不重置左侧选中
  // form.maintenanceUnit = ''
  form.ids = [] //选中的数据
  getList()
}
const restNoPage = () => {
  formSearch.project = ''
  formSearch.assets = ''
  formSearch.assetsTypeId = []
  formSearch.areaId = []
  formSearch.projectId = []
  getNoPageList()
}
// 添加资产相关=======
const showDialogAdd = ref(false)
const addForm = reactive({
  grouping: ''
})
// 弹窗搜索表单
const formSearch = reactive({
  project: '',
  assets: '',
  assetsTypeId: [],
  areaId: [],
  projectId: []
})

const dialogTableData = ref([])

const dialogSubmit = () => {
  getNoPageList()
  // console.log('触发弹窗搜索')
}
const cancelFun = () => {
  showDialogAdd.value = false
}
// 更新数据
const okParams = reactive({
  deviceOverviewSetUnitVosList: [
    // {
    //   assetsCode: '' //弹窗右侧多选表格内数据会回填
    // }
  ],
  deptId: '', //弹窗右侧多选表格内数据会回填
  deptType: 0 //点击左侧列表表头传id 默认选中第一个是0
})
const okFun = async () => {
  // console.log('点击了确认')

  if (selectArray.value.length) {
    // 清空旧数据
    okParams.deviceOverviewSetUnitVosList = []
    // 遍历 selectArray.value，将每个 assetsCode 添加到 deviceOverviewSetUnitVosList 中
    selectArray.value.forEach((item) => {
      okParams.deviceOverviewSetUnitVosList.push({
        assetsCode: item.assetsCode
      })
    })
    if (okParams.deptId === '') {
      ElMessage.warning('请选择右侧列表')
      return
    }
    // console.log('更新后的 deviceOverviewSetUnitVosList:', okParams.deviceOverviewSetUnitVosList)
    // 调用 API 更新数据
    const data = await TicketsPushApi.updateItem(okParams)
    if (data === true) {
      ElMessage.success('添加成功')
    } else {
      ElMessage.error('操作失败')
    }
    showDialogAdd.value = false
    // 刷新列表
    await getList()
  } else {
    ElMessage.warning('请选择要添加的设备')
  }
}
/** 导入按钮操作*/
const importSingleRef = ref()
const showImportSingle = ref(false)
const deptId = ref(0) //导入的id
const deptType = ref(0) //运维单位=0，维保单位=1
const importClick = async () => {
  if (form.operatingUnit === '') {
    deptId.value = form.maintenanceUnit
    deptType.value = 1
  } else if (form.maintenanceUnit === '') {
    deptId.value = form.operatingUnit
    deptType.value = 0
  }
  showImportSingle.value = true
  importSingleRef.value.open()
}
/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TicketsPushApi.exportList(form)
    download.excel(data, '用户数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const dialogLeftList = ref([])
const getDialogLeft = async () => {
  const data = await TicketsPushApi.getDialogLeft()
  // console.log('左侧数据', data)
  dialogLeftList.value = data
}
</script>

<style lang="scss" scoped>
.my-component {
  // width: 100% !important;
  // height: 100% !important;
  background-color: white;
  display: flex;
  flex-direction: column;
  header {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    // border-bottom: 1px solid #ccc;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: bold;
    padding-left: 20px;
  }
  .top-equipment {
    width: 100%;
    height: 50px;
    // background-color: aquamarine;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    .el-tag {
      margin: 0px 2px;
    }
    .tag-txt {
      color: #ffc6c7;
    }
  }
  .main {
    // flex: 1;
    // display: flex;//去掉自身样式防止影响布局
    // padding-left: 20px;
    .left-year {
      // width: 200px;
      // height: 580px;
      height: 100%;
      border: 1px solid #ccc;
      background-color: #f1f3f6;
      border-radius: 1%;
      // margin-right: 10px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      // 隐藏滚动条
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
      }
      .button-group {
        margin: 10px 0 10px 10px;
        width: 90%;
        height: 40px;
        background-color: #e3e6eb;
        border-radius: 60px;
        display: flex;
        justify-content: space-around;
        align-items: center;
      }

      .nav-button {
        width: 48%;
        height: 40px;
        border-radius: 20px;
        border: none;
        background-color: transparent;
        color: black;
        cursor: pointer;
        transition: background-color 0.3s;
      }

      .nav-button.active {
        background-color: white;
      }

      .tabs-content {
        flex-grow: 1;
        overflow-y: auto;
        overflow-y: auto;
        // 隐藏滚动条
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE 10+ */
        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Edge */
        }
        .tab-content {
          display: block;
          padding: 10px;
          .text-style {
            height: 20px !important;
            display: flex;
            align-items: center;
            cursor: pointer;
            margin-bottom: 5px; //左侧列表每个增加5px的间隔
          }
        }

        .tab-content.active {
          background-color: white;
        }

        .el-dropdown-menu .el-dropdown-item img.dropdown-image {
          width: 20px !important;
          height: 20px !important; /* 确保高度也是20px */
          margin-right: 8px; /* 添加一些右边距 */
        }
      }
      .el-dropdown-link {
        color: #606266;
        font-size: 14px;
      }
    }

    // .right-main {
    //   flex: 1;
    //   display: flex;
    //   flex-direction: column;
    //   // overflow-x: auto;
    //   min-width: 0; /* 允许内容被裁剪而不是挤压其他元素 */

    //   .form-box {
    //     width: 100%;
    //     display: flex;
    //     flex-direction: row;
    //     align-items: center;
    //     margin-bottom: 10px;
    //     overflow-x: auto;
    //     // 隐藏滚动条
    //     // scrollbar-width: none; /* Firefox */
    //     // -ms-overflow-style: none; /* IE 10+ */
    //     // &::-webkit-scrollbar {
    //     //   display: none; /* Chrome, Safari, Edge */
    //     // }
    //     .form-search {
    //       flex: 1;
    //       display: flex;
    //       flex-direction: row;
    //       align-items: center;

    //       .el-form-item {
    //         margin-right: 10px;
    //       }
    //     }
    //   }

    //   .right-table {
    //     width: 100%;
    //     flex: 1;
    //     padding-right: 1px; //去除黑线 操作栏没有贴满右边 导致展示部分文字形成黑线
    //     max-height: 530px;
    //     overflow-y: auto;
    //     :deep(.el-table__header th) {
    //       text-align: left;
    //       // background-color: #e0f7fa !important; /* 表头背景颜色浅蓝色 */
    //     }
    //     :deep(.el-table td) {
    //       text-align: left;
    //     }
    //     .demo-pagination-block {
    //       margin-top: 10px;
    //       // position: absolute;
    //       // bottom: 0;
    //       // left: 0;
    //     }
    //   }
    // }
    .demo-pagination-block {
      display: flex;
      justify-content: flex-end;
    }
  }
}

// 添加遮罩层样式
.add-dialog {
  width: 100%;
  height: 100%;

  main {
    width: 100%;
    height: 400px;
    // border: solid 1px #ccc;
    display: flex;
    justify-content: space-between;
    .dialog-left {
      width: 35%;
      height: 100%;
      overflow-y: auto;
      // 隐藏滚动条
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
      }
      border: solid 1px #ccc;
      border-radius: 3px;
      :deep(.el-table__header th) {
        text-align: left;
      }
      :deep(.el-table td) {
        text-align: left;
      }
    }
    .dialog-right {
      width: 64%;
      height: 100%;
      border: solid 1px #ccc;
      border-radius: 3px;
      .right-top {
        width: 100%;
        height: 50px;
        // background-color: #ffc6c7;
        .el-form {
          width: 100%;
          padding: 10px 0 0 10px;
        }
      }

      .right-tabel {
        width: 100%;
        height: 350px;
        // background-color: #ffc6c7;
        :deep(.el-table__header th) {
          text-align: left;
        }
        :deep(.el-table td) {
          text-align: left;
        }
        .table-style {
          width: 100%;
          height: 350px;
          overflow-y: auto;
        }
      }
    }
  }
  footer {
    margin-top: 10px;
    width: 100%;
    height: 50px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    .el-button {
      // width: 45%;
    }
  }
}
// 查询按钮样式
.text-button {
  color: #fff;
}
// 状态文字颜色
.status-normal {
  color: green;
}

.status-alarm {
  color: orange;
}

.status-fault {
  color: red;
}

.status-reporting {
  color: #409eff;
}
// 左侧侧边栏高亮样式
/* 高亮样式 */
.highlight {
  background-color: #fff; /* 浅蓝色背景 */
  // color: #0000ff; /* 蓝色文字 */
  // font-weight: bold; /* 加粗字体 */
}
</style>
