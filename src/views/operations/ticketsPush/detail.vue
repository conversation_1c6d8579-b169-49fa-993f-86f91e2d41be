<template>
  <ContentWrap class="my-component">
    <header>
      <el-button class="ml-5 mr-5" type="primary" plain @click="goBack">返回</el-button>
      <p>工单推送列表-详情</p>
    </header>
    <main>
      <div class="main-left">
        <p class="title-box">设备详情</p>
        <div class="detail-box">
          <PrivateShow :id="params.id" />
        </div>
      </div>
      <div class="main-right">
        <header class="border-style">
          <img v-if="moreDetail?.pictureUrl" :src="moreDetail?.pictureUrl" alt="" />
          <img v-else src="@/assets/imgs/device.png" alt="" />
        </header>
        <footer class="border-style">
          <div class="edit-time" v-for="item in logList" :key="item">
            <p>由{{ item.userName }}{{ item.subType }}</p>
            <p>{{ UTILS.timestampToDateString(item.createTime) }}</p>
          </div>
        </footer>
      </div>
    </main>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, onBeforeRouteLeave } from 'vue-router'
import * as TicketsPushApi from '@/api/operations/ticketsPush'
import * as UTILS from './utils/index' //引入筛选的工具函数
import PrivateShow from '@/components/PrivateShow/index.vue'
const route = useRoute()
const params = reactive({ id: '' })
const detailList = ref({})
const moreDetail = ref({})
onMounted(async () => {
  const storedId = sessionStorage.getItem('ticketsPushDetailId')
  if (storedId) {
    params.id = storedId
    await getDetail()
    await getLog()
  } else {
    ElMessage.warning('已离开页面,请重新选择项目')
  }
  // // 从路由查询参数中获取 id 值
  // params.id = route.query.id as string
  // await getDetail()
  // await getLog()
})

// 使用 beforeRouteLeave 导航守卫
onBeforeRouteLeave((to, from) => {
  // 清除 sessionStorage 中的 ticketsPushDetailId
  // sessionStorage.removeItem('ticketsPushDetailId')
})

const getDetail = async () => {
  const res = await TicketsPushApi.getDetail(params)
  detailList.value = res //拿包含外层的数据
  moreDetail.value = res.deviceOverviewInfo //不包含外层的数据
  // console.log('打印详情数据', moreDetail.value)
  // 重新赋值方便循环渲染
  descriptionsItems.value = [
    { label: '设备编号:', value: moreDetail.value?.assetsCode },
    { label: '所属项目:', value: moreDetail.value?.projectName },
    { label: '设备类型:', value: moreDetail.value?.assetsTypeName },
    { label: '区域位置:', value: moreDetail.value?.area },
    { label: '供应商', value: moreDetail.value?.supplierName },
    { label: '经度:', value: moreDetail.value?.longitude },
    { label: '纬度:', value: moreDetail.value?.latitude },
    { label: '规格型号:', value: moreDetail.value?.modelName },
    { label: '质保状态:', value: UTILS.identificationStatus(moreDetail.value?.warrantyStatus) },
    { label: '设备来源:', value: moreDetail.value?.assetsSourceName },
    { label: 'IP地址:', value: moreDetail.value?.ip },
    { label: '质保单位:', value: moreDetail.value?.maintenanceUnitName }
  ]
  descriptionsItems2.value = [
    { label: '设备名称:', value: moreDetail.value?.assetsName },
    { label: '所属单位:', value: moreDetail.value?.deptName },
    { label: '同步状态:', value: UTILS.identificationSyncStatus(moreDetail.value?.syncStatus) },
    { label: '详细位置:', value: moreDetail.value?.address },
    { label: '二维码地址:', value: moreDetail.value?.qrCode },
    { label: '告警等级:', value: moreDetail.value?.warnName },
    { label: '质保日期:', value: moreDetail.value?.warrantyDate },
    { label: '创建时间:', value: UTILS.timestampToDateString(moreDetail.value?.createTime) },
    { label: '设备标签:', value: moreDetail.value?.label },
    { label: '是否重点:', value: UTILS.identificationKey(moreDetail.value?.isKey) },
    { label: '运营单位:', value: moreDetail.value?.operatingUnitName },
    { label: '维保单位:', value: moreDetail.value?.maintenanceUnitName }
  ]
}

// 左侧默认数据
const descriptionsItems = ref([])
// 右侧默认数据
const descriptionsItems2 = ref([])

// 返回上一页
const goBack = () => {
  window.history.back()
}
// 获得日志
const logList = ref([])
const getLog = async () => {
  const data = await TicketsPushApi.getLog({ bizId: params.id, type: 'DEVICE_MANAGE_CENTER' })
  // console.log('日志', data)
  logList.value = data.list
}
</script>

<style lang="scss" scoped>
.border-style {
  border: 1px solid #ccc;
  border-radius: 1%;
}

.my-component {
  // width: 100%;
  // height: 100%;
  background-color: white;
  header {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    margin-bottom: 10px;
  }
  main {
    width: 100%;
    height: 680px;
    display: flex;
    .main-left {
      flex: 1;
      margin-right: 10px;
      // margin-left: 20px;

      .title-box {
        // width: 100%;
        // height: 40px;
        // line-height: 40px;
        // margin-left: 40px;
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        position: relative;
        font-family: Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #3b3c3d;
        text-align: left;
        font-style: normal;
        text-transform: none;
        line-height: 24px;
        margin: 3px 0;
        padding-left: 28px;

        &::before {
          margin: 10px 0 0 10px;
          content: '';
          position: absolute;
          left: 0;
          top: 6px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #005fff;
          border: 2px solid #d4e4ff;
        }
      }
      .detail-box {
        width: 97%;
        height: 88%;
        margin: 0 12px;
        display: flex;
        .detail-left {
          width: 48%;
        }
        .detail-right {
          width: 48%;
        }
      }
    }
    .main-right {
      // background-color: navajowhite;
      width: 250px;
      height: 100%;
      border: 1px solid #ccc;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;
      header {
        width: 95%;
        height: 25%;
        // background-color: pink;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 95%;
          height: 90%;
        }
      }
      main {
        width: 95%;
        height: 25%;
        // background-color: lightcoral;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 95%;
          height: 90%;
        }
      }
      footer {
        width: 95%;
        height: 72%;
        // background-color: antiquewhite;
        overflow-y: auto;
        &::-webkit-scrollbar {
          width: 6px; // 滚动条宽度
          height: 6px; // 滚动条高度
        }
        &::-webkit-scrollbar-thumb {
          background-color: #c1c1c1c1; // 滚动条颜色
          border-radius: 3px; // 滚动条圆角
        }

        &::-webkit-scrollbar-track {
          background-color: #f1f1f1f1; // 滚动条轨道颜色
          border-radius: 3px; // 滚动条轨道圆角
        }
        .edit-time {
          margin-left: 20px;
          width: 90%;
          height: 70px;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          // background-color: palegreen;
          margin-bottom: 20px;
        }
      }
    }
  }
}
</style>
