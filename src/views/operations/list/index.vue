<template>
  <div class="pr-6">
    <ContentWrap class="my-component">
      <!-- 对话框 -->
      <Dialog v-model="showDialog" :title="dialogTitle" width="30%">
        <el-form :model="form" :rules="rules" ref="formRef">
          <el-form-item
            label="项目编号:"
            prop="projectCode"
            :label-width="formLabelWidth"
            :class="{ 'is-disabled': !isAddMode }"
          >
            <el-input v-model="form.projectCode" autocomplete="off" :disabled="!isAddMode" />
          </el-form-item>
          <el-form-item label="项目名称:" prop="projectName" :label-width="formLabelWidth">
            <el-input v-model="form.projectName" autocomplete="off" />
          </el-form-item>
          <el-form-item label="项目类型:" prop="projectType" :label-width="formLabelWidth">
            <el-select v-model="form.projectType" placeholder="请选择">
              <el-option label="建设项目" :value="1" />
              <el-option label="运维项目" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="运维类型:" prop="oamType" :label-width="formLabelWidth">
            <el-select v-model="form.oamType" placeholder="请选择">
              <el-option label="全运维" :value="1" />
              <el-option label="抢修运维" :value="2" />
              <el-option label="网管运维" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="告警方式:" prop="warnType" :label-width="formLabelWidth">
            <el-select v-model="form.warnType" placeholder="请选择">
              <el-option
                v-for="item in selectList"
                :key="item"
                :label="item.warnTypeName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="维护期:" prop="maintainPeriod" :label-width="formLabelWidth">
            <el-date-picker
              v-model="form.maintainPeriod"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleDateChange"
            />
          </el-form-item>
          <el-form-item label="项目金额:" prop="projectAmount" :label-width="formLabelWidth">
            <el-input v-model="form.projectAmount" autocomplete="off" />
            <div class="append-icon-text">元</div>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="handleAdd">确认</el-button>
            <el-button size="large" @click="showDialog = false">取消</el-button>
          </span>
        </template>
      </Dialog>
      <!-- 对话框 -->
      <header>项目管理列表</header>

      <main>
        <el-row :gutter="20">
          <el-col :span="4" :xs="24">
            <div class="left-year">
              <div class="button-group">
                <button
                  class="nav-button"
                  :class="{ active: activeTab === 'first' }"
                  @click="navClick(0)"
                >
                  维保起始
                </button>
                <button
                  class="nav-button"
                  :class="{ active: activeTab === 'second' }"
                  @click="navClick(1)"
                >
                  维保结束
                </button>
              </div>
              <div class="tabs-content">
                <div id="first" class="tab-content" v-if="activeTab === 'first'">
                  <p
                    v-for="item in allYear"
                    :key="item.id"
                    class="text-style"
                    :class="{ highlight: selectedUnit === item }"
                    @click="clickYear(item, 0)"
                  >
                    <img src="@/assets/imgs/icons/date.png" alt="" class="dropdown-image" />
                    <span class="el-dropdown-link">{{ item }}年</span>
                  </p>
                </div>
                <div id="second" class="tab-content" v-if="activeTab === 'second'">
                  <p
                    v-for="item in allYear"
                    :key="item.id"
                    class="text-style"
                    :class="{ highlight: selectedUnit === item }"
                    @click="clickYear(item, 1)"
                  >
                    <img src="@/assets/imgs/icons/date.png" alt="" class="dropdown-image" />
                    <span class="el-dropdown-link">{{ item }}年</span>
                  </p>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="20" :xs="24">
            <ContentWrap class="right-main">
              <ContentWrap class="form-box">
                <el-form
                  ref="queryFormRef"
                  :inline="true"
                  :model="queryParams"
                  class="form-search flex flex-wrap items-start -mb-15px"
                >
                  <el-form-item label="" prop="project" class="!mr-3">
                    <el-input
                      v-model="queryParams.project"
                      class="!w-240px"
                      clearable
                      placeholder="输入项目编号/名称"
                    />
                  </el-form-item>
                  <el-form-item label="项目类型" prop="projectType" class="!mr-3">
                    <el-select
                      v-model="queryParams.projectType"
                      class="!w-140px"
                      clearable
                      placeholder="选择项目类型"
                    >
                      <el-option label="建设项目" value="1" />
                      <el-option label="运维项目" value="2" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="运维类型" prop="oamType" class="!mr-3">
                    <el-select
                      v-model="queryParams.oamType"
                      class="!w-140px"
                      clearable
                      placeholder="选择运维类型"
                    >
                      <el-option label="全运维" value="1" />
                      <el-option label="抢修运维" value="2" />
                      <el-option label="网管运维" value="3" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="告警级别" prop="warnType" class="!mr-3">
                    <el-select
                      v-model="queryParams.warnType"
                      class="!w-140px"
                      clearable
                      placeholder="选择告警级别"
                    >
                      <el-option
                        v-for="item in selectList"
                        :key="item.id"
                        :label="item.warnTypeName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="状态" prop="status" class="!mr-3">
                    <el-select
                      @change="handleStatusChange"
                      v-model="status"
                      class="!w-140px"
                      clearable
                      placeholder="请选择状态"
                    >
                      <el-option label="在保" value="under_warranty" />
                      <el-option label="过保" value="expired" />
                      <el-option label="临近过保" value="expiring" />
                    </el-select>
                  </el-form-item>

                  <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
                    <div>
                      <XButton
                        title="查询"
                        preIcon="ep:search"
                        v-hasPermi="['infra:oam-project:query']"
                        gradient
                        @click="onSubmit"
                      />
                      <el-button @click="resetForm"><Icon icon="ep:refresh" />重置</el-button>
                    </div>
                    <div>
                      <el-button
                        plain
                        @click="openDialog(true)"
                        v-hasPermi="['infra:oam-project:create']"
                      >
                        <Icon icon="ep:plus" />
                        新增
                      </el-button>
                      <el-button
                        plain
                        @click="importClick"
                        v-hasPermi="['infra:oam-project:import']"
                      >
                        <Icon icon="ep:upload" /> 导入
                      </el-button>
                      <el-button
                        plain
                        @click="handleExport"
                        :loading="exportLoading"
                        v-hasPermi="['infra:oam-project:export']"
                      >
                        <Icon icon="ep:download" />导出
                      </el-button>
                    </div>
                  </div>
                </el-form>
              </ContentWrap>

              <div class="right-table">
                <el-table
                  :data="itemList"
                  height="530"
                  v-loading="loading"
                  style="width: 100%; margin-bottom: 10px"
                  :header-cell-style="{
                    'background-color': '#F4F6F8',
                    'font-size': '14px',
                    color: '#3B3C3D'
                  }"
                >
                  <el-table-column type="index" :index="tableIndex" label="序号" min-width="60" />
                  <el-table-column
                    prop="projectCode"
                    label="项目编号"
                    min-width="150"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="projectName"
                    label="项目名称"
                    min-width="150"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="projectType"
                    label="项目类型"
                    min-width="100"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <span>{{ UTILS.formatProjectType(scope.row.projectType) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="oamType" label="运维类型" min-width="90">
                    <template #default="scope">
                      <span>{{ UTILS.formatOamType(scope.row.oamType) }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop="warnTypeName"
                    label="告警方式"
                    min-width="90"
                    trigger="hover"
                  >
                    <template #default="scope">
                      <el-tooltip
                        :content="scope.row.warnTypeDescription"
                        placement="top"
                        v-if="scope?.row?.warnTypeName"
                      >
                        <span @mouseenter="loadWarnTypeDescription(scope.row)">
                          {{ scope.row.warnTypeName }}
                          <img src="@/assets/imgs/icons/faultTab2.png" alt="" />
                        </span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="dateRange"
                    label="维护期"
                    min-width="200"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <span>{{ scope.row.startDate }}~{{ scope.row.endDate }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column prop="assetsCount" label="设备数" min-width="70" />
                  <el-table-column
                    prop="projectAmount"
                    label="项目金额"
                    min-width="90"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <span class="projectAmount-color" v-if="scope.row.projectAmount < 10000"
                        >{{ scope.row.projectAmount }}元</span
                      >
                      <span class="projectAmount-color" v-if="scope.row.projectAmount >= 10000"
                        >{{ scope.row.projectAmount / 10000 }}万元</span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="creatorNickname"
                    label="创建人"
                    min-width="100"
                    show-overflow-tooltip
                  />

                  <el-table-column prop="createTime" label="创建时间" min-width="180">
                    <template #default="scope">
                      <span>{{ UTILS.formatTimestamp(scope.row.createTime) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="safeguardFlag" label="状态" min-width="80">
                    <template #default="scope">
                      <span :style="{ color: scope.row.safeguardFlag ? '' : 'red' }">{{
                        UTILS.formatSafeguardFlag(scope.row.safeguardFlag, scope.row.nearFlag)
                      }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="date" label="操作" min-width="170" fixed="right">
                    <template #default="scope">
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click="importSingleClick(scope.row.id)"
                        v-hasPermi="['infra:device-overview-info:import']"
                        >设备导入</el-button
                      >
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click="goDetail(scope.row.id)"
                        v-hasPermi="['infra:oam-project:query']"
                        >详情</el-button
                      >
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click="openDialog(false, scope.row.id)"
                        v-hasPermi="['infra:oam-project:update']"
                      >
                        编辑
                      </el-button>

                      <el-button
                        v-hasPermi="['infra:oam-project:delete']"
                        link
                        type="primary"
                        size="small"
                        @click="deleteRow(scope.row.id)"
                        v-if="scope.row.assetsCount == 0"
                        >删除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>

                <div class="demo-pagination-block">
                  <el-pagination
                    v-model:current-page="queryParams.pageNo"
                    v-model:page-size="queryParams.pageSize"
                    :page-sizes="[10, 20, 30, 50, 100]"
                    :size="size"
                    :disabled="disabled"
                    :background="background"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  />
                </div>
              </div> </ContentWrap
          ></el-col>
        </el-row>
        <!-- 左侧盒子结尾 -->
        <!-- 右侧主题盒子结尾 -->
      </main>
    </ContentWrap>
  </div>
  <UserImportForm ref="importFormRef" @success="getList" />
  <ImportSingle ref="importSingleRef" @success="getList" :project-id="ProjectId" />
</template>

<script lang="ts" setup>
import UserImportForm from './components/UserImportForm.vue'
import ImportSingle from './components/ImportSingle.vue'
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import type { TabsInstance } from 'element-plus'
import { CaretBottom } from '@element-plus/icons-vue'
import download from '@/utils/download'
import * as UTILS from './utils'
import * as ListApi from '@/api/operations/list'
import type { ElForm } from 'element-plus'

const route = useRoute()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 处理状态选择变化
// const handleSafeguardFlagChange = (value) => {
//   const selectedLabel = (
//     event?.target?.selectedOptions[0]?.label || event?.target?.innerText
//   )?.trim()

//   if (selectedLabel === '临保') {
//     queryParams.nearFlag = true
//   } else {
//     queryParams.nearFlag = false
//   }

//   // 可以在这里触发搜索或其他操作
//   onSubmit()
// }

const status = ref() //临保状态
const handleStatusChange = (value) => {
  switch (value) {
    case 'under_warranty': // 在保
      queryParams.safeguardFlag = true
      queryParams.nearFlag = null
      break
    case 'expired': // 过保
      queryParams.safeguardFlag = false
      queryParams.nearFlag = null
      break
    case 'expiring': // 临保
      queryParams.safeguardFlag = true
      queryParams.nearFlag = true
      break
  }
}
//序号自增
const tableIndex = (index: number) => {
  return (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
}
// 弹窗部分↓
const showDialog = ref(false)
const isAddMode = ref(true) // 控制是否为新增模式
const form = ref({
  projectCode: '',
  projectName: '',
  projectType: '',
  oamType: '',
  warnType: '',
  maintainPeriod: [],
  projectAmount: '',
  startDate: '',
  endDate: '',
  id: '' // 添加 id 字段
})

// const formLabelWidth = '120px' //表格单格宽度

const rules = {
  projectCode: [{ required: true, message: '请输入项目编号', trigger: 'blur' }],
  projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  projectType: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
  oamType: [{ required: true, message: '请选择运维类型', trigger: 'change' }],
  warnType: [{ required: true, message: '请选择告警方式', trigger: 'change' }],
  maintainPeriod: [{ required: true, message: '请选择维护期', trigger: 'change' }],
  projectAmount: [{ required: true, message: '请输入项目金额', trigger: 'blur' }]
}
const formRef = ref<InstanceType<typeof ElForm> | null>(null)
const dialogTitle = ref('') // 对话框标题
const handleDateChange = (value) => {
  // console.log('选择的日期范围:', value) // 打印选择的日期范围
  if (value) {
    const start = new Date(value[0])
    const end = new Date(value[1])
    start.setDate(start.getDate() + 1) // 日期加一天
    end.setDate(end.getDate() + 1) // 日期加一天
    form.value.startDate = start.toISOString().split('T')[0]
    form.value.endDate = end.toISOString().split('T')[0]
    // console.log('开始日期:', form.value.startDate)
    // console.log('结束日期:', form.value.endDate)
  } else {
    form.value.startDate = ''
    form.value.endDate = ''
  }
}

// 判断新增编辑
const handleAdd = async () => {
  try {
    // 验证表单
    await formRef.value.validate()
    const rawData = toRaw(form.value) // 提取原始数据
    // console.log('打印新增/编辑数据', rawData)
    if (isAddMode.value) {
      // 新增时，确保不包含 id 等编辑字段
      const addData = {
        projectCode: rawData.projectCode,
        projectName: rawData.projectName,
        projectType: rawData.projectType,
        oamType: rawData.oamType,
        warnType: rawData.warnType,
        maintainPeriod: rawData.maintainPeriod,
        projectAmount: rawData.projectAmount,
        startDate: rawData.startDate,
        endDate: rawData.endDate
      }
      await ListApi.addList(addData)
      message.success(t('common.createSuccess'))
    } else {
      // 编辑时，包含 id 字段
      await ListApi.updateItem(rawData)
      message.success(t('common.updateSuccess'))
    }
    await getList() // 重新获取
    showDialog.value = false
  } catch (error) {
    if (error === '表单验证失败') {
      message.error('表单验证失败')
    } else {
      // ElMessage.error('校验不通过请填写完整信息')
    }
  }
}

// 新增编辑
const openDialog = (isAdd = true, projectId = 0) => {
  isAddMode.value = isAdd
  dialogTitle.value = isAdd ? '新增项目' : '编辑项目'
  if (isAdd) {
    // 清空表单数据
    form.value = {
      projectCode: '',
      projectName: '',
      projectType: '',
      oamType: '',
      warnType: '',
      maintainPeriod: [],
      projectAmount: '',
      startDate: '',
      endDate: '',
      id: '' // 确保清空 id 字段
    }
  } else {
    getEdit(projectId)
  }
  nextTick(() => {
    if (formRef.value) {
      formRef.value.resetFields() // 清除验证状态
    }
  })
  showDialog.value = true
}

// 弹窗部分↑

// 分页搜索↓
const loading = ref(true) // 列表的加载中
const total = ref(0)
const itemList = ref([])
const router = useRouter()
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  project: '', //项目编号/名称
  projectCode: '', //项目编号
  projectName: '', //项目名称
  projectType: '', //项目类型
  oamType: '', //运维类型
  warnType: '', //告警级别
  safeguardFlag: '', //在保状态
  nearFlag: '', //临保状态
  type: 0,
  year: '' //年份
})

// 初始化
onMounted(() => {
  // getList()
  getYearList(startOrend.value.type)
  getSelect()
  window.addEventListener('keydown', handleGlobalKeyDown)
  // // 初始化后默认点击左侧列表第一个数据(不点击其他搜索条件永远为左侧第一条下面id的搜索)
  // if (allYear.value.length > 0) {
  //   clickYear(allYear.value[0], 0)
  // }
  // 在保过保状态
  const safeguardFlagParam = route.query.safeguardFlag ? route.query.safeguardFlag : ''
  if (safeguardFlagParam !== undefined && safeguardFlagParam !== '') {
    // 如果数据存在则进行赋值查询
    status.value = safeguardFlagParam
    handleStatusChange(status.value)
    // 调用查询方法
    onSubmit()
  }
})
onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleGlobalKeyDown)
})

const handleGlobalKeyDown = (event) => {
  if (event.key === 'Enter') {
    onSubmit()
  }
}
const activeTab = ref('first') // 默认选中的 tab
const startOrend = ref({ type: 0 }) //开始或结束年份
const allYear = ref([])
// 点击全部年份清空年份和类型
// const allYearClick = () => {
//   queryParams.year = ''
//   queryParams.type = ''
//   getList()
// }
const selectedUnit = ref(null) // 用于存储当前选中的单位
const clickYear = (year, type) => {
  selectedUnit.value = year // 更新选中的单位
  // 打印点击年
  queryParams.year = year
  queryParams.type = type
  getList()
}
const navClick = async (num: number) => {
  queryParams.year = '' //切换维保时清除掉年重新获取列表数据
  if (num === 0) {
    activeTab.value = 'first'
    startOrend.value = { type: 0 }
    await getYearList(startOrend.value.type) //必须在类型修改后在调用接口
  } else {
    activeTab.value = 'second'
    startOrend.value = { type: 1 }
    await getYearList(startOrend.value.type)
  }
  await getList() //重新获取列表数据
}

// 获取左侧年份
const getYearList = async (num: number) => {
  const data = await ListApi.getYear(startOrend.value)
  allYear.value = Object.keys(data).sort((a, b) => parseInt(b) - parseInt(a))
  // 初始化后默认点击左侧列表第一个数据(不点击其他搜索条件永远为左侧第一条下面id的搜索)
  if (allYear.value.length > 0) {
    clickYear(allYear.value[0], num)
  }
}
// 缓存已请求过的 warnType
const warnTypeCache = new Map()
const loadWarnTypeDescription = async (row) => {
  if (!row.warnTypeDescription && row.warnType) {
    if (warnTypeCache.has(row.warnType)) {
      row.warnTypeDescription = warnTypeCache.get(row.warnType)
    } else {
      try {
        const description = await UTILS.getWarnTypeDescription(row.warnType)
        row.warnTypeDescription = description
        warnTypeCache.set(row.warnType, description)
      } catch (error) {
        // console.error('获取告警方式描述失败:', error)
        row.warnTypeDescription = '获取失败'
      }
    }
  }
}
const getList = async () => {
  loading.value = true
  try {
    const data = await ListApi.getList(queryParams)
    // // 提前解析告警方式描述
    // await Promise.all(
    //   data.list.map(async (item) => {
    //     item.warnTypeDescription = await UTILS.getWarnTypeDescription(item?.warnType)
    //   })
    // )
    // 初始化时不请求描述，只赋空值
    data.list.forEach((item) => {
      item.warnTypeDescription = null
    })
    total.value = data.total
    itemList.value = data.list
  } finally {
    loading.value = false
  }

  // console.log('打印表格数据', itemList.value)
}
const selectList = ref([])
const getSelect = async () => {
  const data = await ListApi.getSelect()
  selectList.value = data
  // console.log('打印下拉数据', data)
}

const size = ref<ComponentSize>('default')
const background = ref(true) //分页背景颜色
const disabled = ref(false)

const handleSizeChange = (val: number) => {
  queryParams.pageSize = val
  getList() //重新获取
}
const handleCurrentChange = (val: number) => {
  queryParams.pageNo = val
  getList() //重新获取
}
// 搜索
const onSubmit = () => {
  getList()
}
// 重置
const resetForm = () => {
  // 重置搜索表单
  queryParams.projectCode = ''
  queryParams.projectName = ''
  queryParams.projectType = ''
  queryParams.oamType = ''
  queryParams.project = ''
  queryParams.warnType = ''
  queryParams.safeguardFlag = ''
  queryParams.pageNo = 1 // 重置页码
  queryParams.pageSize = 10 // 重置每页条数

  status.value = '' //状态暂存
  // queryParams.type = 0
  // queryParams.year = ''
  // 重新获取数据
  getList()
}
// 前往详情
const goDetail = (id: number) => {
  sessionStorage.setItem('listDetailId', id.toString())
  router.push({
    path: '/operations/listDetail'
    // query: { id: id.toString() } // 将 id 值传递到详情页
  })
}
// 编辑获取数据回填
const getEdit = async (id) => {
  const data = await ListApi.getDetail({ id })
  form.value = {
    ...data,
    maintainPeriod: [new Date(data.startDate), new Date(data.endDate)]
  }
}
// 删除
const deleteRow = async (id: number) => {
  // 执行删除操作
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ListApi.deleteList(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}
/** 导入按钮操作*/
const importFormRef = ref()
const showImport = ref(false)
const importClick = async () => {
  showImport.value = true
  importFormRef.value.open()
}
// 单行导入按钮操作
const importSingleRef = ref()
const showImportSingle = ref(false)
const ProjectId = ref(0)
const importSingleClick = async (id: number) => {
  ProjectId.value = id
  showImportSingle.value = true
  importSingleRef.value.open()
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ListApi.exportList(queryParams)
    download.excel(data, '运维项目列.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.append-icon-text {
  position: absolute;
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 100%;
  background-color: #dbdee5;
  text-align: center;
}
.projectAmount-color {
  color: #005fff;
}
.my-component {
  // width: 100%;
  // width: 100% !important;
  // height: 100% !important;
  // min-width: 0; // 允许压缩
  background-color: white;
  display: flex;
  flex-direction: column;
  header {
    width: 100%;
    height: 60px;
    font-size: 18px;
    font-weight: bold;
    padding-left: 20px;
    display: flex;
    align-items: center;
  }
  main {
    // flex: 1;
    // display: flex;
    //background-color: pink;
    .left-year {
      height: 100%;
      background-color: aquamarine;
      // width: 200px !important;
      // height: 580px !important;
      border: 1px solid #ccc;
      background-color: #f1f3f6;
      border-radius: 1%;
      // margin-right: 10px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      // 隐藏滚动条
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
      }
      .button-group {
        margin: 10px 0 10px 10px;
        width: 90%;
        height: 40px;
        background-color: #e3e6eb;
        border-radius: 60px;
        display: flex;
        justify-content: space-around;
        align-items: center;
      }

      .nav-button {
        width: 48%;
        height: 40px;
        border-radius: 20px;
        border: none;
        background-color: transparent;
        color: black;
        cursor: pointer;
        transition: background-color 0.3s;
      }

      .nav-button.active {
        background-color: white;
      }

      .tabs-content {
        flex-grow: 1;
        overflow-y: auto;
        overflow-y: auto;
        // 隐藏滚动条
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE 10+ */
        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Edge */
        }
        .tab-content {
          display: block;
          padding: 10px;
          .text-style {
            height: 20px !important;
            display: flex;
            align-items: center;
            cursor: pointer;
            margin-bottom: 5px; //左侧列表每个增加5px的间隔
          }
        }

        .tab-content.active {
          background-color: white;
        }

        .el-dropdown-menu .el-dropdown-item img.dropdown-image {
          width: 20px !important;
          height: 20px !important; /* 确保高度也是20px */
          margin-right: 8px; /* 添加一些右边距 */
        }
      }
    }
    // .el-col {
    //   height: 100%;
    // }
    // .right-main {
    //   // width: calc(100% - 200px); //增加固定宽度防止缩放溢出父盒子
    //   flex: 1;
    //   background-color: palegreen;
    //   display: flex;
    //   flex-direction: column;
    //   // overflow-x: auto;
    //   min-width: 0; /* 允许内容被裁剪而不是挤压其他元素 */
    //   // 隐藏滚动条
    //   scrollbar-width: none; /* Firefox */
    //   -ms-overflow-style: none; /* IE 10+ */
    //   &::-webkit-scrollbar {
    //     display: none; /* Chrome, Safari, Edge */
    //   }
    //   .form-box {
    //     width: 100%;
    //     display: flex;
    //     flex-direction: row;
    //     align-items: center;
    //     margin-bottom: 10px;
    //     overflow-x: auto;
    //     .form-search {
    //       flex: 1;
    //       display: flex;
    //       flex-direction: row;
    //       align-items: center;
    //       .el-form-item {
    //         margin-right: 10px;
    //       }
    //     }
    //   }

    //   .right-table {
    //     width: 100%;
    //     flex: 1;
    //     padding-right: 1px;
    //     // max-height: 540px;
    //     overflow-y: auto;
    //     // 隐藏滚动条
    //     scrollbar-width: none; /* Firefox */
    //     -ms-overflow-style: none; /* IE 10+ */
    //     &::-webkit-scrollbar {
    //       display: none; /* Chrome, Safari, Edge */
    //     }
    //     :deep(.el-table__header th) {
    //       text-align: left;
    //       // font-size: 16px;
    //     }
    //     // :deep(.el-table--default .cell) {
    //     //   font-weight: bold;
    //     // }
    //     :deep(.el-table td) {
    //       text-align: left;
    //     }

    //     .demo-pagination-block + .demo-pagination-block {
    //       margin-top: 10px;
    //     }
    //     .demo-pagination-block .demonstration {
    //       margin-bottom: 16px;
    //     }
    //   }
    // }
    .demo-pagination-block {
      display: flex;
      justify-content: flex-end;
    }
  }

  .custom-tabs {
    :deep(.el-tabs__nav-wrap) {
      flex: none; //使底部灰色边框清除
    }
    :deep(.el-tabs__item.is-active) {
      color: #fff; /* 选中标签的文字颜色 */
      background-color: #409eff; /* 选中标签的背景色 */
      border: 1px solid #409eff; /* 选中标签的边框 */
    }

    :deep(.el-tabs__item) {
      border: 1px solid #409eff; /* 未选中标签的边框 */
      color: #409eff; /* 未选中标签的文字颜色 */
      padding: 0 20px; /* 标签的内边距 */
      transition: all 0.3s; /* 过渡效果 */
      height: 30px;
      width: 60px; /* 设置每个标签的宽度 */
      text-align: center;
      padding: 0 10px;
      margin: 0;
      line-height: 30px;
    }

    :deep(.el-tabs__nav) {
      display: flex; /* 确保标签导航是弹性布局 */
      justify-content: flex-start; /* 从左到右排列 */
      border-bottom: none !important; /* 清除下边框 */
      box-shadow: none !important; /* 清除可能的阴影 */
    }

    :deep(.el-tabs__nav::after) {
      height: 0 !important; /* 将伪元素的高度设置为 0 */
      background-color: transparent !important; /* 清除伪元素的背景色 */
    }

    :deep(.el-tabs__active-bar) {
      display: none; /* 隐藏选中条 */
    }
  }
  .example-showcase .el-dropdown-link {
    cursor: pointer !important;
    color: var(--el-color-primary);
    display: flex;
    align-items: center;
  }
  .el-dropdown-link {
    color: #606266;
    font-size: 14px;
  }
}
// 查询按钮样式
.text-button {
  color: #fff;
}

// 左侧侧边栏高亮样式
/* 高亮样式 */
.highlight {
  background-color: #fff; /* 浅蓝色背景 */
  // color: #0000ff; /* 蓝色文字 */
  // font-weight: bold; /* 加粗字体 */
}
</style>
