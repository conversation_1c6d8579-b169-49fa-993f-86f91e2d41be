<template>
  <ContentWrap class="my-component">
    <header>
      <el-button class="ml-5 mr-5" type="primary" plain @click="goBack">返回</el-button>
      <p>项目管理列表-详情</p>
    </header>
    <main>
      <el-row :gutter="20">
        <el-col :span="6" :xs="24">
          <div class="main-left">
            <p class="left-title">基础信息</p>
            <!-- 描述列表 -->
            <div class="detail-box">
              <el-descriptions :column="1" :size="size" border>
                <el-descriptions-item
                  label-class-name="my-label"
                  v-for="(item, index) in descriptionsItems"
                  :key="index"
                >
                  <template #label v-if="item.label">
                    <div class="label-box">{{ item.label }}</div>
                  </template>

                  <div
                    v-if="item.value !== null && item.value !== undefined && item.value !== ''"
                    :class="['cell-item', index === 6 ? 'blue-text' : '']"
                  >
                    {{ item.value }}
                    <el-tooltip
                      :content="item.warnTypeDescription || '无'"
                      placement="top"
                      :show-if-overflow="false"
                      v-if="item?.value && item.warnTypeDescription"
                    >
                      <img
                        v-if="item.icon"
                        src="@/assets/imgs/icons/faultTab2.png"
                        alt=""
                        style="width: 16px; height: 16px; margin-left: 5px; vertical-align: middle"
                      />
                    </el-tooltip>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <!--  描述列表 -->
          </div>
        </el-col>
        <el-col :span="18" :xs="24">
          <div class="main-right">
            <p class="right-title">项目设备列表</p>
            <div class="form-box">
              <el-form
                ref="queryFormRef"
                :inline="true"
                :model="rightForm"
                class="form-search flex flex-wrap items-start -mb-15px"
              >
                <el-form-item label="" class="!mr-3">
                  <el-input
                    v-model="rightForm.assets"
                    class="!w-240px"
                    clearable
                    placeholder="输入设备编号/名称"
                  />
                </el-form-item>
                <el-form-item
                  label="设备类型"
                  class="!mr-3"
                  v-hasPermi="['infra:device-type:list']"
                >
                  <el-cascader
                    v-model="rightForm.assetsTypeId"
                    :options="typeList"
                    :props="cascaderProps"
                    collapse-tags
                    clearable
                    class="!w-140px"
                  />
                </el-form-item>
                <el-form-item
                  label="区域"
                  prop="areaId"
                  class="!mr-3"
                  v-hasPermi="['infra:device-dict:DictTypeList']"
                >
                  <Dictionary
                    v-model="rightForm.areaId"
                    type="cascader"
                    width="180px"
                    dict-type="oamArea"
                    :cascader-props="{
                      multiple: true,
                      checkStrictly: true,
                      label: 'name',
                      value: 'id'
                    }"
                    :max-collapse-tags="2"
                    placeholder="请选择区域"
                  />
                </el-form-item>

                <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
                  <div>
                    <XButton
                      class="text-button"
                      preIcon="ep:search"
                      title="查询"
                      width="75px"
                      gradient
                      @click="onSubmit"
                      v-hasPermi="['infra:device-overview-info:query']"
                    />
                    <el-button @click="resetForm(true)"><Icon icon="ep:refresh" />重置</el-button>
                    <XButton
                      class="text-button"
                      preIcon="ep:search"
                      title="高级搜索"
                      width="100px"
                      gradient
                      @click="showAdvancedSearch"
                      v-hasPermi="['infra:device-overview-info:query']"
                    />
                  </div>
                </div>
              </el-form>
            </div>
            <el-table
              :data="tableData"
              height="530"
              v-loading="loading"
              style="width: 100%; margin-bottom: 10px"
            >
              <!-- 其他列 -->
              <el-table-column type="index" :index="tableIndex" label="序号" min-width="80" />
              <el-table-column
                prop="assetsCode"
                label="设备编号"
                min-width="150"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <span @click="handleClick(scope.row.id)" style="color: blue; cursor: pointer">{{
                    scope.row.assetsCode
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="assetsName"
                label="设备名称"
                min-width="150"
                show-overflow-tooltip
              />
              <el-table-column prop="assetsTypeName" label="设备类型" min-width="120" />
              <el-table-column prop="area" label="所属区域" min-width="120" />
              <el-table-column prop="operatingUnitName" label="运营商" min-width="120" />
              <el-table-column prop="maintenanceUnitName" label="维保单位" min-width="120" />
              <el-table-column prop="ip" label="ip" min-width="90" />
              <el-table-column prop="assetsSourceName" label="设备来源" min-width="120" />
              <el-table-column prop="label" label="标签" min-width="120" />
              <el-table-column prop="warnName" label="告警方式" min-width="90">
                <template #default="scope">
                  <el-tooltip
                    :content="scope.row.warnTypeDescription"
                    placement="top"
                    v-if="scope?.row?.warnName"
                  >
                    <span @mouseenter="loadWarnTypeDescription(scope.row)">
                      {{ scope.row.warnName }}
                      <img src="@/assets/imgs/icons/faultTab2.png" alt="" />
                    </span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="warrantyStatus" label="设备状态" min-width="90">
                <template #default="scope">
                  <div :class="getStatusClass(scope.row.warrantyStatus)">
                    {{ getStatusText(scope.row.warrantyStatus) }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="syncStatus" label="同步状态" min-width="90">
                <template #default="scope">
                  <div :class="getStatusClass2(scope.row.syncStatus)">
                    {{ getStatusText2(scope.row.syncStatus) }}
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!-- 分页部分 -->
            <div class="demo-pagination-block">
              <el-pagination
                v-model:current-page="rightForm.pageNo"
                v-model:page-size="rightForm.pageSize"
                :page-sizes="[10, 20, 30, 50, 100]"
                :size="size"
                :disabled="disabled"
                :background="background"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </main>
    <!-- 高级搜索遮罩层 -->
    <el-dialog v-model="showDialog" title="高级搜索" width="45%" @close="closeAdvancedSearchDialog">
      <el-form :model="rightForm" class="form-search">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="设备编号:">
              <el-input v-model="rightForm.assetsCode" placeholder="输入设备编号" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="设备名称:">
              <el-input v-model="rightForm.assetsName" placeholder="输入设备名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="设备类型:" v-hasPermi="['infra:device-type:list']">
              <el-cascader
                style="width: 100%"
                v-model="rightForm.assetsTypeId"
                :options="typeList"
                :props="cascaderProps"
                collapse-tags
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="所属区域:"
              prop="areaId"
              v-hasPermi="['infra:device-dict:DictTypeList']"
            >
              <Dictionary
                style="width: 100%"
                v-model="rightForm.areaId"
                type="cascader"
                width="240px"
                dict-type="oamArea"
                :cascader-props="{
                  multiple: true,
                  checkStrictly: true,
                  label: 'name',
                  value: 'id'
                }"
                :max-collapse-tags="2"
                placeholder="请选择区域"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="运营商:">
              <el-select v-model="rightForm.operatingUnit" placeholder="选择运营商">
                <el-option
                  v-for="item in operatingUnitList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="维保单位:">
              <el-select v-model="rightForm.maintenanceUnit" placeholder="选择维保单位">
                <el-option
                  v-for="item in maintenanceUnitList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="设备来源:" v-hasPermi="['infra:device-dict:DictTypeList']">
              <el-select v-model="rightForm.assetsSource" placeholder="选择设备来源">
                <el-option
                  v-for="item in assetsSourceList"
                  :key="item.id"
                  :label="item.value"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="告警方式:">
              <el-select v-model="rightForm.warnId" placeholder="选择告警方式">
                <el-option
                  v-for="item in warnIdList"
                  :key="item.id"
                  :label="item.warnTypeName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="同步状态:">
              <el-select v-model="rightForm.syncStatus" placeholder="选择同步状态">
                <el-option label="未同步" :value="0" />
                <el-option label="已同步" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="bottom-btn">
            <el-button type="primary" @click="submitAdvancedSearch">确认</el-button>
            <el-button @click="resetForm(true)">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { getDictList } from '@/api/infra/deviceDict' //设备来源
import * as DynamicFormApi from '@/api/DynamicForm'
import * as TicketsPushApi from '@/api/operations/ticketsPush'
import * as ListApi from '@/api/operations/list'
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
import { formatProjectType, formatOamType, formatTimestamp, formatSafeguardFlag } from './utils'
import type { ComponentSize } from 'element-plus'
import * as UTILS from './utils'

const route = useRoute()
// 使用 computed 来确保响应性
const params = reactive({ id: '' })
const itemObj = ref({}) //详情数据
const router = useRouter()
//序号自增
const tableIndex = (index: number) => {
  return (rightForm.pageNo - 1) * rightForm.pageSize + index + 1
}
// 默认数据
const descriptionsItems = ref([
  { label: '项目编号:', value: '' },
  { label: '项目名称:', value: '' },
  { label: '项目类型:', value: '' },
  { label: '运维类型:', value: '' },
  { label: '告警方式:', value: '', icon: '', warnTypeDescription: '' },
  { label: '维护期:', value: '' },
  { label: '设备数:', value: '' },
  { label: '项目金额:', value: '' },
  { label: '创建人:', value: '' },
  { label: '创建时间:', value: '' },
  { label: '状态:', value: '' }
])

onMounted(async () => {
  const storedId = sessionStorage.getItem('listDetailId')
  if (storedId) {
    params.id = storedId
    rightForm.projectId[0] = storedId
    await nextTick()
    await getList()
    await getRightList()
  } else {
    ElMessage.warning('已离开页面,请重新选择项目')
  }
  getType() // 获取设备类型下拉
  getOperatingUnitFun() // 获取运营商下拉
  getMaintenanceUnitFun() //维保单位
  getAssetsSourceList() //设备来源
  getWarnIdList() //告警方式
})

// 使用 beforeRouteLeave 导航守卫
onBeforeRouteLeave((to, from) => {
  // 清除 sessionStorage 中的 listDetailId
  // sessionStorage.removeItem('listDetailId')
})

const getList = async () => {
  const data = await ListApi.getDetail(params)
  itemObj.value = data
  // 重新赋值左侧列表方便循环渲染
  descriptionsItems.value = [
    { label: '项目编号', value: data?.projectCode },
    { label: '项目名称', value: data?.projectName },
    { label: '项目类型', value: formatProjectType(data?.projectType) },
    { label: '运维类型', value: formatOamType(data?.oamType) },
    {
      label: '告警方式',
      value: data?.warnTypeName,
      icon: 'faultTab2.png',
      warnTypeDescription: await UTILS.getWarnTypeDescription(data?.warnType)
    },
    {
      label: '维护期',
      value: data?.startDate?.replace(/-/g, '/') + ' ~ ' + data?.endDate?.replace(/-/g, '/')
    },
    { label: '设备数', value: data?.assetsCount },
    {
      label: '项目金额',
      value:
        data?.projectAmount >= 10000
          ? data?.projectAmount / 10000 + '万元'
          : data?.projectAmount + '元'
    },
    { label: '创建人', value: data?.creatorNickname },
    { label: '创建时间', value: formatTimestamp(data?.createTime).replace(/-/g, '/') },
    { label: '状态', value: formatSafeguardFlag(data?.safeguardFlag) }
  ]
}

// 表格数据
const tableData = ref([])
const rightForm = reactive({
  pageNo: 1,
  pageSize: 10,
  projectId: [], //父组件传递过来的id
  assets: null, //设备编号名称
  assetsTypeId: [], //设备类型
  areaId: [], //区域id
  assetsCode: null, //设备编号
  assetsName: null, //设备名称
  operatingUnit: null, //运营商
  maintenanceUnit: null, //维保单位
  assetsSource: null, //设备来源
  warnId: null, //告警方式
  syncStatus: null //同步状态
})
// 分页相关
const loading = ref(true)
const total = ref(0)
const size = ref('default')
const background = ref(true)
const disabled = ref(false)
// 点击分页
// 其他事件
const handleSizeChange = (val: number) => {
  rightForm.pageSize = val
  getRightList() //重新获取
}
const handleCurrentChange = (val: number) => {
  rightForm.pageNo = val
  getRightList() //重新获取
}
// 缓存已请求过的 warnType
const warnTypeCache = new Map()
const loadWarnTypeDescription = async (row) => {
  if (!row.warnTypeDescription && row.warnId) {
    if (warnTypeCache.has(row.warnId)) {
      row.warnTypeDescription = warnTypeCache.get(row.warnId)
    } else {
      try {
        const description = await UTILS.getWarnTypeDescription(row.warnId)
        row.warnTypeDescription = description
        warnTypeCache.set(row.warnId, description)
      } catch (error) {
        // console.error('获取告警方式描述失败:', error)
        row.warnTypeDescription = '获取失败'
      }
    }
  }
}
const getRightList = async () => {
  loading.value = true
  try {
    const data = await ListApi.getDetailList(rightForm)
    // console.log('打印列表数据', data)
    // // 提前解析告警方式描述
    // await Promise.all(
    //   data?.list.map(async (item) => {
    //     item.warnTypeDescription = await UTILS.getWarnTypeDescription(item?.warnId)
    //   })
    // )
    // 初始化时不请求描述，只赋空值
    data.list.forEach((item) => {
      item.warnTypeDescription = null
    })
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const handleClick = (id: number) => {
  sessionStorage.removeItem('equipmentDetailCode') //传id清除code 传code清除id
  sessionStorage.setItem('equipmentDetailId', id.toString())
  router.push({
    path: '/operations/equipmentDetail'
    // query: { id: id.toString() } // 将 id 值传递到详情页
  })
}

// 设备类型
const typeList = ref([])
// 下拉格式
const cascaderProps = reactive({
  value: 'id',
  label: 'name',
  children: 'children',
  multiple: true,
  checkStrictly: true,
  emitPath: false
})
const getType = async () => {
  const data = await TicketsPushApi.getType()
  // console.log('设备类型typeList', data)
  typeList.value = data
}
// 运营单位/运维单位
const operatingUnitList = ref([]) //运营商/运营单位
const getOperatingUnit = async (type: string) => {
  const unitTypeData = await DynamicFormApi.getUnitType({ code: type })
  const unitData = await DynamicFormApi.getUnit({ typeId: unitTypeData })
  return unitData
}
const getOperatingUnitFun = async () => {
  const data = await getOperatingUnit('operation')
  // console.log('运营商/运维单位', data)
  operatingUnitList.value = data
}
// 维保单位
const maintenanceUnitList = ref([]) //维保单位
const getMaintenanceUnitFun = async () => {
  const data = await getOperatingUnit('maintenance')
  // console.log('维保单位', data)
  maintenanceUnitList.value = data
}
//设备来源
const assetsSourceList = ref([])
const getAssetsSourceList = async () => {
  const data = await getDictList('deviceSource')
  // console.log('设备来源', data)
  assetsSourceList.value = data
}
// 告警方式
const warnIdList = ref([])
const getWarnIdList = async () => {
  const data = await DynamicFormApi.getWarning()
  // console.log('告警方式', data)
  warnIdList.value = data
}
// 搜索
const onSubmit = () => {
  // form.ids = [] //清除多选框选中后在搜索，防止只搜多选框
  getRightList()
}
// 重置
const resetForm = (flag: boolean) => {
  rightForm.assets = null
  rightForm.assetsCode = null // 设备编号
  rightForm.assetsTypeId = [] // 设备类型
  rightForm.areaId = [] // 所属区域
  rightForm.pageNo = 1 // 分页页码
  rightForm.pageSize = 10 // 分页大小
  // rightForm.deptId = null // 部门ID
  rightForm.assetsName = null // 设备名称
  rightForm.operatingUnit = null // 运营商
  rightForm.maintenanceUnit = null // 推送单位/维保单位
  rightForm.assetsSource = null // 设备来源
  rightForm.warnId = null // 告警方式
  rightForm.syncStatus = null // 同步状态
  // rightForm.ids = [] //选中的数据
  // 使用接受的flag来判断是否需要重新请求数据 高级搜索弹窗关闭部分不需要
  if (flag) {
    getRightList() // 调用获取列表的函数
  }
}
// 高级搜索相关
const showDialog = ref(false)
const showAdvancedSearch = () => {
  showDialog.value = true
  // 每次打开遮罩层时重置表单数据
  // resetForm(true)
}

const submitAdvancedSearch = async () => {
  // console.log('高级搜索表单数据:', form)
  // form.ids = [] //清除多选框选中后在搜索，防止只搜多选框
  await getRightList()
  showDialog.value = false
}
const closeAdvancedSearchDialog = () => {
  showDialog.value = false
  // 不调用 resetForm 或者在 resetForm 中不重置数据
}
// 返回上一页
const goBack = () => {
  window.history.back()
}
const getStatusText = (status: number) => {
  switch (status) {
    case 0:
      return '质保中'
    case 1:
      return '已过保'
    default:
      return ''
  }
}
const getStatusText2 = (status: number) => {
  switch (status) {
    case 0:
      return '未同步'
    case 1:
      return '已同步'
    default:
      return ''
  }
}

const getStatusClass = (status: number) => {
  switch (status) {
    case 0:
      return 'status-normal'
    case 1:
      return 'status-fault'
    default:
      return ''
  }
}
const getStatusClass2 = (status: number) => {
  switch (status) {
    case 0:
      return 'status-fault'
    case 1:
      return 'status-normal'
    default:
      return ''
  }
}
</script>
<style lang="scss" scoped>
.my-component {
  // width: 100%;
  // height: 100%;
  background-color: white;
  header {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    margin-bottom: 10px;
  }
  main {
    // width: 100%;
    // // height: 630px;
    // height: 530px;
    // display: flex;
    // // padding: 0 20px;
    // padding: 0 20px 0 0;
    .main-left {
      // width: 350px;
      // height: 100%;
      // margin-right: 10px;

      .left-title {
        // width: 350px;
        // width: 100%;
        // height: 40px;
        // line-height: 40px;
        // margin-left: 40px;
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        position: relative;
        font-family: Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #3b3c3d;
        text-align: left;
        font-style: normal;
        text-transform: none;
        line-height: 24px;
        margin: 3px 0;
        padding-left: 28px;

        &::before {
          margin: 10px 0 0 10px;
          content: '';
          position: absolute;
          left: 0;
          top: 6px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #005fff;
          border: 2px solid #d4e4ff;
        }
      }

      .detail-box {
        // width: 95%;
        // height: 90%;
        margin-top: -17px;
      }
      .label-box {
        min-width: 80px;
      }
    }
    // .main-right {
    //   flex: 1;
    //   overflow-x: auto;
    //   // 隐藏滚动条
    //   scrollbar-width: none; /* Firefox */
    //   -ms-overflow-style: none; /* IE 10+ */
    //   &::-webkit-scrollbar {
    //     display: none; /* Chrome, Safari, Edge */
    //   }

    .right-title {
      // width: 100%;
      // height: 40px;
      // line-height: 40px;
      // margin-left: 40px;
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      position: relative;
      font-family: Microsoft YaHei;
      font-weight: bold;
      font-size: 16px;
      color: #3b3c3d;
      text-align: left;
      font-style: normal;
      text-transform: none;
      line-height: 24px;
      margin: 3px 0;
      padding-left: 28px;

      &::before {
        margin: 10px 0 0 10px;
        content: '';
        position: absolute;
        left: 0;
        top: 6px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #005fff;
        border: 2px solid #d4e4ff;
      }
    }
    //   :deep(.el-table__header th) {
    //     text-align: left;
    //   }
    //   :deep(.el-table td) {
    //     text-align: left;
    //   }
    // }
    .demo-pagination-block {
      display: flex;
      justify-content: flex-end;
    }
  }
}
// 状态文字颜色
.status-normal {
  color: green;
}

.status-reported {
  color: orange;
}

.status-fault {
  color: red;
}
// ===
.el-descriptions {
  margin-top: 20px;
}
.cell-item {
  display: flex;
  align-items: center;
  min-width: 180px;
}
.margin-top {
  margin-top: 20px;
}

/* 为设备数的文字添加蓝色 */
.blue-text {
  color: blue;
}
// 左侧label背景颜色
:deep(.my-label) {
  background: #f7f8f8 !important;
}
</style>
