import * as MannerApi from '@/api/alarm/rules/manner'
// 项目类型
export function formatProjectType(projectType: number): string {
  switch (projectType) {
    case 1:
      return '建设项目'
    case 2:
      return '运维项目'
    default:
      return '未知类型'
  }
}
// 运维类型
export function formatOamType(oamType: number): string {
  switch (oamType) {
    case 1:
      return '全运维'
    case 2:
      return '抢修运维'
    case 3:
      return '网管运维'
    default:
      return '未知类型'
  }
}
// 时间戳转换
export function formatTimestamp(timestamp) {
  const date = new Date(timestamp)
  const year = date.getFullYear() // 获取年份
  const month = String(date.getMonth() + 1).padStart(2, '0') // 获取月份，月份从0开始计数，所以需要+1
  const day = String(date.getDate()).padStart(2, '0') // 获取日期
  const hours = String(date.getHours()).padStart(2, '0') // 获取小时
  const minutes = String(date.getMinutes()).padStart(2, '0') // 获取分钟
  const seconds = String(date.getSeconds()).padStart(2, '0') // 获取秒数
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
}
// 在保状态
/**
 * 格式化在保/过保状态，支持临近过保判断
 * @param safeguardFlag - 是否在保
 * @param nearFlag - 是否临近过保（可选）
 * @returns {string} 状态文本
 */
export function formatSafeguardFlag(safeguardFlag: boolean, nearFlag?: boolean): string {
  // console.log('打印两个状态', safeguardFlag, nearFlag)
  if (safeguardFlag && nearFlag) {
    return '临近过保'
  } else if (safeguardFlag) {
    return '在保'
  } else if (!safeguardFlag) {
    return '过保'
  } else {
    return ''
  }
}
// 日期转化合并
export function formatDateRange(startDate, endDate) {
  const start = new Date(startDate[0], startDate[1] - 1, startDate[2])
  const end = new Date(endDate[0], endDate[1] - 1, endDate[2])

  const format = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}/${month}/${day}`
  }

  return `${format(start)}-${format(end)}`
}

// 告警等级提示
export async function getWarnTypeDescription(id) {
  if (!id) return '无'
  try {
    const row = await MannerApi.getWarnDetail({ id }) // 等待接口返回真实数据
    const texts = []
    if (row?.popFlag) texts.push('弹窗')
    if (row?.topFlag) texts.push('置顶')
    if (row?.pushFlag) texts.push('推送')
    if (row?.msgFlag) texts.push('短信')
    if (row?.alarmFlag) texts.push('警报')
    return texts.length > 0 ? texts.join('、') : '无'
  } catch (error) {
    console.error('获取失败:', error)
    return '无'
  }
}

export function getIconPathByWarnType(warnType) {
  if (warnType === '一级') {
    return '@/assets/imgs/icons/faultTab.png'
  } else if (warnType === '二级') {
    return '@/assets/imgs/icons/faultTab.png'
  } else if (warnType === '三级') {
    return '@/assets/imgs/icons/faultTab.png'
  } else if (warnType === '四级') {
    return '@/assets/imgs/icons/faultTab.png'
  } else {
    return '@/assets/imgs/icons/faultTab.png'
  }
}
