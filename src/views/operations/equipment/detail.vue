<template>
  <ContentWrap class="my-component">
    <header>
      <el-button class="ml-5 mr-5" type="primary" plain @click="goBack">返回</el-button>
      <p>运维设备列表-详情</p>
    </header>
    <main>
      <div class="main-left border-style">
        <div class="detail-box">
          <PrivateShow :id="params.id" :assetsCode="params.assetsCode" />
        </div>
      </div>

      <div class="main-right">
        <header class="border-style">
          <img v-if="detailData?.pictureUrl" :src="detailData?.pictureUrl" alt="" />
          <img v-else src="@/assets/imgs/device.png" alt="" />
          <!-- <el-image :src="detailData?.pictureUrl">
            <template #error>
              <div class="image-slot">
                <img src="@/assets/imgs/device.png" alt="" />
              </div>
            </template>
          </el-image> -->
        </header>
        <!-- <main class="border-style"> <img src="@/assets/imgs/loginbg.png" alt="" /></main> -->
        <footer class="border-style">
          <div class="edit-time" v-for="item in logList" :key="item">
            <p>由{{ item.userName }}{{ item.subType }}</p>
            <!-- <p>编辑信息:{{ item.action }}</p> -->
            <p>{{ UTILS.timestampToDateString(item.createTime) }}</p>
          </div>
        </footer>
      </div>
    </main>
  </ContentWrap>
</template>

<script lang="ts" setup>
import PrivateShow from '@/components/PrivateShow/index.vue'
import { ref, reactive, onMounted } from 'vue'
import { useRoute, onBeforeRouteLeave } from 'vue-router'
import * as EquipmentApi from '@/api/operations/equipment'
import * as WhiteListApi from '@/api/alarm/whiteList' //引入api处理传递assetsCode的情况
import * as UTILS from './utils/index' //引入筛选的工具函数
const route = useRoute()
const params = reactive({ id: null, assetsCode: null })
const errUrl = ref('@/assets/imgs/device.png')
onMounted(async () => {
  const storedId = sessionStorage.getItem('equipmentDetailId')
    ? sessionStorage.getItem('equipmentDetailId')
    : null
  const storedCode = sessionStorage.getItem('equipmentDetailCode')
    ? sessionStorage.getItem('equipmentDetailCode')
    : null

  if (storedId) {
    params.id = storedId
    console.log('设备id判断成立', params.id)
    await getDetail()
    await getEquipmentSource()
    // console.log('准备调用 getLog - id 模式')
    await getLog()
  } else if (storedCode) {
    params.assetsCode = storedCode
    // console.log('设备编号判断成立', params.assetsCode)
    await getDetail()
    // await getEquipmentSource()
    // console.log('准备调用 getLog - code 模式')
    await getLog()
  } else {
    ElMessage.warning('已离开页面,请重新选择项目')
  }
})

// 使用 beforeRouteLeave 导航守卫
onBeforeRouteLeave((to, from) => {
  // 清除 sessionStorage 中的 equipmentDetailId
  // sessionStorage.removeItem('equipmentDetailId')
})

const detailData = ref(null) //声明id存储用于处理在传递assetsCode查看详情时，使用查找到的详情id去查日志
const getDetail = async () => {
  if (params.id) {
    console.log('详情id成立', params.id)
    const data = await EquipmentApi.getDetail(params)
    detailData.value = data?.deviceOverviewInfo
  } else if (params.assetsCode) {
    console.log('详情编号成立', params.assetsCode)

    const data = await WhiteListApi.getDetail({ assetsCode: params.assetsCode })
    detailData.value = data?.deviceOverviewInfo
  }
  console.log('打印详情数据', detailData.value)
}

// 返回上一页
const goBack = () => {
  window.history.back()
}
const EquipmentSource = ref([])
const getEquipmentSource = async () => {
  const data = await EquipmentApi.getEquipmentSource({ dictType: 'deviceSource' })
  EquipmentSource.value = data
  // console.log('设备来源', EquipmentSource.value)
}
// 获得日志
const logList = ref([])
const getLog = async () => {
  // console.log('打印id', params.id, '打印code', params.assetsCode)
  if (params.id) {
    console.log('id成立', params.id)
    const data = await EquipmentApi.getLog({ bizId: params.id, type: 'DEVICE_MANAGE_CENTER' })
    logList.value = data.list
  }
  if (params.assetsCode) {
    // console.log('code成立', params.id)
    const data = await EquipmentApi.getLog({
      bizId: detailData.value.id,
      type: 'DEVICE_MANAGE_CENTER'
    })

    logList.value = data.list
  }
}
</script>

<style lang="scss" scoped>
.border-style {
  border: 1px solid #ccc;
  border-radius: 1%;
}
.my-component {
  // width: 100%;
  // height: 100%;
  background-color: white;
  header {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    margin-bottom: 10px;
  }
  main {
    width: 100%;
    height: 650px;
    display: flex;
    .main-left {
      flex: 1;
      margin-right: 10px;
      margin-left: 20px;

      .detail-box {
        width: 97%;
        height: 100%;
        margin: 0 12px;
        display: flex;
        .detail-left {
          width: 48%;
        }
        .detail-right {
          width: 48%;
        }
      }
    }
    .main-right {
      width: 250px;
      height: 100%;
      border: 1px solid #ccc;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;
      header {
        width: 95%;
        height: 25%;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 95%;
          height: 90%;
        }
        // .el-image,
        // .image-slot {
        //   width: 95%;
        //   height: 90%;
        //   img {
        //     width: 95%;
        //     height: 90%;
        //     object-fit: cover;
        //   }
        // }
      }
      main {
        width: 95%;
        height: 25%;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 95%;
          height: 90%;
        }
      }
      footer {
        width: 95%;
        // height: 45%;
        height: 70%;
        overflow-y: auto;
        &::-webkit-scrollbar {
          width: 6px; // 滚动条宽度
          height: 6px; // 滚动条高度
        }
        &::-webkit-scrollbar-thumb {
          background-color: #c1c1c1c1; // 滚动条颜色
          border-radius: 3px; // 滚动条圆角
        }

        &::-webkit-scrollbar-track {
          background-color: #f1f1f1f1; // 滚动条轨道颜色
          border-radius: 3px; // 滚动条轨道圆角
        }
        .edit-time {
          margin-left: 20px;
          width: 90%;
          height: 70px;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          margin-bottom: 20px;
        }
      }
    }
  }
}
// 左侧label背景颜色
:deep(.my-label) {
  background: #f7f8f8 !important;
}
</style>
