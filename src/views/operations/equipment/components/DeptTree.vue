<template>
  <main>
    <div class="head-container">
      <el-input
        v-model="deptName"
        class="mt-10px mb-20px pl-10px pr-10px"
        clearable
        placeholder="搜索项目"
      >
        <template #prefix>
          <Icon icon="ep:search" />
        </template>
      </el-input>
    </div>

    <div class="head-container">
      <!-- <el-tree
        style="background-color: #f1f3f6"
        ref="treeRef"
        :data="deptList"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        :props="defaultProps"
        default-expand-all
        highlight-current
        node-key="id"
        @node-click="handleNodeClick"
      >
        <template #default="{ node }">
          <span ref="el => setRef(el, node)" class="tree-node-text">
            <el-tooltip :content="node.label" placement="top" :disabled="!isLabelOverflow(node)">
              <span>
                {{ node.label.length > 7 ? node.label.slice(0, 7) + '...' : node.label }}
              </span>
            </el-tooltip>
          </span>
        </template>
      </el-tree> -->
      <!-- ================= -->
      <el-tree
        style="background-color: #f1f3f6"
        ref="treeRef"
        :data="deptList"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        :props="defaultProps"
        default-expand-all
        highlight-current
        node-key="id"
        @node-click="handleNodeClick"
      >
        <template #default="{ node }">
          <el-tooltip :content="node.label" placement="top" :disabled="node.label.length < 10">
            <span class="tree-node-text">
              {{ node.label.length > 10 ? node.label.slice(0, 10) + '...' : node.label }}
            </span>
          </el-tooltip>
        </template>
      </el-tree>
      <!-- ====================== -->
      <!-- <el-tree
        style="background-color: #f1f3f6"
        ref="treeRef"
        :data="deptList"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        :props="defaultProps"
        default-expand-all
        highlight-current
        node-key="id"
        @node-click="handleNodeClick"
      /> -->
    </div>
  </main>
</template>

<script lang="ts" setup>
import { ElTree } from 'element-plus'
import * as ListApi from '@/api/operations/list'
import { defaultProps } from '@/utils/tree'
import { ref, watch, onMounted, nextTick } from 'vue'

defineOptions({ name: 'SystemUserDeptTree' })

const deptName = ref('')
const deptList = ref<Tree[]>([]) // 树形结构
const treeRef = ref<InstanceType<typeof ElTree>>()

function handleTree(data: { [key: string]: any[] }): Tree[] {
  return Object.entries(data)
    .sort(([yearA], [yearB]) => parseInt(yearB) - parseInt(yearA)) // 对年份进行倒序排序
    .map(([year, projects]) => ({
      id: year, // 使用年份作为节点的 id
      name: year, // 使用年份作为节点的名称
      children: projects.map((project) => ({
        id: project.id.toString(), // 确保 id 是字符串类型
        name: project.projectName || '未知项目', // 使用 projectName 作为子节点的名称
        startDate: project.startDate.join('-'), // 可选：添加开始日期
        endDate: project.endDate.join('-') // 可选：添加结束日期
      }))
    }))
}

// 获取左侧年份 只要开始年份固定type未0
const getYearList = async () => {
  const data = await ListApi.getYear({ type: 0 })
  // console.log('getYearList', data)
  deptList.value = []
  deptList.value.push(...handleTree(data))
  // 确保 DOM 更新后再进行点击操作
  await nextTick()
  // 获取第一条数据并模拟点击
  if (deptList.value.length > 0) {
    // ↓下面这部分是点击父元素下面的第一个元素
    // const firstNode = deptList.value[0].children ? deptList.value[0].children[0] : deptList.value[0]
    // if (firstNode.children) {
    //   // 如果是父节点，先展开
    //   treeRef.value!.toggleExpanded(firstNode.id)
    // }
    // // 等待展开动画完成
    // await nextTick()
    // handleNodeClick(firstNode, { level: firstNode.children ? 1 : 2 })
    // // 设置高亮
    // treeRef.value!.setCurrentKey(firstNode.id)
    // ↑上面这部分是点击父元素下面的第一个元素
    // ↓下面这部分是直接点击第一个父元素
    // 直接获取第一个父节点
    const firstNode = deptList.value[0]
    // 模拟点击第一个父节点
    handleNodeClick(firstNode, { level: 1 })
    // 设置高亮
    treeRef.value!.setCurrentKey(firstNode.id)
  }
}

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}

/** 处理部门被点击 */
// const handleNodeClick = async (row: { [key: string]: any }, node: any) => {
//   if (node.level === 1) {
//     // 顶层父级节点点击时只进行展开或收缩
//     treeRef.value!.toggleExpanded(row.id)
//   } else {
//     // 子节点点击时触发接口调用
//     emits('node-click', row)
//   }
// }
const handleNodeClick = async (row: { [key: string]: any }, node: any) => {
  if (node.level === 1) {
    // 点击父节点：传递父节点和所有子节点
    const children = row.children || []
    emits('node-click', { ...row, children }) // 包含 children 数组
  } else {
    // 子节点：仅传递自己
    emits('node-click', row)
  }
}
const emits = defineEmits(['node-click'])

/** 监听deptName */
watch(deptName, (val) => {
  treeRef.value!.filter(val)
})

/** 初始化 */
onMounted(async () => {
  await getYearList()
})
</script>

<style lang="scss" scoped>
main {
  width: 100%;
  height: 100%;
}
:deep(.el-tree-node__content) {
  margin-bottom: 3px; //左侧列表每个增加3px的间隔
}
/* 自定义高亮样式 */
:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background-color: white !important;
}

// 溢出隐藏
.tree-node-text {
  display: inline-block;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
