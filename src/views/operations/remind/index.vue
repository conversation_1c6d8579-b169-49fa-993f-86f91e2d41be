<template>
  <ContentWrap class="my-component">
    <header>
      <p>项目到期提醒设置</p>
    </header>
    <main>
      <ContentWrap class="search-box">
        <div class="title-box"> 提醒时间设置(设置提醒时间后,项目到期前会限制为临近过保状态) </div>
        <div class="form-box">
          项目到期前
          <el-form :model="formSearch" class="form-search">
            <el-form-item>
              <el-input
                v-model="formSearch.value"
                placeholder=""
                @input="handleInput"
                type="number"
                min="0"
              />
            </el-form-item>
          </el-form>
          天进行提醒
        </div>
      </ContentWrap>
      <div class="table-box">
        <ContentWrap>
          <div class="title-box"> 提醒方式设置</div>
          <el-form :model="ruleForm" label-width="100px">
            <el-form-item prop="type">
              <el-checkbox-group v-model="ruleForm.type">
                <el-checkbox value="1" name="type">消息推送</el-checkbox>
                <br />
                <el-checkbox value="2" name="type">短信</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </ContentWrap>
        <div class="btn-box">
          <el-button type="primary" @click="submitForm">确认设置</el-button>
        </div>
      </div>
    </main>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as RegionalApi from '@/api/operations/regional'
import * as EquipmentApi from '@/api/operations/equipment'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

onMounted(async () => {
  await getList()
})
// const objData = ref({})

const getList = async () => {
  try {
    const res = await EquipmentApi.getEquipmentSource(formSearch.value)
    formSearch.value = res[0]
    // console.log('数据', formSearch.value)
    initLabelData()
  } finally {
  }
}

// 初始化 label 数据
const initLabelData = () => {
  const labelStr = formSearch.value.label
  // console.log('打印labelStr', labelStr)
  let labelArray = []
  if (labelStr) {
    // labelArray = labelStr.split(',').map(Number)
    labelArray = labelStr.split(',')
  }
  // console.log('打印ruleForm', ruleForm.value)
  // 重新赋值整个对象，确保响应式生效
  ruleForm.value = {
    type: labelArray
  }
  // console.log('打印2ruleForm', ruleForm.value)
}

const formSearch = ref({
  label: '', //多选框
  value: '', //天数
  dictType: 'projectNearDays' //固定的
})

const ruleForm = ref({
  type: [] // 默认为空数组
})

// 输入框输入事件
const handleInput = (event) => {
  // 移除所有非数字字符
  formSearch.value.value = event.replace(/\D/g, '')
}

// 提交保存
const submitForm = async () => {
  // 将数组转换为逗号字符串
  formSearch.value.label = ruleForm.value.type.join(',')
  try {
    // 调用接口提交 formSearch.value
    await RegionalApi.updateItem(formSearch.value)
    ElMessage.success('保存成功')
    getList()
  } catch (error) {
    ElMessage.error('保存失败')
  }
}
</script>

<style lang="scss" scoped>
.border-style {
  border: 1px solid #ccc;
  border-radius: 1%;
}
.my-component {
  // width: 100%;
  // height: 100%;
  background-color: white;
  header {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: bold;
    padding-left: 20px;
  }
  main {
    width: 100%;
    height: 530px;
    display: flex;
    flex-direction: column;
    // overflow: auto;

    .search-box {
      width: 100%;
      height: 120px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;

      .title-box {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        position: relative;
        font-family: Microsoft YaHei;
        // font-weight: bold;
        font-size: 16px;
        color: #3b3c3d;
        text-align: left;
        font-style: normal;
        text-transform: none;
        line-height: 24px;
        margin: 3px 0;
        padding-left: 28px;

        &::before {
          margin: 10px 0 0 10px;
          content: '';
          position: absolute;
          left: 0;
          top: 6px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #005fff;
          border: 2px solid #d4e4ff;
        }
      }
      .form-box {
        padding-left: 26px;
        display: flex;
        align-items: center;
        .form-search {
          padding-top: 20px;
          width: 100px;
          margin: 0 10px;
        }
      }
    }
    .table-box {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .title-box {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        position: relative;
        font-family: Microsoft YaHei;
        // font-weight: bold;
        font-size: 16px;
        color: #3b3c3d;
        text-align: left;
        font-style: normal;
        text-transform: none;
        line-height: 24px;
        margin: 3px 0;
        padding-left: 28px;

        &::before {
          margin: 10px 0 0 10px;
          content: '';
          position: absolute;
          left: 0;
          top: 6px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #005fff;
          border: 2px solid #d4e4ff;
        }
      }
      .btn-box {
        width: 100%;
      }
    }
  }
}
</style>
