<template>
  <ContentWrap class="my-component">
    <header>
      <p>运维区域列表</p>
    </header>

    <main>
      <ContentWrap class="search-box">
        <el-form :model="formSearch" class="form-search">
          <el-row>
            <el-col :span="12">
              <el-form-item>
                <el-input v-model="formSearch.value" placeholder="输入区域名称" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <XButton
                class="text-button"
                preIcon="ep:search"
                title="查询"
                width="75px"
                gradient
                @click="onSubmit"
                v-hasPermi="['infra:device-dict:query']"
              />

              <el-button @click="resetForm"><Icon icon="ep:refresh" />重置</el-button>
            </el-col>
          </el-row>
        </el-form>
      </ContentWrap>
      <div class="table-box">
        <!-- load -->
        <el-table
          class="table-style"
          :data="tableData"
          style="width: 100%"
          v-loading="loading"
          row-key="id"
          lazy
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :header-cell-style="{
            'background-color': '#F4F6F8',
            'font-size': '14px',
            color: '#3B3C3D'
          }"
        >
          <el-table-column prop="name" label="区域名称" />
          <el-table-column prop="label" label="区域编码" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button
                link
                type="primary"
                size="small"
                @click="handleAddChild(scope.row)"
                v-hasPermi="['infra:device-dict:create']"
              >
                添加子级
              </el-button>
              <el-button
                link
                type="primary"
                size="small"
                @click="handleAddSibling(scope.row)"
                v-hasPermi="['infra:device-dict:create']"
              >
                添加同级
              </el-button>
              <!-- 如果当前行没有 children，则显示编辑和删除按钮 -->
              <template v-if="!scope.row.children || scope.row.children.length === 0">
                <el-button
                  link
                  type="primary"
                  size="small"
                  @click="handleEdit(scope.row)"
                  v-hasPermi="['infra:device-dict:update']"
                >
                  编辑
                </el-button>
                <el-button
                  link
                  type="primary"
                  size="small"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['infra:device-dict:delete']"
                >
                  删除
                </el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </main>

    <!-- 对话框 -->
    <el-dialog v-model="showDialog" :title="dialogTitle" width="30%">
      <el-form :model="form" :rules="rules" ref="formRef">
        <el-form-item label="父级区域编码" :label-width="formLabelWidth">
          <el-input v-model="form.label" disabled />
        </el-form-item>
        <el-form-item label="父级区域名称" :label-width="formLabelWidth">
          <el-input v-model="form.value" disabled />
        </el-form-item>
      </el-form>
      <!-- 其他表单项 -->
      <el-form :model="form2" :rules="rules" ref="formRef">
        <el-form-item label="区域编码" prop="label" :label-width="formLabelWidth">
          <el-input v-model="form2.label" autocomplete="off" />
        </el-form-item>
        <el-form-item label="区域名称" prop="value" :label-width="formLabelWidth">
          <el-input v-model="form2.value" autocomplete="off" />
        </el-form-item>
        <!-- 其他表单项 -->
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">确认</el-button>
          <el-button @click="showDialog = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import * as RegionalApi from '@/api/operations/regional'
import { GetDicByDictTypeRes } from '@/api/operations/regional/index'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const params = reactive({
  dictType: 'oamArea'
})

onMounted(() => {
  getList()
  window.addEventListener('keydown', handleGlobalKeyDown)
})
onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleGlobalKeyDown)
})
const handleGlobalKeyDown = (event) => {
  if (event.key === 'Enter') {
    onSubmit()
  }
}
const searchTableDate = ref([]) //用于作为搜索展示的数据
const loading = ref(true)
const getList = async () => {
  loading.value = true
  try {
    const res = await RegionalApi.getList(formSearch.value)
    tableData.value = res as GetDicByDictTypeRes
    searchTableDate.value = res as GetDicByDictTypeRes
  } finally {
    loading.value = false
  }
}

const formSearch = ref({
  label: '',
  value: '',
  dictType: 'oamArea'
})

// 表单数据
const form = reactive({
  label: '', //父级区域编码名称
  value: '' //父级名称
})
const form2 = reactive({
  id: '', //编辑时的id
  parentId: '', // 父级ID
  label: '', // 子级区域编码名称
  value: '', // 子级名称
  dictType: 'oamArea', // 类型
  operationType: '' // 操作类型：'addChild', 'addSibling', 'edit'
})

const formLabelWidth = '120px'
const showDialog = ref(false)
const formRef = ref(null)
const rules = {
  label: [{ required: true, message: '请输入区域编码', trigger: 'blur' }],
  value: [{ required: true, message: '请输入区域名称', trigger: 'blur' }]
}

const dialogTitle = ref('编辑区域')
const tableData = ref<GetDicByDictTypeRes | null>(null)

const handleAddChild = (row) => {
  dialogTitle.value = '新增区域'
  form.label = row.label // 父级区域编码
  form.value = row.name // 父级区域名称
  form2.parentId = row.id
  form2.label = ''
  form2.value = ''
  form2.operationType = 'addChild'
  showDialog.value = true
}

const handleAddSibling = (row) => {
  dialogTitle.value = '新增区域'
  // 获取当前行的父级区域信息
  const parentRow = getParentRow(row) // 获取父级行
  form.label = parentRow ? parentRow.label : '' // 父级区域编码
  form.value = parentRow ? parentRow.name : '' // 父级区域名称
  form2.parentId = parentRow ? parentRow.id : 0 // 父级ID
  form2.label = '' // 新增区域的编码
  form2.value = '' // 新增区域的名称
  form2.id = '' // 新增区域的名称
  form2.operationType = 'addSibling'
  showDialog.value = true
}
const getParentRow = (row) => {
  const stack = [tableData.value] // 使用栈来实现深度优先搜索
  while (stack.length > 0) {
    const current = stack.pop()
    for (let i = 0; i < current.length; i++) {
      const item = current[i]
      if (item.children && item.children.length > 0) {
        stack.push(item.children)
      }
      if (item.children && item.children.some((child) => child.id === row.id)) {
        return item // 找到父级行
      }
    }
  }
  return null // 如果没有找到父级行，返回 null
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑区域'
  const parentRow = getParentRow(row) // 获取父级行
  form.label = parentRow ? parentRow.label : '' // 父级区域编码
  form.value = parentRow ? parentRow.name : '' // 父级区域名称
  form2.parentId = parentRow ? parentRow.id : 0 // 父级ID
  form2.label = row.label // 回填子级区域的编码
  form2.value = row.name // 回填子级区域的名称
  form2.id = row.id
  form2.operationType = 'edit'
  showDialog.value = true
}

const handleDelete = async (row) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    await RegionalApi.deleteList({ id: row.id })
    message.success(t('common.delSuccess'))
    await getList() // 刷新列表数据
  } catch (error) {
    ElMessage.error('删除失败，请稍后再试')
  }
}

const handleSubmit = async () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (form2.operationType === 'addChild' || form2.operationType === 'addSibling') {
          await RegionalApi.addList(form2)
        } else if (form2.operationType === 'edit') {
          await RegionalApi.updateItem(form2)
        }
        await getList()
        ElMessage.success('操作成功')
        showDialog.value = false
      } catch (error) {}
    } else {
      // ElMessage.error('表单验证失败')
    }
  })
}
// 重置
const resetForm = () => {
  formSearch.value.label = ''
  formSearch.value.value = ''
  getList()
}
// 递归搜索数据函数
const searchData = (data, query) => {
  if (!query) return data // 如果没有搜索内容，返回全部数据
  const result = []
  const searchRecursive = (items) => {
    items.forEach((item) => {
      if (item.name.toLowerCase().includes(query.toLowerCase())) {
        result.push(item) // 如果当前项匹配，加入结果
      }
      if (item.children && item.children.length > 0) {
        searchRecursive(item.children) // 递归搜索子项
      }
    })
  }
  searchRecursive(data)
  return result
}
const onSubmit = () => {
  const newData = searchData(searchTableDate.value, formSearch.value.value)
  tableData.value = newData
}

watch(showDialog, (newValue) => {
  if (!newValue) {
    // 弹窗关闭时重置表单数据
    form.value = { label: '', value: '', id: '', name: '' }
    // 清除验证状态
    if (formRef.value) {
      formRef.value.resetFields()
    }
  }
})
</script>

<style lang="scss" scoped>
.border-style {
  border: 1px solid #ccc;
  border-radius: 1%;
}
.my-component {
  // width: 100%;
  // height: 100%;
  background-color: white;
  header {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    // border-bottom: 1px solid #ccc;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: bold;
    padding-left: 20px;
  }
  main {
    width: 100%;
    height: 530px;
    display: flex;
    flex-direction: column;
    overflow: auto;
    // padding-left: 20px;
    // padding-right: 40px;
    .search-box {
      width: 100%;
      height: 50px;
      display: flex;
      flex-direction: row;
      // align-items: center;
      align-items: flex-start;
      margin-bottom: 10px;

      .form-search {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;

        .el-form-item {
          margin-right: 10px;
        }
      }
    }
    .table-box {
      flex: 1;
      .table-style {
        :deep(.el-table__header th) {
          text-align: left;
        }
        :deep(.el-table__body td) {
          text-align: left;
        }
      }
    }
  }
}
// 查询按钮样式
.text-button {
  color: #fff;
}
</style>
