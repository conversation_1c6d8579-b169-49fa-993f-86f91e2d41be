<template>

  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      class="flex flex-wrap items-start -mb-15px"
      :inline="true"
      :model="queryParams"
    >
      <el-form-item label="" prop="name" class="!mr-3">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="输入设备编号/名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属区域" prop="status" class="!mr-3">
        <el-select v-model="queryParams.status" class="!w-140px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="设备类型" prop="status" class="!mr-3">
        <el-select v-model="queryParams.status" class="!w-140px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属项目" prop="status" class="!mr-3">
        <el-select v-model="queryParams.status" class="!w-140px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="设备状态" prop="status" class="!mr-3">
        <el-select v-model="queryParams.status" class="!w-140px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>



        <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
          <div>
            <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:device-overview-info:query']" gradient @click="handleQuery" />
            <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
          </div>
     <div>
       <el-button plain v-hasPermi="['infra:device-overview-info:export']" @click="handleExport" :loading="exportLoading">
         <Icon icon="ep:download" />导出
       </el-button>
     </div>

      </div>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table ref="myTable" v-loading="loading" @selection-change="selectionChange" :data="list">
      <el-table-column align="left" type="selection"   width="55" />
      <el-table-column type="index" label="序号" width="55" align="left">
        <template #default="scope">
          <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column align="left" label="设备编号" prop="code" />
      <el-table-column align="left" label="设备名称" prop="name" />
      <el-table-column align="left" label="设备类型" prop="name" />
      <el-table-column align="left" label="所属区域" prop="name" />
      <el-table-column align="left" label="所属项目" prop="name" />
      <el-table-column align="left" label="维修次数" prop="name" />
      <el-table-column align="left" label="设备状态" prop="name" />
      <el-table-column align="left" label="最新一次维修时间" prop="name" />

      <el-table-column :width="120" align="left" label="操作">
        <template #default="scope">
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openDetails(scope.row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <!-- 关联设备弹窗 -->
  <Dialog v-model="dialogVisible" width="1000" title="关联设备">
    <div style="display: flex;justify-content: space-between;">
      <el-form
      ref="devicesFormRef"
      :inline="true"
      :model="devicesParams"
      label-width="48px"
    >
      <el-form-item label="" prop="name">
        <el-input
          v-model="devicesParams.name"
          class="!w-140px"
          clearable
          placeholder="输入资产编号/名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型" prop="status">
        <el-select v-model="devicesParams.status" class="!w-140px" clearable placeholder="请选择">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="范围" prop="status">
        <el-select v-model="devicesParams.status" class="!w-140px" clearable placeholder="请选择">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

    </el-form>
      <div class="mb-4">
        <el-button type="primary">查询</el-button>
        <el-button type="primary">重置</el-button>
      </div>
    </div>
    <el-table  class="devicesTable" ref="devicesTable" v-loading="devicesLoading" @selection-change="selectionChange" :data="deviceslist">
      <el-table-column align="left" type="selection"   width="55" />
<!--      <el-table-column type="index" label="序号" width="55" align="left">-->
<!--        <template #default="scope">-->
<!--          <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column align="left" label="资产编号" prop="code" />
      <el-table-column align="left" label="资产名称" prop="name" />
      <el-table-column align="left" label="所属项目" prop="name" />
      <el-table-column align="left" label="资产类型" prop="name" />
    </el-table>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <el-button  style="width: 200px;" type="primary" >确定</el-button>
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { useRouter } from "vue-router";
import * as RoleApi from '@/api/system/role'

import {modifyStatus} from "@/api/system/role";

defineOptions({ name: 'PluginList' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // vue-router
const loading = ref(true) // 列表的加载中
const devicesLoading = ref(false) // 列表的加载中
const dialogVisible = ref(false) //  关联设备弹窗的显示隐藏
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const deviceslist = ref([]) // 列表的数据
const selectionList = ref([]) // 列表选中的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined,
  createTime: []
})
const devicesParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中


/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
/** 新增按钮操作 */
const openAdd = () => {
  router.push('AccessoriesAdd')
}
/** 详情按钮操作 */
const openDetails = (value:number) => {
  console.log('value',value)
  router.push('RepairListDetails')
}
/** 关联设备按钮 */
const openDevices = (value:number) => {
  console.log('value',value)
  dialogVisible.value = true
}

/** 表格选中数据 */
const selectionChange = (value) => {
  selectionList.value = value
  console.log('选中数据',selectionList.value)
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}


const myTable=ref()
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  if (selectionList.value.length <1) {
    message.warning('请选择要删除的角色！')
  }else {
    const ids = selectionList.value.map((item) => item.id).join(',')
    try {
      // 删除的二次确认
      await message.delConfirm()
      // 发起删除
      // await RoleApi.deleteRole(ids)
      message.success(t('common.delSuccess'))
      // 刷新列表
      // await getList()
    } catch {
      myTable.value.clearSelection();
    }
  }
}



//单个状态修改
const SingleState = async (type: string,row:any) => {
  console.log('sssss',row)
  let parms={...row,status:type== 'Disable'? '1' : '0'}
  try {
    // 操作的二次确认
    await message.confirm('你确定要操作该角色状态吗？')
    // 发起删除
    await modifyStatus([ parms])
    message.success('操作成功！')
    // 刷新列表
    await getList()
  } catch {

  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // const data = await PostApi.getList(deptName.value)
    // console.log('排班管理',data)
    // list.value = data
    // total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await RoleApi.exportRole(queryParams)
    download.excel(data, '角色列表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
<style scoped>
.devicesTable{

}

</style>
