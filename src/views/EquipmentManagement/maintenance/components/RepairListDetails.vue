<template>
  <div class="box">
    <header>
<!--      <el-button class="elbutten" type="primary" @click="goback()">-->

<!--      </el-button>-->
      <el-button type="info" :icon="ArrowLeft" @click="goback()" plain>返回</el-button>
      <div class="itmdiv">辅件列表-详情</div>
      <div class="rightbutten">
        <el-button type="primary" @click="validateForms" >保存</el-button>
        <el-button @click="onReset" >重置</el-button>
      </div>
    </header>

    <div class="banner">
      <div class="left">
        <!-- top -->
        <div class="top">
          <p class="head">
            <span></span>
            <span>资产详情</span>
          </p>
          <div  class="MaintenanceRecord">
            <div v-for="(itm,index) in leftList" :key="index" style="background: #f2f2f2; padding: 8px; ">
              <div class="BasicFields" v-if="dataList">
                <!--              <div class="txteStyle" style="width: 55%"><span class="keyStyle">{{itm.label+ ':'}}</span><div class="centerStyle">{{dataList.assertInfo[itm.key] }}</div></div>-->
                <!--              <div class="txteStyle" v-if="itm.label2" ><span class="keyStyle">{{itm.label2+ ':'}}</span><div class="centerStyle">{{dataList.assertInfo[itm.key2]}}</div></div>       -->
                <div class="txteStyle" style="width: 55%"><span class="keyStyle">{{itm.label+ ':'}}</span><div class="centerStyle">{{dataList[itm.key] }}</div></div>
                <div class="txteStyle" v-if="itm.label2" ><span class="keyStyle">{{itm.label2+ ':'}}</span><div class="centerStyle">{{dataList[itm.key2]}}</div></div>
              </div>
            </div>
          </div>

        </div>
        <div class="top" style="margin-top: 20px">
          <p class="head">
            <span></span>
            <span>维修记录</span>
          </p>
          <div class="MaintenanceRecord">
<!--          <el-scrollbar height="500px" style="padding-left: 30px;">-->
            <div class="record" v-for="(itm,index) in 5" :key="index">
              <div class="record-box">
                <div>工单编号：xxxxxxx</div>
                <div>维修中</div>
              </div>
              <div class="mage-top">维修开始：2025/1/1 11:00:00</div>
              <div class="mage-top">维修结束:---</div>
              <div class="mage-top">维修人员:xxx部门-张三</div>
            </div>
<!--            </el-scrollbar>-->
          </div>
        </div>
        <!-- bottom -->

      </div>
      <div class="right">
        <div class="top">
<!--          <p class="head">-->
<!--            <span></span>-->
<!--            <span>资产二维码</span>-->

<!--            <span class="btn">-->
<!--           <el-button type="primary" size="small" @click="propertySave" plain>保存</el-button>-->
<!--           <el-button type="primary" size="small" @click="codeExport" plain>导出</el-button>-->
<!--            </span>-->
<!--          </p>-->
          <div>
            <el-tabs tab-position="top" v-model="activeName" @tab-click="handleClick" class="demo-tabs">
              <el-tab-pane
                v-for="itm in Template"
                :key="itm.id"
                :label="itm.modelName"
                :name="itm.id"
              >
<!--                <div v-if="itm.modelUrl" class="imgContainer"><img :src="itm.modelUrl" alt=""></div>-->
<!--                <div v-else>暂未获取到图片</div>-->
              </el-tab-pane>

            </el-tabs>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive,toRefs,onBeforeMount,onMounted,watch,ref} from 'vue'
import { Search ,CirclePlus,Edit,Delete, ArrowLeft} from '@element-plus/icons-vue'
import {useRouter,useRoute} from "vue-router";
import {ElMessage} from "element-plus";
// import {EditUpdate, getDetails, getTypedropdown} from "@/api/AssetList/index";
const { t } = useI18n() // 国际化

const router = useRouter();
const routers = useRoute();
let id= ref('')
const activeName = ref('');
let Template =ref([]  )
let queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
})
const list = ref([{code:'ssssss11',name:'杆件'}]) // 列表的数据
const dataList = ref({
  name:'JKSB2023001',
  code:'神威监控项目',
  orgName:'测试单位',
  projectName:'测试项目',
  areaName:'测试位置',
  address:'测试详细位置',
  typeName:'测试类型'

})
const leftList = ref(   [
  {
    label: '资产名称',
    key: 'name',
    label2: '资产编号',
    key2: 'code',

  },
  {
    label: '所属项目',
    key: 'orgName',
    label2: '所属单位',
    key2: 'projectName',
  },
  {
    label: '类型',
    key: 'areaName',
    label2: '分组',
    key2: 'address',
  },
  {
    label: '位置',
    key: 'longitude',
    label2: '详细位置',
    key2: 'latitude',
  },
  {
    label: '经纬度',
    key: 'longitude',
    label2: '品牌',
    key2: 'latitude',
  },
  {
    label: '型号',
    key: 'longitude',
    label2: '用途',
    key2: 'latitude',
  },
  {
    label: '设备状态',
    key: 'longitude',
    label2: '设备类型',
    key2: 'latitude',
  },
  {
    label: '设备登录名',
    key: 'longitude',
    label2: '使用日期',
    key2: 'latitude',
  },
  {
    label: 'IP地址',
    key: 'longitude',
    label2: '设备登录密码',
    key2: 'latitude',
  },
  {
    label: '质保单位',
    key: 'longitude',
    label2: '备注',
    key2: 'latitude',
  },
  {
    label: '质保联系人',
    key: 'longitude',
    label2: '质保日期',
    key2: 'latitude',
  },
  {
    label: '责任单位',
    key: 'longitude',
    label2: '质保联系电话',
    key2: 'latitude',
  },
  {
    label: '责任单位联系方式',
    key: 'longitude',
    label2: '责任单位联系人',
    key2: 'latitude',
  },

])
//返回
const goback = () =>{
  router.go(-1)
}

let customFieldsRef = ref()


// watch(
//   () => data,
//   () => {
// // console.log('watch');
//   },
//   {deep: true,immediate: true,}
// );
onBeforeMount(() => {

})
onMounted(() => {

})
</script>
<style lang="scss" scoped>
.box{
  //background: #F5F6F8;
  //position: relative;
  height: 75vh;     // 设置容器高度为视口高度
  header{
    // width: 1616px;
    //position: sticky;
    //top: 10px;
    //left: 10px;
    //right: 0;
    //z-index: 100;
    height: 48px;
    background: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 16px;
    margin-bottom: 16px;
    .itmdiv{
      margin-left: 10px;
    }
    .elbutten{
      width: 80px;
      height: 32px;
      background: #EAEDF2;
      border-radius: 4px 4px 4px 4px;
      border: 0;
      font-size: 14px;
      color: #323336;
      padding-left: 0;
      img{
        width: 24px;
        height: 24px;
      }
      span{
        display: inline-block;
        margin-top: 2px;
      }
    }
    .rightbutten{
      margin-right: 10px;
    }
    p{
      font-weight: 400;
      font-size: 14px;
      margin-left: 24px;
      span{
        &:first-child{
          color: #9C9DA2;
        }
      }
    }
  }
  .banner{
    width: 100%;
    display: flex;
    .head{
      padding-bottom: 11px;
      border-bottom: 1px solid  #EAEDF2;
      margin:0 0 23px 0;
      display: flex;
      align-items: center;
      //justify-content: space-between;
      span{
        font-weight: bold;
        font-size: 16px;
        color: #323336;
        &:first-child{
          display: inline-block;
          width: 3px;
          height: 16px;
          background: linear-gradient( 180deg, #16B9FA 0%, #0058FF 100%);
          border-radius: 2px 2px 2px 2px;
          margin-right: 9px;
        }
      }
    }
    .left{
      flex: 1;
      // padding: 16px 24px;
      margin-right: 16px;
      .top,.bottom{
        min-height: 400px;
        background: #FFFFFF;
        padding: 16px 24px;;
        .BasicFields{
          //margin-bottom: 10px;

          display: flex;
          .txteStyle{
            display: flex;
            width: 45%;
            .keyStyle{
              min-width: 90px;
            }
            .centerStyle{
              font-size: 16px;
              color: #9C9DA2;
              word-wrap: break-word;
              white-space: normal;
              text-align: left;
              max-width: 350px;

              margin-left: 24px;
            }
          }
        }
        .MaintenanceRecord{
          width: 100%;
          max-height: 350px;
          overflow: auto;
          padding-right: 10px;
          .record{
            margin-top: 15px;
            width: 100%;
            //height: 60px;
            padding: 20px;
            background-color: #f2f2f2;
            .record-box{
              display: flex;
              justify-content: space-between;
              align-items: center;
            }
            .mage-top{
              margin-top: 10px;

            }

          }
        }

      }
      .bottom{
        margin-top: 16px;
        min-height: 304px;
      }
    }
    .right{
      width: 400px;
      background: #FFFFFF;
      padding: 20px;
      .btn{
        margin-left: 49%;
      }
      .imgContainer{
        width: 100%;
        max-height: 400px;
        overflow: auto;
        img {
          display: block; /* 去��底部间隙 */
          margin: 0 auto; /* 水平居中 */
          max-width: 100%; /* 最大宽度为容器的100% */
          height: auto; /* 高度自动调整以保持宽高比 */
        }
      }
      .el-timeline{
        padding-left: 0;
        .el-timeline-item{
          padding-bottom: 48px;
        }
        .el-timeline-item__node--primary{
          color: #0058FF;
        }
        .el-timeline-item__node.is-hollow{
          border-width: 3px;
        }
        .el-timeline-item__timestamp{
          font-size: 13px;
        }
      }
    }
  }
}
/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
