<template>
<div>
  <ContentWrap>
  <el-tabs v-model="activeName" class="demo-tabs" >
    <el-tab-pane label="报废申请" name="first">
      <ScrapApplication/>
    </el-tab-pane>
    <el-tab-pane label="已报废" name="second">
      <AlreadyScrapped/>
    </el-tab-pane>
  </el-tabs>
  </ContentWrap>
</div>
</template>
<script lang="ts" setup>
import ScrapApplication from './components/ScrapApplication.vue';
import AlreadyScrapped from './components/AlreadyScrapped.vue';

defineOptions({ name: 'EquipmentScrapping' })


const activeName = ref('first') // 列表的总页数




/** 初始化 **/
onMounted(() => {
})
</script>
<style scoped>


</style>
