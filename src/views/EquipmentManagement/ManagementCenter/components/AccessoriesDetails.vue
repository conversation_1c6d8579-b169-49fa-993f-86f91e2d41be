<template>
  <div class="box">
    <!-- 头部导航 -->
    <header class="header">
      <div class="header-left">
        <el-button type="info" :icon="ArrowLeft" @click="goback()" plain>返回</el-button>
        <div class="title">辅件列表-详情</div>
      </div>
      <div class="header-right">
<!--        <el-button type="primary" @click="validateForms">保存</el-button>-->
<!--        <el-button @click="onReset">重置</el-button>-->
      </div>
    </header>

    <!-- 主体内容区 -->
    <div class="content-wrapper">
      <!-- 左侧详情 -->
      <div class="content-left">
        <!-- 辅件详情 -->
        <div class="detail-card" style="height: 200px;">
          <div class="card-header">
            <span class="header-bar"></span>
            <span>辅件详情</span>
          </div>
          <div class="detail-content">
            <el-scrollbar height="calc(35vh - 60px)">
              <div v-for="(itm, index) in leftList"
                   :key="index"
                   class="detail-item">
                <div class="detail-row">
                  <div class="detail-col">
                    <span class="label">{{itm.label}}:</span>
                    <span class="value">{{dataList[itm.key]}}</span>
                  </div>
                  <div class="detail-col" v-if="itm.label2">
                    <span class="label">{{itm.label2}}:</span>
                    <span class="value">{{dataList[itm.key2]}}</span>
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>

        <!-- 关联设备 -->
        <div class="detail-card">
          <div class="card-header">
            <span class="header-bar"></span>
            <span>关联设备</span>
          </div>
          <div class="table-content">
<!--            <el-scrollbar height="500px">-->
              <el-table
                ref="myTable"
                height="450"
                :data="list"
                size="small"
                :max-height="400">
                <el-table-column type="index" label="序号" width="55" align="center">
                  <template #default="scope">
                    <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="设备编号" prop="assetsCode" >
                  <template #default="scope">
                    <el-button text link @click="toDevice(scope.row.id)"  type="primary">{{scope.row.assetsCode}}</el-button>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="设备名称" prop="assetsName" />
                <el-table-column align="center" label="设备类型" prop="assetsTypeName" />
                <el-table-column align="center" label="所属区域" prop="area" />
                <el-table-column align="center" label="供应商" prop="supplierName" />
                <el-table-column align="center" label="规格型号" prop="modelName" />
                <el-table-column align="center" label="质保状态" prop="warrantyStatus">
                  <template #default="scope">
                    <span>{{scope.row.warrantyStatus=='0'? '质保中': '已过保'}}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="质保日期" prop="warrantyDate" />
                <el-table-column align="center" label="设备来源" prop="assetsSourceName" />
                <el-table-column align="center" label="标签" prop="name" />
              </el-table>
<!--            </el-scrollbar>-->
          </div>
        </div>
      </div>

      <!-- 右侧内容 -->
      <div class="content-right">
        <div class="picUrl">
          <img v-if="dataList.picUrl" :src="dataList.picUrl"  class="picture" alt="" />
          <img v-else src="@/assets/imgs/device.png" class="picture" alt="" />
        </div>
        <div class="picUrl TextEditing" >
          <el-scrollbar height="600px">
            <div class="OperationLog" v-for=" item in logList" :key="item.id">
              <div class="round"></div>
              <div style="margin-left: 10px;">
                <div>由  {{ item.userName }}  {{ item.subType }}</div>
                <div style="color: #919AAA;font-size: 14px;">{{formatDate(item.createTime)}}</div>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive,toRefs,onBeforeMount,onMounted,watch,ref} from 'vue'
import { Search ,CirclePlus,Edit,Delete, ArrowLeft} from '@element-plus/icons-vue'
import {AccDetails,getPropertyPagination} from "@/api/EquipmentManagement/ManagementCenter";
import {useRouter,useRoute} from "vue-router";
import {ElMessage} from "element-plus";
import * as EquipmentApi from '@/api/operations/equipment'
import dayjs from "dayjs";
const { t } = useI18n() // 国际化

const router = useRouter();
const routers = useRoute();
let id= ref('')
const activeName = ref('');
let Template =ref([]  )
let queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
})
const list = ref([]) // 列表的数据
const dataList = ref({
  auxiliaryCode:'',
  auxiliaryName:'',
  assetsTypeName:'',
  areaName:'',

})
const leftList = ref(   [
  {
    label: '辅件编号',
    key: 'auxiliaryCode',
    label2: '辅件名称',
    key2: 'auxiliaryName',

  },
  {
    label: '辅件类型',
    key: 'assetsTypeName',
    label2: '所属区域',
    key2: 'areaName',
  },
])
//返回
const goback = () =>{
  router.go(-1)
}
let customFieldsRef = ref()
onBeforeMount(() => {

})

const formatDate = (timestamp) => {
  if (!timestamp) {
    return ''
  }
  // 日期存在，则进行格式化
  return timestamp ? dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss') : ''
}

// 获得日志
const logList = ref([])
const getLog = async (id) => {
  const data = await EquipmentApi.getLog({ bizId: id, type: 'AUXILIARY_MANAGE_CENTER' })
  console.log('日志', data)
  logList.value = data.list
}
const toDevice = (id) => {
  router.push({
    path: 'DeviceDetails',
    query: { id }
  })
}

onMounted(async () => {

if (routers.query.id){
  const data = await AccDetails(routers.query.id)
  dataList.value.auxiliaryCode = data.auxiliaryCode
  dataList.value.auxiliaryName = data.auxiliaryName
  dataList.value.assetsTypeName = data.assetsTypeName
  dataList.value.areaName = data.areaName
  dataList.value.picUrl = data.picUrl
  if (data.assetsIdList && data.assetsIdList.length > 0) {
    const res = await getPropertyPagination({ids:data.assetsIdList})
    list.value = res
  }
  getLog(routers.query.id)
}

})
</script>
<style lang="scss" scoped>
.box {
  display: flex;
  flex-direction: column;
  //height: 100vh;
  background: #f5f6f8;
  overflow: hidden;
}

.header {
  height: 48px;
  padding: 0 16px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);

  &-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .title {
    font-size: 16px;
    font-weight: 500;
  }
}

.content-wrapper {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  min-height: 0; // 重要：防止内容溢出
  overflow: hidden;
}

.content-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 800px;
  gap: 16px;
  min-width: 0; // 重要：防止内容溢出
}

.content-right {
  width: 280px;
  min-width: 280px;
  background: #fff;
  height: 800px;
  border-radius: 4px;

  @media screen and (max-width: 1200px) {
    width: 280px;
  }
  .picUrl{
    width: 260px;
    margin-top: 15px;
    margin-left: 10px;
    height: 150px;
    border: 1px solid #e9e9e9;
    overflow: hidden;
    .picture{
      width: 100%;
      height: auto;
      object-fit: contain;
    }
  }
  .TextEditing{
    height: 600px;
  }
  .OperationLog{
    //width: 260px;
    padding: 15px;
    display: flex;
    .round{
      width: 10px;
      height: 10px;
      margin-top: 7px;
      border-radius: 50%;
      background: #0099ff;
    }

  }
}

.detail-card {
  background: #fff;
  border-radius: 4px;
  height: calc(80% - 8px);
  display: flex;
  flex-direction: column;
}

.card-header {
  padding: 16px;
  border-bottom: 1px solid #eaedf2;
  display: flex;
  align-items: center;
  gap: 8px;
  span{
    font-weight: bold;
    font-size: 16px;
    color: #323336;
  }
  .header-bar {
    width: 3px;
    height: 16px;
    background: linear-gradient(180deg, #16B9FA 0%, #0058FF 100%);
    border-radius: 2px;
  }
}

.detail-content {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}

.detail-item {
  background: #f2f2f2;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.detail-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.detail-col {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: flex-start;

  .label {
    min-width: 90px;
    color: #666;
  }

  .value {
    flex: 1;
    color: #333;
    word-break: break-all;
  }
}

// 响应式布局
@media screen and (max-width: 1024px) {
  .content-wrapper {
    flex-direction: column;
  }

  .content-right {
    width: 100%;
    height: 300px;
  }

  .detail-col {
    min-width: 100%;
  }
}
/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
