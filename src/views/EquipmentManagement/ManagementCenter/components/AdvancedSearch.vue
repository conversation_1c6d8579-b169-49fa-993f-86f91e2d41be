<template>
  <Dialog v-model="dialogVisible" width="1000" title="高级搜索">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      label-width="100px"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="设备编号:" prop="assetsCode">
            <el-input v-model="formData.assetsCode" placeholder="请输入设备编号" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="设备名称:" prop="assetsName">
            <el-input v-model="formData.assetsName" maxlength="11" placeholder="设备名称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="设备类型:" prop="mobile">
            <el-cascader :options="DeviceType" v-model="assetsTypeId" @change="handleChange" :props="props1" style="width: 100%;"  collapse-tags clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="所属区域:" prop="roleIds">
            <Dictionary
              v-model="formData.areaId"
              type="cascader"
              dict-type="oamArea"
              :cascader-props="{
              multiple: true,
              label:'name',
              value: 'id',
             }"
              :max-collapse-tags="2"
              placeholder="请选择区域"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="供应商:" prop="supplierId">
            <Dictionary
              v-model="formData.supplierId"
              type="select"
              dict-type="supplierDirectory"
              placeholder="请选择供应商"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="规格型号:" prop="modelId">
            <Dictionary
              v-model="formData.modelId"
              type="select"
              dict-type="modelConfig"
              placeholder="请选择规格型号"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="质保状态:" prop="deptId">
            <el-select  v-model="formData.warrantyStatus"  placeholder="请选择质保状态">
              <el-option
                v-for="item in warrantyList"
                :key="item.id"
                :label="item.name"
                :value="item.id!"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="质保日期:" prop="deptId">
            <el-date-picker
              v-model="TimeValue"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="设备来源:" prop="deptId">
            <Dictionary
              v-model="formData.assetsSource"
              type="select"
              dict-type="deviceSource"
              placeholder="请选择设备来源"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="是否重点:" prop="deptId">
            <el-select
              v-model="formData.isKey"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="dict in keynoteList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="标签:" prop="deptId">
            <Dictionary
              v-model="formData.labelId"
              type="cascader"
              dict-type="deviceLabel"
              :cascader-props="{
              multiple: true,

              label:'name',
              value: 'id',
             }"
              :max-collapse-tags="2"
              placeholder="请选择标签"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <XButton title="确定" width="200px" gradient  @click="submitForm" />
<!--        <el-button :disabled="formLoading" style="width: 200px;" type="primary">确定</el-button>-->
        <el-button style="width: 200px;" @click="resetForm">重置</el-button>
      </div>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import {getDeviceType} from "@/api/EquipmentManagement/ManagementCenter/index";
import {ref} from "vue";
defineOptions({ name: 'AdvancedSearch' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const props1 = {
  value: 'id',
  label: 'name',
  children: 'children',
  multiple: true
}
const dialogVisible = ref(false) // 弹窗的是否展示
const TimeValue = ref('') // 质保时间
const assetsTypeId = ref([]) // 设备类型接收值
const warrantyList = ref([{name:'质保中',id:'0'}, { name:'已过保',id:'1'}])
const DeviceType = ref([])  //设备类型
let keynoteList=ref([{
  label: '非重点',
  value: '0'
}, {
  label: '重点',
  value: '1'
}])
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  assetsCode: '',
  assetsName: '',
  assetsTypeId: [],
  warrantyDateStart:'',
  warrantyDateEnd:'',
  areaId: [],
  supplierId: '',
  modelId: '',
  warrantyStatus: '',
  assetsSource: '',
  isKey: '',
  labelId: []
})

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async () => {
  formData.value={
    assetsCode: '',
    assetsName: '',
    assetsTypeId: [],
    warrantyDateStart:'',
    warrantyDateEnd:'',
    areaId: [],
    supplierId: '',
    modelId: '',
    warrantyStatus: '',
    assetsSource: '',
    isKey: '',
    labelId: []}
  assetsTypeId.value=[]
  TimeValue.value=[]
  dialogVisible.value = true
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {


  let params = {...formData.value,
    warrantyDateStart:TimeValue.value[0],
    warrantyDateEnd:TimeValue.value[1],
  }
  // 提交请求
  formLoading.value = true
  try {
    // const data = formData.value as unknown as UserApi.UserVO
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success',params)
  } finally {
    formLoading.value = false
  }
}
const handleChange = (val) => {
    // 1. 展平数组
     const flatArray = val.flat()
    // 2. 去重
     const uniqueArray = [...new Set(flatArray)]
    // 3. 过滤掉无效值（如 null、undefined）
     const validArray = uniqueArray.filter(item => item != null)
     formData.value.assetsTypeId=validArray

}


onMounted(async () => {
  // 加载设备类型数据
  DeviceType.value = await getDeviceType()
})

/** 重置表单 */
const resetForm = () => {
  formData.value={
    assetsCode: '',
    assetsName: '',
    assetsTypeId: [],
    warrantyDateStart:'',
    warrantyDateEnd:'',
    areaId: [],
    supplierId: '',
    modelId: '',
    warrantyStatus: '',
    assetsSource: '',
    isKey: '',
    labelId: []}
  assetsTypeId.value=[]
  TimeValue.value=[]
  formRef.value?.resetFields()
}
</script>
