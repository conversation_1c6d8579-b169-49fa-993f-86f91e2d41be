<template>

  <div class="box">
    <!-- 头部导航 -->
    <header class="header">
      <div class="header-left">
        <el-button type="info" :icon="ArrowLeft" @click="goback()" plain>返回</el-button>
        <div class="title">设备列表-详情</div>
      </div>
      <div class="header-right">
      </div>
    </header>

    <!-- 主体内容区 -->
    <div class="content-wrapper">
      <!-- 左侧详情 -->
      <div class="content-left">
        <!-- 辅件详情 -->
        <div class="detail-card" style="height: 800px;">
          <div style="display: flex;align-items: center;justify-content: space-between;border-bottom: 1px solid #eaedf2;">
            <div class="card-header">
              <span class="header-bar"></span>
              <span>设备详情</span>
            </div>
            <div style="padding-right: 10px;">
              <el-button v-if="Hikvision" @click="onVideo">视频预览</el-button>
            </div>
          </div>

          <div class="detail-content">
            <PrivateShow :id="routers.query.id" />
          </div>
        </div>

      </div>

      <!-- 右侧内容 -->
      <div class="content-right">
        <div class="picUrl">
          <img v-if="pictureUrl" :src="pictureUrl" alt="" style="width: 100%;height: 100%"/>
          <img v-else src="@/assets/imgs/device.png" style="width: 100%;height: 100%" alt="" />
        </div>
        <div class="picUrl TextEditing" >
          <el-scrollbar height="600px">
         <div class="OperationLog" v-for=" itm in logList" :key="itm.id">
           <div class="round"></div>
           <div style="margin-left: 10px;">
             <div>由  {{ itm.userName }}  {{ itm.subType }}</div>
             <div style="color: #919AAA;font-size: 14px;">{{formatMonthlyDate(itm.createTime)}}</div>
<!--             <div>{{item.createTime}}</div>-->
           </div>
         </div>
          </el-scrollbar>
        </div>
      </div>
    </div>
    <Dialog 
      v-model="dialogVisible" 
      width="900" 
      title="视频预览" 
      @opened="handleDialogOpened" 
      :before-close="handleDialogClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div id="Video-Preview" class="Video-Preview" style="width: 100%; height: 400px;">
<!--        <div  class="video-error">-->
<!--          1232131-->
<!--        </div>-->
<!--        <div v-if="!isPlaying && !videoError" class="video-loading">-->
<!--          正在加载视频...-->
<!--        </div>-->
      </div>
      <template #footer>
        <div style="display: flex;align-items: center;justify-content: center;">
          <el-button style="width: 150px;" @click="closeDialog">关闭</el-button>
        </div>
      </template>
    </Dialog>
  </div>
</template>

<script setup>
import PrivateShow from '@/components/PrivateShow/index.vue'
import { reactive,toRefs,onBeforeMount,onMounted,watch,ref, nextTick, onBeforeUnmount} from 'vue'
import {ArrowLeft, Plus} from '@element-plus/icons-vue'
import {useRouter,useRoute} from "vue-router";
import * as EquipmentApi from '@/api/operations/equipment'
import * as TicketsPushApi from '@/api/operations/ticketsPush'
import { t } from 'vue-i18n' // 国际化
// 修改引入路径为根目录
const router = useRouter();
const routers = useRoute();
let id= ref('')
const activeName = ref('');
const EquipmentNumbel = ref('');
const Hikvision = ref('');
const pictureUrl = ref('');
const rtspUrl = ref('ws://***********:559/openUrl/vsig12JSzhS45c307635883437e804c4');
const myPlugin = ref(null);
const curIndex = ref(0);
const dialogVisible = ref(false);
let Template =ref([]  )

// 视频播放相关状态
const videoError = ref('');
const isPlaying = ref(false);

//返回
const goback = () =>{
  router.go(-1)
}

let customFieldsRef = ref()


const formatMonthlyDate = (timestamp) => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 获得日志
const logList = ref([])
const getLog = async (id) => {
  const data = await EquipmentApi.getLog({ bizId:id , type: 'DEVICE_MANAGE_CENTER' })
  console.log('日志', data)
  logList.value = data.list
}

//获取图片
const getdeviceImg = async (id)=>{
  const res = await TicketsPushApi.getDetail({id})
  Hikvision.value=res.hikInfo?.cameraIndexCode
  EquipmentNumbel.value=res.deviceOverviewInfo.assetsCode
  pictureUrl.value=res.deviceOverviewInfo.pictureUrl
}

onBeforeMount(() => {

})

// 对话框打开后的处理
const handleDialogOpened = async () => {
  videoError.value = '';
  isPlaying.value = false;

  try {
    // 确保在对话框完全打开后再初始化视频播放器
    await nextTick();
   await RegisterVideo();
    await getVideoAddress();
  } catch (error) {
    ElMessage.error('视频播放失败:')
    console.error('视频初始化失败:', error);
    videoError.value = `视频初始化失败: ${error.message}`;
    isPlaying.value = false;
  }
};

//获取视频地址
const getVideoAddress= async ()=>{
  let res = await TicketsPushApi.getVideo({assetsCode:Hikvision.value,protocol:'ws'})
  const result = JSON.parse(res);
  const url = result.data?.url || ''; // 提供默认值
  if (url){
   await Preview(url);
  }else {
    ElMessage.warning('未获取到视频')
  }
}

const RegisterVideo = () => {
  curIndex.value = 0; // 当前窗口下标
  try {
    myPlugin.value = new JSPlugin({
      szId: 'Video-Preview',
      szBasePath: '/h5player/',      // 静态资源目录
      workerPath: '/h5player/',
      iMaxSplit: 4,
      iCurrentSplit: 1,
      oStyle: {
        border: "#343434",
        borderSelect: "#343434",
        background: "#000"
      }
    });
  } catch (error) {
    console.error('Failed to initialize JSPlugin:', error);
    throw error;
  }
};

const Preview = async (url) => {
  if (!myPlugin.value) {
    throw new Error('视频插件未初始化');
  }

  try {
    isPlaying.value = true;
    videoError.value = '';

    await myPlugin.value.JS_Play(
      url,
      {
        playURL: url,
        mode: 0,
        // 添加更多播放参数
        timeout: 10000, // 10秒超时
        reconnect: true, // 自动重连
        reconnectInterval: 5000, // 5秒重连间隔
      },
      curIndex.value
    );

    console.info('视频播放成功');
  } catch (err) {
    isPlaying.value = false;
    const errorMsg = getErrorMessage(err);
    console.error('视频播放失败:', errorMsg);
    ElMessage.error('视频播放失败:', errorMsg)
    videoError.value = `播放失败: ${errorMsg}`;
    throw err;
  }
};

// 获取友好的错误信息
const getErrorMessage = (error) => {
  if (typeof error === 'string') return error;
  if (error.code) {
    switch (error.code) {
      case '0x12f900008':
        return '视频流连接失败，请检查网络连接和视频源是否可用';
      case '0x12f900009':
        return '视频流格式不支持';
      case '0x12f900010':
        return '视频流解码失败';
      default:
        return `错误代码: ${error.code}`;
    }
  }
  return error.message || '未知错误';
};

const onVideo = () => {
  dialogVisible.value = true;
};

// 对话框关闭时的处理
const handleDialogClose = async () => {
  try {
    // 先停止视频播放
    if (myPlugin.value) {
      try {
        await myPlugin.value.JS_Stop(curIndex.value);
      } catch (e) {
        console.warn('停止视频播放时发生错误:', e);
      }
    }
    
    // 重置状态
    videoError.value = '';
    isPlaying.value = false;
    curIndex.value = 0;
    
    // 使用 nextTick 确保状态更新后再销毁播放器
    await nextTick();
    
    // 最后销毁播放器实例
    if (myPlugin.value) {
      try {
        await myPlugin.value.JS_Destroy();
      } catch (e) {
        console.warn('销毁播放器实例时发生错误:', e);
      } finally {
        myPlugin.value = null;
      }
    }
    
    // 最后再关闭对话框
    dialogVisible.value = false;
  } catch (error) {
    console.error('关闭视频预览时发生错误:', error);
    // 确保对话框最终会被关闭
    dialogVisible.value = false;
  }
};

// 修改关闭按钮的处理方式
const closeDialog = () => {
  handleDialogClose();
};

// 组件卸载时确保清理资源
onBeforeUnmount(() => {
  if (dialogVisible.value) {
    handleDialogClose();
  }
});

onMounted(() => {
  getLog(routers.query.id)
  getdeviceImg(routers.query.id)
})
</script>
<style lang="scss" scoped>
.box {
  display: flex;
  flex-direction: column;
  //height: 100vh;
  background: #f5f6f8;
  overflow: hidden;
}

.header {
  height: 48px;
  padding: 0 16px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);

  &-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .title {
    font-size: 16px;
    font-weight: 500;
  }
}

.content-wrapper {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  min-height: 0; // 重要：防止内容溢出
  overflow: hidden;
}

.content-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 0; // 重要：防止内容溢出
}

.content-right {
  width: 280px;
  min-width: 280px;
  height: 800px;
  background: #fff;
  border-radius: 4px;

  @media screen and (max-width: 1200px) {
    width: 280px;
  }
  .picUrl{
    width: 260px;
    margin-top: 15px;
    margin-left: 10px;
    height: 150px;
    border: 1px solid #e9e9e9;
  }
  .TextEditing{
    height: 600px;
  }
  .OperationLog{
    //width: 260px;
    padding: 15px;
    display: flex;
    .round{
      width: 10px;
      height: 10px;
      margin-top: 7px;
      border-radius: 50%;
      background: #0099ff;
    }

  }
}

.detail-card {
  background: #fff;
  border-radius: 4px;
  height: calc(80% - 8px);
  display: flex;
  flex-direction: column;
}

.card-header {
  padding: 16px;
  //border-bottom: 1px solid #eaedf2;
  display: flex;
  align-items: center;
  gap: 8px;
  span{
    font-weight: bold;
    font-size: 16px;
    color: #323336;
  }
  .header-bar {
    width: 3px;
    height: 16px;
    background: linear-gradient(180deg, #16B9FA 0%, #0058FF 100%);
    border-radius: 2px;
  }
}

.detail-content {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}

.detail-item {
  background: #f2f2f2;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.detail-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.detail-col {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: flex-start;

  .label {
    min-width: 90px;
    color: #666;
  }

  .value {
    flex: 1;
    color: #333;
    word-break: break-all;
  }
}

// 响应式布局
@media screen and (max-width: 1024px) {
  .content-wrapper {
    flex-direction: column;
  }

  .content-right {
    width: 100%;
    height: 300px;
  }

  .detail-col {
    min-width: 100%;
  }
}
/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.Video-Preview {
  background: #000;
  margin: 0 auto;
  position: relative;

  .video-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    color: #ff4d4f;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 4px;
    text-align: center;
  }

  .video-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 4px;
  }
}
</style>
