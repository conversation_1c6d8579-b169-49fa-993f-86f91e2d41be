<template>
  <div class="head-container">
    <el-input v-model="deptName" class="mb-20px" clearable placeholder="搜索类型">
      <template #prefix>
        <Icon icon="ep:search" />
      </template>
    </el-input>
  </div>
  <div class="head-container">
    <el-tree
      ref="treeRef"
      style="background: rgb(241 243 246);"
      :data="deptList"
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
      :props="defaultProps"
      default-expand-all
      highlight-current
      :current-node-key="defaultSelectedKey"
      node-key="id"
      @node-click="handleNodeClick"
    >
      <template #default="{ node, }">
        <span class="custom-tree-node">
          <Icon :icon="node.expanded ? 'ep:folder-opened' : 'ep:folder'" class="mr-4px" />
          <span>{{ node.label }}</span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script lang="ts" setup>
import { ElTree } from 'element-plus'
import * as DeptApi from '@/api/system/dept'
import { defaultProps, handleTree } from '@/utils/tree'
import {defineProps, nextTick} from "vue";
import {getDeviceType} from "@/api/EquipmentManagement/ManagementCenter/index";
const props=defineProps({
  // typeId: {
  //   type: [String, Number],
  //   required: true
  // },
  // Id: {
  //   type: [String, Number],
  //   required: true
  // },
  // activeTab: {
  //   type: [String, Number],
  //   required: true
  // } ,
})
defineOptions({ name: 'SystemUserDeptTree' })
// 添加默认选中的key
const defaultSelectedKey = ref('')
const deptName = ref('')
const deptList = ref<Tree[]>([]) // 树形结构
const treeRef = ref<InstanceType<typeof ElTree>>()

/** 获得部门树 */
const getTree = async (id?: Number ) => {
    const res = await getDeviceType()
    deptList.value = []
    deptList.value = res
}

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}

/** 处理部门被点击 */
const handleNodeClick = async (row: { [key: string]: any }) => {
  emits('node-click', row)
}
const emits = defineEmits(['node-click'])

/** 监听deptName */
watch(deptName, (val) => {
  treeRef.value!.filter(val)
})

// 清除高亮的方法
const clearHighlight = () => {
  treeRef.value?.setCurrentKey(null)  // 或者 setCurrentNode(null)
}

// 暴露方法给父组件
defineExpose({
  clearHighlight
})

/** 初始化 */
onMounted(async () => {
  await getTree()
})
</script>
<style scoped>
/* 选中节点的背景颜色改为白色 */
:deep(.custom-tree .el-tree-node.is-current > .el-tree-node__content) {
  background-color: white !important;
}
</style>
