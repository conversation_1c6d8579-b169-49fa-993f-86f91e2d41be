<template>
  <div class="box">
    <header>
      <el-button type="info" :icon="ArrowLeft" @click="goback()" plain>返回</el-button>
      <div class="itmdiv">设备列表-新增</div>
      <div class="rightbutton">
        <el-button type="primary" @click="onSave">保存</el-button>
        <el-button @click="onReset">重置</el-button>
      </div>
    </header>

    <div class="banner">
      <div class="editCenter">
        <DynamicForm ref="commonFormRef" :schema="commonFormShema" v-model="commonForm"/>
        <!-- 拓展信息表单（根据资产来源切换） -->
        <DynamicForm :schema="currentFormSchema" v-model="currentFormData" ref="extendFormRef"/>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Search, CirclePlus, Edit, Delete, ArrowLeft } from '@element-plus/icons-vue'
import { center } from '@/utils/mapmostUtils'
import { commonFormShema, commonFormData } from '@/components/DynamicForm/src/commonDictForm'
import { hikFormShema, hikData } from '@/components/DynamicForm/src/hikForm'
import { shElectricFormShema, shElectricData } from '@/components/DynamicForm/src/shElectricForm'
import { zabbixFormShema, zabbixData } from '@/components/DynamicForm/src/zabbixForm'
import { iotFormShema, iotData } from '@/components/DynamicForm/src/iotForm'
import {useRouter} from "vue-router";
const router = useRouter();
let dialogVisible = ref(false)

const goback = () => {
  router.go(-1)
}

// 通用表单
const commonFormRef = ref()
const commonForm = ref({ ...commonFormData })

// 设备来源（assetsSource）表单
const hikForm = ref({ ...hikData })
const iotForm = ref({ ...iotData })
const zabbixForm = ref({ ...zabbixData })
const shElectricForm = ref({ ...shElectricData })

// 对应表单 schema + data 映射
const formMap = {
  28: { schema: hikFormShema, data: hikForm },
  29: { schema: iotFormShema, data: iotForm },
  30: { schema: shElectricFormShema, data: shElectricForm },
  31: { schema: zabbixFormShema, data: zabbixForm }
}

// 当前选中的扩展表单 schema + data
const currentFormSchema = computed(() => formMap[commonForm.value.assetsSource]?.schema || [])
const currentFormData = computed(() => formMap[commonForm.value.assetsSource]?.data || {})
const extendFormRef = ref()

//重置
function onReset() {
  commonFormRef.value?.reset()
  extendFormRef.value?.reset()
}

// 保存按钮
const onSave = async () => {
  console.log('通用表单:', commonForm.value)
  console.log('资产来源拓展表单:', currentFormData.value)

  const results = await Promise.all([
    commonFormRef.value.validate(),
    extendFormRef.value.validate()
  ])
  const isAllValid = results.every((res) => res === true)

  console.log(isAllValid, '结果')
  if (isAllValid) {
    console.log('校验通过，准备提交')
  } else {
    console.log('校验未通过字段:', results)
    ElMessage.warning('请完整填写必填项')
  }
}
onMounted(() => {
 console.log('center',commonFormShema)
})


</script>
<style lang="scss" scoped>
.box {
  header {
    height: 48px;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 16px;
    margin-bottom: 16px;
    .itmdiv {
      margin-left: 10px;
    }
    .elbutten {
      width: 80px;
      height: 32px;
      background: #eaedf2;
      border-radius: 4px 4px 4px 4px;
      border: 0;
      font-size: 14px;
      color: #323336;
      padding-left: 0;
      img {
        width: 24px;
        height: 24px;
      }
      span {
        display: inline-block;
        margin-top: 2px;
      }
    }
    .rightbutton {
      margin-right: 10px;
      color: #ffffff;
    }
    p {
      font-weight: 400;
      font-size: 14px;
      margin-left: 24px;
      span {
        &:first-child {
          color: #9c9da2;
        }
      }
    }
  }
  .banner {
    width: 100%;
    display: flex;
    background: #ffffff;
    .editCenter {
      width: 100%;
      margin: 30px;
    }
  }
}
</style>
