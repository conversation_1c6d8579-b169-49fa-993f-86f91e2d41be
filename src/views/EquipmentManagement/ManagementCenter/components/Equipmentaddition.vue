<template>
<!-- eslint-disable vue/valid-v-slot -->
  <div class="my-component">
    <header>
      <el-button class="ml-5 mr-5" type="primary" plain @click="goBack">返回</el-button>
      <p>{{ pageTitle }}</p>
    </header>
    <main class="border-style">
      <DynamicForm ref="commonFormRef" :schema="commonFormShema" v-model="commonForm" />
      <DynamicForm v-model="commonForm" :schema="formSchema">
        <template #item.pictureUrl>
          <el-upload
          v-model:file-list="fileList"
          :action="uploadUrl"
          :headers="headers"
          list-type="picture-card"
          :multiple="false"
          :limit="1"
          :on-remove="handleRemove"
          :on-success="handleSuccess"
          :on-error="handleError"
          >
          <el-icon><Plus /></el-icon></el-upload>
        </template>
      </DynamicForm>
      <div  style="margin-bottom: 20px;border: 2px solid rgb(243 243 243)"> </div>
      <!-- 拓展信息表单（根据资产来源切换） -->
      <DynamicForm
        v-if="commonForm?.assetsSource && sourceMap[commonForm?.assetsSource] === 'hik'"
        :schema="hikFormShema"
        v-model="hikForm"
        ref="extendFormRef"
      />
      <DynamicForm
        v-else-if="commonForm?.assetsSource && sourceMap[commonForm?.assetsSource] === 'iot'"
        :schema="iotFormShema"
        v-model="iotForm"
        ref="extendFormRef"
      />
      <DynamicForm
        v-else-if="
          commonForm?.assetsSource && sourceMap[commonForm?.assetsSource] === 'ShanghaiElectric'
        "
        :schema="shElectricFormShema"
        v-model="shElectricForm"
        ref="extendFormRef"
      />
      <DynamicForm
        v-else-if="commonForm?.assetsSource && sourceMap[commonForm?.assetsSource] === 'zabbix'"
        :schema="zabbixFormShema"
        v-model="zabbixForm"
        ref="extendFormRef"
      />
      <div class="bottom-btn">
        <el-button type="primary" @click="onSave">保存</el-button>
        <el-button @click="onReset">重置</el-button>
      </div>
    </main>
  </div>
</template>

<script lang="ts" setup>
// ==========================
import { commonFormShema, commonFormData } from '@/components/DynamicForm/src/commonDictForm'
import { hikFormShema, hikData } from '@/components/DynamicForm/src/hikForm'
import { shElectricFormShema, shElectricData } from '@/components/DynamicForm/src/shElectricForm'
import { zabbixFormShema, zabbixData } from '@/components/DynamicForm/src/zabbixForm'
import { iotFormShema, iotData } from '@/components/DynamicForm/src/iotForm'
//===========================
import { Plus} from '@element-plus/icons-vue'
import { reactive, ref, onMounted, computed } from 'vue'
import * as EquipmentApi from '@/api/operations/equipment'
import { getDictList } from '@/api/infra/deviceDict'
import { useRoute } from 'vue-router'
import * as TicketsPushApi from '@/api/operations/ticketsPush'
import {getRefreshToken} from "@/utils/auth";
const uploadUrl=import.meta.env.VITE_BASE_URL + '/admin-api/infra/file/upload'
const headers = computed(() => ({
  Authorization: 'Bearer ' + getRefreshToken()
}))
const fileList = ref([])
const route = useRoute()
const isID = reactive({ id: '' }) //是否是编辑id
// 移除formData定义，使用commonForm.pictureUrl统一管理
onMounted(() => {
  // 从路由查询参数中获取 id 值
  isID.id = route.query.id?.toString() || ''
  if (isID.id) {
    getDetail()
    // 获取数据并回填
  }
})
const formSchema=[{
  label: '上传图片',
  prop: 'pictureUrl',
  type: 'custom',
  component: 'el-upload',
  listType: 'picture-card',
  multiple: false, // 设置为false表示单次只能上传一张
  limit: 1 // 限制最多上传1张图片
}]
const detailList = ref([])
const getDetail = async () => {
  const res = await TicketsPushApi.getDetail(isID)
  detailList.value = res
  // 回填通用表单数据
  commonForm.value = res.deviceOverviewInfo
  if (res.deviceOverviewInfo.pictureUrl) {
    handleInitialData(res.deviceOverviewInfo)
  }
  if (res.hikInfo != null) {
    hikForm.value = res.hikInfo
  } else if (res.iotInfo != null) {
    iotForm.value = res.iotInfo
  } else if (res.shElectricInfo != null) {
    shElectricForm.value = res.shElectricInfo
  } else if (res.zabbixInfo != null) {
    zabbixForm.value = res.zabbixInfo
  }
}

const sourceMap = ref({})
const getSource = async () => {
  const data = await getDictList('deviceSource')
  console.log(data, 'getSource')
  data.forEach((d) => {
    sourceMap.value[d.id] = d.label
  })
}
getSource()
// 标头文字
const pageTitle = computed(() => {
  return isID.id ? '运维设备列表-编辑' : '运维设备列表-新增'
})

// 上传成功
const handleSuccess = (response, uploadFile) => {
  if (response.code === 0) {
    ElMessage.success('上传成功')
    commonForm.value.pictureUrl=response.data

    // 处理返回的文件信息
  } else {
    ElMessage.error(response.msg || '上传失败')
  }
}

// 上传失败
const handleError = (error) => {
  ElMessage.error('上传失败：' + (error.msg || '未知错误'))
  commonForm.value.pictureUrl=''
  fileList.value = []
}

// 上传成功
const handleRemove = (uploadFile) => {
  commonForm.value.pictureUrl = ''
  fileList.value=[]

}
const handleInitialData = (data) => {
  if (data.pictureUrl) {
    fileList.value = [{
      name: '图片', // 可以自定义名称
      url: data.pictureUrl, // 后端返回的图片地址
      status: 'success', // 设置状态为成功
      uid: -1 // 需要一个唯一标识
    }]
  }
}

const goBack = () => {
  window.history.back()
}
// =======================
// 通用表单
const commonFormRef = ref()
const commonForm = ref({ ...commonFormData })
// 设备来源（assetsSource）表单
const hikForm = ref({ ...hikData })
const iotForm = ref({ ...iotData })
const zabbixForm = ref({ ...zabbixData })
const shElectricForm = ref({ ...shElectricData })
const extendFormRef = ref()

//重置
function onReset() {
  commonFormRef.value?.reset()
  extendFormRef.value?.reset()
}

// 保存按钮

const onSave = async () => {
  console.log('通用表单:', commonForm.value)
  console.log('其他表单:', hikForm.value, iotForm.value, zabbixForm.value, shElectricForm.value)

  const params = {
    deviceOverviewInfo: {...commonForm.value,id:isID.id},
    hikInfo: hikForm.value,
    iotInfo: iotForm.value,
    zabbixInfo: zabbixForm.value,
    shElectricInfo: shElectricForm.value
  }
  console.log('总表单:', hikForm.value, iotForm.value, zabbixForm.value, shElectricForm.value)
  const results = await Promise.all([commonFormRef.value.validate()])
  const isAllValid = results.every((res) => res === true)
  // console.log('打印results', results)
  // console.log(isAllValid, '是否通过')

  if (isAllValid) {
    if (isID.id) {
      try {
        await EquipmentApi.updateItem(params)
        ElMessage.success('编辑成功')
        goBack()
      } catch (error) {
        console.error('编辑失败，错误信息:', error)
        ElMessage.error('编辑失败')
      }
    } else {
      try {
        await EquipmentApi.addList(params)
        ElMessage.success('提交成功')
        goBack()
      } catch (error) {
      }
    }
  } else {
    console.log('校验未通过字段:', results)
    ElMessage.warning('请完整填写必填项')
  }
}
// =======================
</script>
<style lang="scss" scoped>
.border-style {
  border: 1px solid #ccc;
  border-radius: 1%;
}
.my-component {
  width: 100%;
  height: 100%;
  background-color: white;
  display: flex;
  flex-direction: column;
  header {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    margin-bottom: 10px;
  }
  main {
    width: 100%;
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    .bottom-btn {
      width: 100%;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      button {
        width: 40%;
      }
    }
  }
}
</style>



