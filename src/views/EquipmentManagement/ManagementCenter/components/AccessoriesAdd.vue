<template>
  <div class="box">
    <header>
      <el-button type="info" :icon="ArrowLeft" @click="goback()" plain>返回</el-button>
      <div class="itmdiv">{{`辅件列表-${routers.query.id? '编辑' : '新增'}`}}</div>
      <div class="rightbutten">
        <XButton title="保存" @click="validateForms"  gradient />
        <el-button @click="goback()" >取消</el-button>
      </div>
    </header>

    <div class="banner">
      <div class="editCenterLeft">
        <el-form
          ref="ruleFormRef"
          style="max-width: 600px"
          :model="ruleForm"
          :rules="rules"
          label-width="110px"
          class="demo-ruleForm"
          status-icon
        >
          <el-form-item label="辅件编号" prop="auxiliaryCode">
            <el-input v-model="ruleForm.auxiliaryCode" />
          </el-form-item>
          <el-form-item label="辅件名称" prop="auxiliaryName">
            <el-input v-model="ruleForm.auxiliaryName" />
          </el-form-item>
          <el-form-item label="所属区域" prop="areaId">
            <Dictionary
              v-model="ruleForm.areaId"
              type="cascader"
              dict-type="oamArea"
              :divide="false"
              :cascader-props="{
              checkStrictly: true,
              emitPath: true,
              label:'name',
              value: 'id',
             }"
              :max-collapse-tags="1"
              placeholder="请选择区域"
            />
          </el-form-item>
          <el-form-item label="辅件类型" prop="assetsTypeId">
            <Dictionary
              v-model="ruleForm.assetsTypeId"
              type="select"
              dict-type="accessoryType"
              placeholder="请选择辅件类型"
            />
          </el-form-item>
          <el-form-item label="经纬度"  required>
            <el-form-item prop="longitude" style="display: flex;align-items: center;margin-right: 10px;">
              <el-input
                v-model="ruleForm.longitude"
                placeholder="请输入经度"
                style="width: 100px;"
              />
            </el-form-item>
            <el-form-item prop="latitude" class="inline-item">
              <el-input
                v-model="ruleForm.latitude"
                placeholder="请输入纬度"
                style="width: 100px;"
              />
            </el-form-item>
            <div style="margin-left: 7px;"> <el-button :icon="Position" @click="openMap"  /></div>
          </el-form-item>

          <el-form-item label="图片" prop="address">
            <el-upload
              v-model:file-list="fileList"
              :action="uploadUrl"
              :headers="headers"
              list-type="picture-card"
              :on-remove="handleRemove"
              :on-success="handleSuccess"
              :on-error="handleError"
            >
              <el-icon><Plus /></el-icon>
            </el-upload>

          </el-form-item>

        </el-form>
      </div>
      <div class="editCenterRight">
        <div style="display: flex; justify-content: space-between; margin-top: -30px;">
          <div style="display: flex;align-items: center;">
            <div style="width: 13px;height: 14px;background: #0c66ff;border-radius: 30px;"></div>
            <div style="margin-left: 2px;">关联设备</div>
          </div>
          <div>
            <el-form-item style="margin-top: 15px;">
              <el-select  style="width: 75px;" v-model="devicesParams.regionMeter" placeholder="请选择"  clearable>
                <el-option label="5米" value="5" key="5" />
                <el-option label="10米" value="10" key="10" />
                <el-option label="15米" value="15" key="15" />
              </el-select>
            </el-form-item>
          </div>
        </div>
          <el-form
            ref="devicesFormRef"
            :inline="true"
            :model="devicesParams"
          >
            <el-form-item label="" prop="name" class="!mr-3">
              <el-input
                v-model="devicesParams.assetsName"
                class="!w-100px"
                clearable
                placeholder="输入设备编号/名称"
              />
            </el-form-item>
            <el-form-item label="类型" prop="status" class="!mr-3">
              <el-cascader :options="DeviceType" v-model="assetsTypeId" @change="handleChange"  :props="props1" style="width: 150px;" :max-collapse-tags="1"  collapse-tags collapse-tags-tooltip />
            </el-form-item>
            <el-form-item label="" prop="status" class="!mr-3">
              <XButton title="查询"  gradient @click="PophandleQuery" />
            </el-form-item>
            <el-form-item label="" prop="status" class="!mr-3">
              <el-button  @click="onReset" >重置</el-button>
            </el-form-item>
          </el-form>

        <el-table  row-key="id" class="devicesTable"  height="250" ref="tableRef"  @selection-change="selectionChange" :data="deviceslist">
          <el-table-column align="center" type="selection" :reserve-selection="true"  width="55" />
          <el-table-column align="center" label="设备编号" prop="assetsCode" />
          <el-table-column align="center" label="设备名称" prop="assetsName" />
          <el-table-column align="center" label="所属项目" prop="projectName" />
          <el-table-column align="center" label="设备类型" prop="assetsTypeName" />
        </el-table>
        <Pagination
          style="margin-top: 35px;"
          v-model:limit="devicesParams.pageSize"
          v-model:page="devicesParams.pageNo"
          :total="Total"
          @pagination="devicesGetList"
        />
      </div>

    </div>

    <Dialog v-model="dialogVisible" width="1000" title="经纬度选择">
      <MapPoints ref="mapPointRef" showFind v-model="choosedLngAndLat" />
      <div style="display: flex; align-items: center; justify-content: center; margin-top: 20px">
        <XButton title="确认" width="160px" gradient @click="handleConfirm" />
        <XButton title="重置" width="160px" @click="handleReset" />
      </div>
    </Dialog>
    <el-dialog v-model="dialogVisibleimg">
      <img w-full :src="dialogImageUrl" style="width: 100%;height: 500px;" alt="Preview Image" />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { reactive,onMounted,ref,onUnmounted} from 'vue'
import {ArrowLeft, Plus, Position} from '@element-plus/icons-vue'
import { getRefreshToken } from '@/utils/auth'
import {useRouter,useRoute} from "vue-router";
import {center} from "@/utils/mapmostUtils";
import {getDeviceType,getPropertyPage,AccessoriesAdd,AccDetails,AccessoriesUpdate} from "@/api/EquipmentManagement/ManagementCenter";
const data = reactive({})
const choosedLngAndLat = ref({ lng: center[0], lat: center[1] })
const uploadUrl=import.meta.env.VITE_BASE_URL + '/admin-api/infra/file/upload'
import emitter  from '@/utils/eventBus'
const headers = computed(() => ({
  Authorization: 'Bearer ' + getRefreshToken()
}))
let  rules=reactive({
  auxiliaryCode: [
    { required: true, message: '请输入辅件编号', trigger: 'blur' },
  ],
  auxiliaryName: [
    { required: true, message: '请输入辅件名称', trigger: 'blur' },
  ],
  areaId: [
    { required: true, message: '请选择所属区域', trigger: 'blur' },
  ],
  assetsTypeId: [
    { required: true, message: '请选择辅件类型', trigger: 'blur' },
  ],

  latitude: [
    { required: true, message: '请填写纬度', trigger: 'blur' },
  ],
  longitude: [
    { required: true, message: '请填写经度', trigger: 'blur' },
  ],
})
const DeviceType = ref([])  //设备类型
const assetsTypeId = ref([]) // 设备类型接收值
const fileList = ref([])
const props1 = {
  value: 'id',
  label: 'name',
  children: 'children',
  multiple: true
}
let ruleFormRef = ref()
let Total = ref(10)
let dialogVisible = ref(false)
let watchVisible = ref(true)
let TypeList = ref([])
let customFields = ref([])
let customFieldsList = ref([])
let selectionList = ref([])
let customFieldValues = ref({})
let ruleForm=ref({})
const router = useRouter();
const routers = useRoute();
interface Person {
  auxiliaryCode: string;
  auxiliaryName: string;
  areaId:string;
  assetsTypeId:string;
  longitude:string;
  latitude:string;
  picUrl?: string; // 图片 URL 可选
  deviceAssetsAuxiliarySaveReqVOList?: { id: any, deviceId: any }[]; // 可选，根据需求添加
}
const deviceslist = ref([]) // 列表的数据
const devicesParams = reactive({
  pageNo: 1,
  pageSize: 10,
  assetsName:'',
  regionMeter:'5',
  latitude:'',
  longitude:'',
  assetsTypeId:[]
})
// 所有选中项的 id 数组
const selectedIds = ref([])
const currentPageSelection = ref([]) // 列表选中的数据
/** 表格选中数据 */
const selectionChange = (selection) => {
  // 表格选择变化处理
  currentPageSelection.value = selection
  // 更新总选择数组
  const currentPageIds = selection.map(item => item.id)
  // 获取当前页所有数据的 id
  const currentPageAllIds = deviceslist.value.map(item => item.id)

  // 从总选择中移除当前页取消选择的数据
  selectedIds.value = selectedIds.value.filter(id =>
    !currentPageAllIds.includes(id) || currentPageIds.includes(id)
  )

  // 添加当前页新选择的数据
  currentPageIds.forEach(id => {
    if (!selectedIds.value.includes(id)) {
      selectedIds.value.push(id)
    }
  })
}
// 表格引用
const tableRef = ref()
// 切换页面时设置选中状态
const setTableSelection = () => {
  // 等待表格数据更新
  // 改用 Set 提高查找效率
  const selectedIdSet = new Set(selectedIds.value.map(String));

  setTimeout(() => {
    deviceslist.value.forEach(row => {
      const rowIdStr = String(row.id);
      if (selectedIdSet.has(rowIdStr)) {
        tableRef.value?.toggleRowSelection(row, true);
      }
    });
  }, 200);

}
//返回
const goback = () =>{
  router.push({
    path: 'PluginList',
  })
}

//保存按钮
 const onSave =async (value:Person) =>{
   try {
     if (routers.query.id){
       const val = await AccessoriesUpdate(value)
       router.push({path:'PluginList'})
     }else {
       const data = await AccessoriesAdd(value)
       router.push({path:'PluginList'})
     }
     // router.go(-1)
   }finally {

   }
}

onMounted(async () => {
  DeviceType.value = await getDeviceType()
  if (routers.query.id){
    const data = await AccDetails(routers.query.id)
    ruleForm.value.auxiliaryCode=data.auxiliaryCode
    ruleForm.value.auxiliaryName=data.auxiliaryName
    ruleForm.value.areaId=data.areaId
    ruleForm.value.assetsTypeId=data.assetsTypeId
    ruleForm.value.longitude=data.longitude
    ruleForm.value.latitude=data.latitude
    devicesParams.longitude=data.longitude
    devicesParams.latitude=data.latitude
    ruleForm.value.id=data.id
    handleInitialData(data)
    selectedIds.value=data.assetsIdList
    setTableSelection()
  }
  devicesGetList()
})


// 处理后端返回的数据，进行回显
const handleInitialData = (data) => {
  if (data.picUrl) {
    fileList.value = [{
      name: '图片', // 可以自定义名称
      url: data.picUrl, // 后端返回的图片地址
      status: 'success', // 设置状态为成功
      uid: -1 // 需要一个唯一标识
    }]
  }
}
const handleChange = (val) => {
  // 1. 展平数组
  const flatArray = val.flat()
  // 2. 去重
  const uniqueArray = [...new Set(flatArray)]
  // 3. 过滤掉无效值（如 null、undefined）
  const validArray = uniqueArray.filter(item => item != null)
  devicesParams.assetsTypeId=validArray

}

//打开经纬度弹窗
const openMap = () =>{
  dialogVisible.value = true
}
/** 设备列表 */
const devicesGetList = async () => {

  try {
    const data = await getPropertyPage(devicesParams)
    deviceslist.value = data.list
    Total.value = data.total
    setTableSelection()
  } finally {
  }
}


//关联设备搜索按钮
const PophandleQuery = () =>{
  devicesGetList()

}

const devicesFormRef=ref()
//关联设备重置按钮
const onReset = () =>{
  watchVisible.value=false
  devicesFormRef.value.resetFields()
  devicesParams.regionMeter='5'
  assetsTypeId.value=[]
  devicesParams.assetsTypeId=[]
  assetsTypeId.value=[]
  selectedIds.value=[]
  currentPageSelection.value=[]
  devicesGetList()
}

const dialogImageUrl = ref('')
const dialogVisibleimg = ref(false)

// 上传成功
const handleSuccess = (response, uploadFile) => {
  if (response.code === 0) {
    ElMessage.success('上传成功')
    ruleForm.value.picUrl=response.data

    // 处理返回的文件信息
  } else {
    ElMessage.error(response.msg || '上传失败')
  }
}
// 上传成功
const handleRemove = () => {

}

// 上传失败
const handleError = (error) => {
  ElMessage.error('上传失败：' + (error.msg || '未知错误'))
  ruleForm.value.picUrl=''
}



//表单验证
const validateForms = async () => {
  try {
    const [form1Valid, form2Valid] = await Promise.all([
      ruleFormRef.value.validate().catch(() => false),
      devicesFormRef.value.validate().catch(() => false)
    ])

    if (form1Valid && form2Valid) {
      let dd=[]
      if (selectedIds.value.length>0){
        selectedIds.value.forEach(itm=>{
          // dd.push({id:itm.id, deviceId:itm.assetsTypeId})
          dd.push({deviceId:itm})

        })
      }
      if(!routers.query.id){
        let areaId=ruleForm.value.areaId[ruleForm.value.areaId.length-1]
        let parms={...ruleForm.value,
          areaId,
          deviceAssetsAuxiliarySaveReqVOList:dd}
        onSave(parms)
        ElMessage.success('操作成功')
        emitter.emit('PlUGINT', '1')
        goback()
      }else {
        let parms={...ruleForm.value,
          deviceAssetsAuxiliarySaveReqVOList:dd}
        onSave(parms)
        ElMessage.success('操作成功')
        emitter.emit('PlUGINT', '1')
        goback()
      }

      // 执行提交逻辑
    } else {
      console.log('表单验证失败')
      console.log('dww1d1d11',customFieldValues.value)
    }
  } catch (error) {
    console.log('验证过程出错:', error)
  }

}
const handleConfirm = () => {
  ruleForm.value.latitude = choosedLngAndLat.value.lat
  ruleForm.value.longitude = choosedLngAndLat.value.lng
  devicesParams.latitude = choosedLngAndLat.value.lat
  devicesParams.longitude = choosedLngAndLat.value.lng
  dialogVisible.value=false
  devicesGetList()

}

const handleReset = () => {
  choosedLngAndLat.value = { lng: center[0], lat: center[1] }
}
// 监听路由变化，触发数据初始化
watchEffect(async () => {
    devicesGetList()
    DeviceType.value = await getDeviceType()
    if (routers.query.id){
      const data = await AccDetails(routers.query.id)
      ruleForm.value.auxiliaryCode=data.auxiliaryCode
      ruleForm.value.auxiliaryName=data.auxiliaryName
      ruleForm.value.areaId=data.areaId
      ruleForm.value.assetsTypeId=data.assetsTypeId
      ruleForm.value.longitude=data.longitude
      ruleForm.value.latitude=data.latitude
      devicesParams.longitude=data.longitude
      devicesParams.latitude=data.latitude
      ruleForm.value.id=data.id
      handleInitialData(data)
      selectedIds.value=data.assetsIdList
      setTableSelection()
  }

})

// 组件卸载时重置数据
onUnmounted(() => {
  // 重置表单数据
  ruleForm.value = {}
  // 重置其他状态
  fileList.value = []
  selectedIds.value = []
  currentPageSelection.value = []
  assetsTypeId.value = []
  devicesParams.assetsName = ''
  devicesParams.assetsTypeId = []
  devicesParams.status = ''
})

</script>
<style lang="scss" scoped>

.box{
  //background: #F5F6F8;
  //position: relative;
  //height: 75vh;     // 设置容器高度为视口高度
  header{
    // width: 1616px;
    //position: sticky;
    //top: 10px;
    //left: 10px;
    //right: 0;
    //z-index: 100;
    height: 48px;
    background: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 16px;
    margin-bottom: 16px;
    .itmdiv{
      margin-left: 10px;
    }
    .elbutten{
      width: 80px;
      height: 32px;
      background: #EAEDF2;
      border-radius: 4px 4px 4px 4px;
      border: 0;
      font-size: 14px;
      color: #323336;
      padding-left: 0;
      img{
        width: 24px;
        height: 24px;
      }
      span{
        display: inline-block;
        margin-top: 2px;
      }
    }
    .rightbutten{
      margin-right: 10px;
    }
    p{
      font-weight: 400;
      font-size: 14px;
      margin-left: 24px;
      span{
        &:first-child{
          color: #9C9DA2;
        }
      }
    }
  }
  .banner{
    width: 100%;
    display: flex;
    background: #FFFFFF;
    .editCenterLeft{
      //flex: 1;
      //width: 100%;
      width: 50%;
      padding-right: 60px;
      //margin: 30px;
      padding-top: 30px;
      border: 1px solid #EAEDF2;
    }
    .editCenterRight{
      //flex: 1;
      width: 50%;
      //margin: 30px;
      padding: 30px;
      border: 1px solid #EAEDF2;
    }
  }
}
</style>
