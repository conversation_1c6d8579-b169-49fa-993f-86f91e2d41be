<template>
  <Dialog v-model="dialogVisible" width="1000" title="选择模板">
    <el-tabs tab-position="left"  v-model="activeName"  style="height: 500px" class="demo-tabs">
      <el-tab-pane
        v-for="itm in Template"
        :key="itm.id"
        :label="itm.modelName"
        :name="itm.id"
      >
<!--        <div v-if="itm.modelUrl" class="imgContainer"><img :src="itm.modelUrl" alt=""></div>-->
<!--        <div v-else>暂未获取到图片</div>-->
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <el-button  style="width: 200px;" type="primary" >确定</el-button>
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { CommonStatusEnum } from '@/utils/constants'
import { defaultProps, handleTree } from '@/utils/tree'
import * as PostApi from '@/api/system/post'
import * as DeptApi from '@/api/system/dept'
import * as UserApi from '@/api/system/user'
import { FormRules } from 'element-plus'
import {getRoleList} from "@/api/system/post";

defineOptions({ name: 'ConfigureQRcode' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const activeName = ref('');
let Template =ref([]  )
const dialogVisible = ref(false) // 弹窗的是否展示
const formData = ref({
  nickname: '',
  deptId: '',
  mobile: '',
  email: '',
  id: undefined,
  username: '',
  password: '',
  sex: undefined,
  postIds: [],
  remark: '',
  status: CommonStatusEnum.ENABLE,
  roleIds: []
})

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  // // 加载角色列表
  dialogVisible.value = true
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// /** 提交表单 */
// const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
// const submitForm = async () => {
//   // 校验表单
//   if (!formRef) return
//   const valid = await formRef.value.validate()
//   if (!valid) return
//   // 提交请求
//   formLoading.value = true
//   try {
//     const data = formData.value as unknown as UserApi.UserVO
//     if (formType.value === 'create') {
//       await UserApi.createUser(data)
//       message.success(t('common.createSuccess'))
//     } else {
//       await UserApi.updateUser(data)
//       message.success(t('common.updateSuccess'))
//     }
//     dialogVisible.value = false
//     // 发送操作成功的事件
//     emit('success',formType.value)
//   } finally {
//     formLoading.value = false
//   }
// }
const queryParams = reactive({
  pageNo: 1,
  pageSize: 100,
  name: undefined,
  status: undefined
})
let unitList = ref<Tree[]>([])
onMounted(async () => {
  // 加载性别字典
  // const data = await DeptApi.getDeptPage(queryParams)
  // unitList.value = handleTree(data)
  // postList.value = await PostApi.getRoleList()
})


</script>
