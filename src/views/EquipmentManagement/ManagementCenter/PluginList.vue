<template>
  <ContentWrap class="h-1/1">
    <span class="TitleStyle">辅件列表</span>
  </ContentWrap>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="flex flex-wrap items-start -mb-15px"
    >
      <el-form-item label="" prop="auxiliaryName" class="!mr-3">
        <el-input
          v-model="queryParams.auxiliaryName"
          class="!w-240px"
          clearable
          placeholder="输入辅件编号/名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="区域" prop="areaId" class="!mr-3">
        <Dictionary
          v-model="queryParams.areaId"
          type="cascader"
          width="240px"
          dict-type="oamArea"
          :cascader-props="{
              multiple: true,
              checkStrictly: true,
              label:'name',
              value: 'id',
             }"
          :max-collapse-tags="1"
          placeholder="请选择区域"
        />
      </el-form-item>
      <el-form-item label="辅件类型" prop="assetsTypeId" class="!mr-3">
        <Dictionary
          v-model="queryParams.assetsTypeId"
          :multiple="true"
          :max-collapse-tags="2"
          type="select"
          width="150px"
          dict-type="accessoryType"
          placeholder="请选择辅件类型"
        />
      </el-form-item>
      <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
        <div>
          <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:device-auxiliary-info:query']" gradient @click="handleQuery" />
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />
            重置
          </el-button>
        </div>
        <div>
          <el-button
            v-hasPermi="['infra:device-auxiliary-info:create']"
            plain
            @click="openAdd()"
          >
            <Icon icon="ep:plus" />
            新增
          </el-button>
          <el-button
            v-hasPermi="['infra:device-auxiliary-info:import']"
            plain
            @click="handleImport"
          >
            <Icon icon="ep:upload" />
            导入
          </el-button>
          <el-button
            v-hasPermi="['infra:device-auxiliary-info:export']"
            plain
            @click="handleExport"
            :loading="exportLoading"
          >
            <Icon icon="ep:download" />
            导出
          </el-button>
        </div>
      </div>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      ref="myTable"
      v-loading="loading"
      @selection-change="selectionChange"
      :data="list"
      :header-cell-style="{
             'background-color': '#F4F6F8',
            'font-size': '14px',
            'color': '#3B3C3D',}"
    >
      <el-table-column align="center" type="selection"   width="55" />
      <el-table-column type="index" label="序号" width="55" align="center">
        <template #default="scope">
          <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="辅件编号" prop="auxiliaryCode" />
      <el-table-column align="center" label="辅件名称" prop="auxiliaryName" />
      <el-table-column align="center" label="所属区域" prop="areaName" />
      <el-table-column align="center" label="辅件类型" prop="assetsTypeName" />
      <el-table-column align="center" label="经纬度" prop="longitude">
        <template #default="scope">
          <span>{{scope.row.longitude+ ','+scope.row.latitude}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="关联设备数量" prop="assetsCount" />

      <el-table-column :width="230" align="center" label="操作">
        <template #default="scope">
<!--          v-hasPermi="['system:role:update']"-->
          <el-button
            link
            type="primary"
            @click="openDevices(scope.row.id)"
          >
            关联设备
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openDetails(scope.row.id)"
          >
            详情
          </el-button>

          <el-button
            @click="AccEdit(scope.row.id)"
            link
            v-hasPermi="['infra:device-auxiliary-info:update']"
            type="primary"
          >
           编辑
          </el-button>
          <el-button
            link
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['infra:device-auxiliary-info:delete']"
            type="primary"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <!-- 关联设备弹窗 -->
  <Dialog v-model="dialogVisible" width="1000" title="关联设备">
    <div style="display: flex;justify-content: space-between;">
      <el-form
      ref="devicesFormRef"
      :inline="true"
      :model="devicesParams"
      label-width="48px"
    >
      <el-form-item label="" prop="assetsName">
        <el-input
          v-model="devicesParams.assetsName"
          class="!w-140px"
          clearable
          placeholder="输入设备编号/名称"
        />
      </el-form-item>
      <el-form-item label="类型" prop="status">
        <el-cascader :options="DeviceType" v-model="assetsTypeId" @change="handleChange" :props="props1" style="width: 100%;" :max-collapse-tags="1"  collapse-tags collapse-tags-tooltip />
      </el-form-item>
      <el-form-item label="范围" prop="status">
        <el-select v-model="devicesParams.status" class="!w-140px" clearable placeholder="请选择">
          <el-option label="5米" value="5"/>
          <el-option label="10米" value="10"/>
          <el-option label="15米" value="15"/>
        </el-select>
      </el-form-item>

    </el-form>
      <div class="mb-4">
        <XButton title="查询"  gradient @click="PophandleQuery" />
        <el-button  @click="onReset" >重置</el-button>
      </div>
    </div>
    <el-table row-key="id"  class="devicesTable" height="350" ref="tableRef" v-loading="devicesLoading" @selection-change="selectionChange" :data="deviceslist">
      <el-table-column align="center" type="selection"   :reserve-selection="true" width="55" />
      <el-table-column align="center" label="设备编号" prop="assetsCode" />
      <el-table-column align="center" label="设备名称" prop="assetsName" />
      <el-table-column align="center" label="所属项目" prop="projectName" />
      <el-table-column align="center" label="设备类型" prop="assetsTypeName" />
    </el-table>
    <!-- 分页 -->
    <Pagination
      style="margin-top: 35px;"
      v-model:limit="devicesParams.pageSize"
      v-model:page="devicesParams.pageNo"
      :total="devicesParamsTotal"
      @pagination="devicesGetList"
    />
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center; margin-top: 60px;">
        <XButton title="确定"  width="200px" gradient @click="onRelated" />
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>

  <ImportComponents ref="importFormRef" path="/infra/device-auxiliary-info/import" fileName="辅件导入模板" title="辅件导入" :downloadPath="DownloadPath" @success="getList" />

</template>
<script lang="ts" setup>
import download from '@/utils/download'
import {
  getPropertyPage,
  getDeviceType,
  getAccessoriesPage,
  deleteAccessories, AccDetails, AccessoriesUpdate, AccExportexcel
} from "@/api/EquipmentManagement/ManagementCenter/index";
import { useRouter } from "vue-router";
import {ref} from "vue";
defineOptions({ name: 'PluginList' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
import emitter from '@/utils/eventBus'
const router = useRouter() // vue-router
const DownloadPath='/infra/device-auxiliary-info/get-import-template'
const DeviceType = ref([])  //设备类型
const loading = ref(true) // 列表的加载中
const devicesLoading = ref(false) // 列表的加载中
const dialogVisible = ref(false) //  关联设备弹窗的显示隐藏
const assetsTypeId = ref([]) // 设备类型接收值
const total = ref(10) // 列表的总页数
const devicesParamsTotal = ref(10) // 列表的总页数
const list = ref([]) // 列表的数据
const deviceList = ref({}) // 列表的数据
const deviceslist = ref([]) // 列表的数据
const props1 = {
  value: 'id',
  label: 'name',
  children: 'children',
  multiple: true
}
// 所有选中项的 id 数组
const selectedIds = ref([])
const currentPageSelection = ref([]) // 列表选中的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  auxiliaryCode:'',
  areaId:[],
  assetsTypeId:[]

})
const devicesParams = reactive({
  pageNo: 1,
  pageSize: 10,
  assetsName:'',
  assetsTypeId:[]

})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

const devicesFormRef=ref()

//关联设备重置按钮
const onReset = () =>{
  devicesFormRef.value.resetFields()
  assetsTypeId.value=[]
  devicesParams.assetsTypeId=[]
  devicesGetList()
}

//关联设备搜索按钮
const PophandleQuery = () =>{
  devicesGetList()

}


const handleChange = (val) => {
  // 1. 展平数组
  const flatArray = val.flat()
  // 2. 去重
  const uniqueArray = [...new Set(flatArray)]
  // 3. 过滤掉无效值（如 null、undefined）
  const validArray = uniqueArray.filter(item => item != null)
  devicesParams.assetsTypeId=validArray


}
/** 导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}
emitter.on('PlUGINT', (payload) => {
  // 处理事件
  setTimeout(()=>{
    getList()
  },100)
})
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
/** 新增按钮操作 */
const openAdd = () => {
  router.push({
    path:'AccessoriesAdd',
    query: {
      t: new Date().getTime() // 添加时间戳参数
    }
  })
}
/** 编辑按钮操作 */
const AccEdit = (value:number) => {
  router.push({
    path: 'AccessoriesAdd',
    query: { id: value }
  })
}
/** 详情按钮操作 */
const openDetails = (value:number) => {
  console.log('value',value)

  router.push(
    {
      path: 'AccessoriesDetails',
      query: { id: value }
  })
}
/** 关联设备按钮 */
const openDevices = async (value:number) => {
  const data = await AccDetails(value)
  try {
    deviceList.value.auxiliaryCode=data.auxiliaryCode
    deviceList.value.auxiliaryName=data.auxiliaryName
    deviceList.value.areaId=data.areaId
    deviceList.value.assetsTypeId=data.assetsTypeId
    deviceList.value.longitude=data.longitude
    deviceList.value.latitude=data.latitude
    deviceList.value.id=data.id
    dialogVisible.value = true
    selectedIds.value=data.assetsIdList
    setTableSelection()
  }finally {

  }
  console.log('value',value)

}

/** 关联设备确定按钮 */
const onRelated=async ()=>{
  let dd=[]
  selectedIds.value.forEach(itm=>{
    // dd.push({id:itm.id, deviceId:itm.assetsTypeId})
    dd.push({deviceId:itm})

  })
  let parms={...deviceList.value,
    deviceAssetsAuxiliarySaveReqVOList:dd}
  const data = await AccessoriesUpdate(parms)
  try {
    message.success('关联成功')
    getList()
    dialogVisible.value = false
  }finally {

  }
  console.log('related devices',selectedIds.value)
}

/** 表格选中数据 */
const selectionChange = (selection) => {
  // 表格选择变化处理
    currentPageSelection.value = selection
    // 更新总选择数组
    const currentPageIds = selection.map(item => item.id)
    // 获取当前页所有数据的 id
    const currentPageAllIds = deviceslist.value.map(item => item.id)

    // 从总选择中移除当前页取消选择的数据
    selectedIds.value = selectedIds.value.filter(id =>
      !currentPageAllIds.includes(id) || currentPageIds.includes(id)
    )

    // 添加当前页新选择的数据
    currentPageIds.forEach(id => {
      if (!selectedIds.value.includes(id)) {
        selectedIds.value.push(id)
      }
    })
}
// 表格引用
const tableRef = ref()
// 切换页面时设置选中状态
const setTableSelection = () => {
  // 等待表格数据更新
  // 改用 Set 提高查找效率
  const selectedIdSet = new Set(selectedIds.value.map(String));

  setTimeout(() => {
    deviceslist.value.forEach(row => {
      const rowIdStr = String(row.id);
      if (selectedIdSet.has(rowIdStr)) {
        tableRef.value?.toggleRowSelection(row, true);
      }
    });
  }, 200);
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await deleteAccessories(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await getAccessoriesPage(queryParams)
    list.value = data.list
    total.value = data.total
    loading.value = false
  } finally {
    loading.value = false
  }
}

/** 设备列表 */
const devicesGetList = async () => {

  try {
    const data = await getPropertyPage(devicesParams)
    deviceslist.value = data.list
    devicesParamsTotal.value = data.total
    // devicesLoading.value = true
    setTableSelection()
  } finally {
  }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await AccExportexcel(queryParams)
    download.excel(data, '辅件列表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  getList()
  devicesGetList()
  // 加载设备类型数据
  DeviceType.value = await getDeviceType()
})
</script>
<style scoped>

</style>
