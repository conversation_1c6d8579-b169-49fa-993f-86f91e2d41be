<template>

  <ContentWrap class="h-1/1">
  <span class="TitleStyle">设备列表</span>
  </ContentWrap>
  <el-row :gutter="20">
    <!-- 左侧部门树 -->
    <el-col :span="4" :xs="24">
      <ContentWrap    class="h-1/1" style="background: rgb(241 243 246)">
        <DeptTree ref="treeRef" @node-click="handleDeptNodeClick" />
      </ContentWrap>
    </el-col>
    <el-col :span="20" :xs="24">

      <!-- 搜索 -->
      <ContentWrap>
        <el-form
          class="flex flex-wrap items-start -mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
        >
          <el-form-item label="" prop="assetsName" class="!mr-3">
            <el-input
              v-model="queryParams.assets"
              placeholder="输入设备编号/名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-140px"
            />
          </el-form-item>
          <el-form-item label="区域" prop="mobile" class="!mr-3">
            <Dictionary
              v-model="queryParams.areaId"
              type="cascader"
              width="160px"
              dict-type="oamArea"
              :cascader-props="{
                multiple: true,
                checkStrictly: true,
                label: 'name',
                value: 'id'
              }"
              :max-collapse-tags="1"
              placeholder="请选择区域"
            />
          </el-form-item>
          <el-form-item label="质保状态" prop="mobile" class="!mr-3">
            <el-select class="!w-100px" v-model="queryParams.warrantyStatus" placeholder="请选择">
              <el-option
                v-for="item in postList"
                :key="item.id"
                :label="item.name"
                :value="item.id!"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="是否重点" prop="status" class="!mr-3">
            <el-select v-model="queryParams.isKey" placeholder="请选择" clearable class="!w-100px">
              <el-option
                v-for="dict in statusList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
            <div>
              <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:device-overview-info:query']" gradient @click="handleQuery" />
              <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
            </div>
            <div>
              <el-button @click="openForm"><Icon icon="ep:search" />高级搜索</el-button>
              <el-button plain v-hasPermi="['infra:device-overview-info:create']" @click="addition('create')">
                <Icon icon="ep:plus" />
                新增
              </el-button>
              <el-button @click="handleImport" v-hasPermi="['infra:device-overview-info:import']" plain> <Icon icon="ep:upload" /> 导入 </el-button>
              <el-button plain v-hasPermi="['infra:device-overview-info:export']" @click="handleExport" :loading="exportLoading">
                <Icon icon="ep:download" />导出
              </el-button>
              <el-button plain @click="Configure('create')">
                配置二维码
              </el-button>
              <el-button plain @click="Configure('create')">
                发布工单
              </el-button>
              <el-button plain> <Icon icon="ep:download" />导出二维码 </el-button>
            </div>
          </div>
        </el-form>
      </ContentWrap>

      <ContentWrap>
        <el-table
          ref="tableRef"
          :data="list"
          @selection-change="checkList"
          v-loading="loading"
          :header-cell-style="{
            'background-color': '#F4F6F8',
            'font-size': '14px',
            color: '#3B3C3D'
          }"
        >
          <el-table-column label="序号" type="selection" align="left" width="55" />

          <el-table-column type="index" label="序号" width="55" align="left">
            <template #default="scope">
              <span>{{ (queryParams.pageNo - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <template v-for="dict in columnList" :key="dict.key">
            <el-table-column
              v-if="dict.key == 'operate'"
              :label="dict.label"
              align="left"
              width="160"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
                <el-button
                  type="primary"
                  style="width: 30px"
                  @click="openDetails(scope.row.id)"
                  text
                  >详情</el-button
                >
                <el-button type="primary" v-hasPermi="['infra:device-overview-info:update']" style="width: 30px" @click="DeviceEdit(scope.row.id)" text
                  >编辑</el-button
                >
                <el-button
                  type="primary"
                  style="width: 30px"
                  v-hasPermi="['infra:device-overview-info:delete']"
                  @click="handleDelete(scope.row.id)"
                  text
                  >删除</el-button
                >
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="dict.key == 'warrantyStatus'"
              :label="dict.label"
              align="left"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
             <span>{{scope.row.warrantyStatus=='0' ? '质保中' : '已过保'}}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="dict.key == 'isKey'"
              :label="dict.label"
              align="left"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
             <span>{{scope.row.isKey=='0' ? '非重点' : '重点'}}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="dict.key == 'assetsCode'"
              :label="dict.label"
              align="left"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
            <div style="display: flex;align-items: center;"><img v-if="scope.row.isKey!='0'" src="@/assets/imgs/icons/keynote.png" style="width: 20px;height: 20px;" alt=""/>{{scope.row.assetsCode}} </div>
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="dict.key == 'label'"
              :label="dict.label"
              align="left"
              :show-overflow-tooltip="true"
              width="100"
            >
              <template #default="scope">
                <div class="flex gap-2 staffTag"  v-if="scope.row.labels && scope.row.labels.length > 0 ">
                <el-tag :style="{background: itm.colourCode}" v-for="itm in  scope.row.labels" :key="itm.labelId" >{{itm.label}}</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-else
              :label="dict.label"
              align="left"
              :prop="dict.key"
              :show-overflow-tooltip="true"
            />
          </template>
        </el-table>

        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>
    <!--    导入-->
    <!--    <deviceImportForm ref="importFormRef" @success="getList" />-->
    <ImportComponents
      ref="importFormRef"
      path="infra/device-overview-info/import/template"
      fileName="设备导入模板"
      title="设备导入"
      :projectid="projectId"
      :downloadPath="DownloadPath"
      @success="getList"
    />
    <!--    高级搜索-->
    <AdvancedSearch ref="formRef" @success="advanced" />
    <!--    配置二维码-->
    <ConfigureQRcode ref="QRcodeRef" />
  </el-row>
</template>

<script lang="ts" setup>
import download from '@/utils/download'
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import DeptTree from './components/DeptTree.vue'
import {
  getDicByDictType,
  getPropertyPage,
  deleteProperty,
  deviceExportexcel
} from '@/api/EquipmentManagement/ManagementCenter/index'
import { getAccessoryTypePage} from "@/api/EquipmentManagement/DeviceConfig";
import AdvancedSearch from './components/AdvancedSearch.vue'
import ConfigureQRcode from './components/ConfigureQRcode.vue'

const DownloadPath = '/infra/device-overview-info/get/template'
const route = useRouter()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
let options = ref([])
let projectId = ref('')
let postList = ref([
  { name: '质保中', id: '0' },
  { name: '已过保', id: '1' }
]) //质保状态
const props = { multiple: true, label: 'name' }
let selectionList = ref([])
export interface TenantExportReqVO {
  assetsCode?: string
  assetsName?: string
  assetsTypeId?: []
  areaId?: []
  supplierId?: string
  modelId?: string
  warrantyStatus?: string
  assetsSource?: string
  isKey?: string
  labelId?: []
}
let columnList = ref([
  // { label: '设备编号', key: 'assetsCode' },
  // { label: '设备名称', key: 'assetsName' },
  // { label: '设备类型', key: 'assetsTypeName' },
  // { label: '所属区域', key: 'area' },
  // { label: '供应商', key: 'supplierName' },
  // { label: '规格型号', key: 'modelName' },
  // { label: '质保状态', key: 'warrantyStatus' },
  // { label: '质保日期', key: 'warrantyDate' },
  // { label: '设备来源', key: 'assetsSourceName' },
  // { label: '标签', key: 'label' },
  // { label: '二维码', key: 'qrCode' },
  // { label: '操作', key: 'operate' }
]) // 列的集合
let statusList = ref([
  {
    label: '非重点',
    value: '0'
  },
  {
    label: '重点',
    value: '1'
  }
])
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  areaId: undefined,
  username: undefined,
  warrantyStatus: undefined,
  isKey: undefined
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await getPropertyPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

const getTableCatalogue = async () =>{
  let res =await  getAccessoryTypePage({ pageNo: 1, pageSize: 100,dictType: 'deviceTab',})
  if (res.list.length > 0) {
    res.list.forEach((item) => {
      if(item.status == '0'){
        columnList.value.push({
          label:item.value,
          key:item.label,
        })
      }
    })
  }else {

  }
}


let tableRef = ref()

/** 表格选中数据 */
const checkList = (value: any) => {
  selectionList.value = value
}

const treeRef = ref()
/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    areaId: undefined,
    username: undefined,
    warrantyStatus: undefined,
    isKey: undefined
  }
  handleQuery()
  treeRef.value?.clearHighlight()
}

/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  // if (projectId.value){
    importFormRef.value.open()
  // }else {
  //   ElMessage.warning('请先选择设备类型')
  // }

}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    const ids=selectionList.value.map(item => item.id)
    // if (selectionList.value.length > 0)
    // {
    //   ids=selectionList.value.map(item => item.id)
    // }
    // // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await deviceExportexcel({...queryParams.value,ids})
    download.excel(data, '设备数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await deleteProperty(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 */
onMounted(async () => {
  await getTableCatalogue()
  await getList()
})

// 获取区域信息
const getRegion = async () => {
  let data = await getDicByDictType('oamArea')
  options.value = data
}

/** 左侧数点击 */
const handleDeptNodeClick = (value) => {
  queryParams.value.assetsTypeId = [value.id]
  projectId.value = value.id
  getList()
}

/** 高级搜索弹窗 */
const formRef = ref()
const openForm = () => {
  formRef.value.open()
}

/** 配置二维码*/
const QRcodeRef = ref()
const Configure = () => {
  QRcodeRef.value.open()
}

//设备新增
const addition = () => {
  route.push('Equipmentaddition')
}

//设备编辑
const DeviceEdit = (id: number) => {
  route.push({
    path: 'Equipmentaddition',
    query: { id }
  })
}

//设备详情
const openDetails = (id: number) => {
  route.push({
    path: 'DeviceDetails',
    query: { id }
  })
}
//高级搜索按钮
const advanced = (value: TenantExportReqVO) => {
  queryParams.value = { ...queryParams.value, ...value }
  getList()
}
</script>
<style scoped >
.staffTag {
  display: flex;
  align-items: center;
  margin-top: 3px;
  width: 100px;
  height: 50px;
  overflow: auto;
}
/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 3px;
}

/* 鼠标悬停时滚动条的样式 */
::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

</style>
