
<template>
  <ContentWrap class="h-1/1">
    <span class="TitleStyle">设备标签配置</span>
  </ContentWrap>
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="flex flex-wrap items-start -mb-15px"
    >
      <el-form-item label="" prop="value" class="!mr-3">
        <el-input
          v-model="queryParams.value"
          class="!w-240px"
          clearable
          placeholder="输入标签名称"
        />
      </el-form-item>
      <el-form-item label="标签类型" prop="groupType" class="!mr-3">
        <Dictionary
          style="min-width: 150px"
          v-model="queryParams.groupType"
          type="select"
          dict-type="deviceLabelType"
          placeholder="请选择标签类型"
        />
<!--        <el-select v-model="queryParams.groupType" placeholder="请选择" style="min-width: 180px" >-->
<!--          <el-option label="应用类" value="0"/>-->
<!--          <el-option label="维护类" value="1"/>-->
<!--        </el-select>-->
      </el-form-item>
      <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
        <div>
          <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:device-dict:query']" gradient @click="handleQuery" />
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />
            重置
          </el-button>
        </div>
        <div>
          <el-button
            plain
            @click="TypeAdd"
            v-hasPermi="['infra:device-dict:create']"
          >
            <Icon icon="ep:plus" />
            新增
          </el-button>
          <el-button
            plain
            @click="handleImport"
            v-hasPermi="['infra:device-dict:import']"
          >
            <Icon icon="ep:upload" />
            导入
          </el-button>
          <el-button
            plain
            @click="handleExport" :loading="exportLoading"
            v-hasPermi="['infra:device-dict:export']"
          >
            <Icon icon="ep:download" />
            导出
          </el-button>
        </div>
      </div>
    </el-form>
  </ContentWrap>
  <!-- 列表 -->
  <ContentWrap>
    <el-table
      ref="myTable"
      v-loading="loading"
      @selection-change="selectionChange"
      :data="list">
      <el-table-column align="center" label="标签名称" prop="value" />
      <el-table-column align="center" label="标签类型" prop="groupName" />
<!--        <template #default="scope">-->
<!--          {{scope.row.groupType=='0'? '应用类' : '维护类' }}-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column :width="200" align="center" label="操作">
        <template #default="scope">
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openEdit(scope.row)"
            v-hasPermi="['infra:device-dict:update']"

          >
            编辑
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="TypeDelete(scope.row.id)"
            v-hasPermi="['infra:device-dict:delete']"

          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <Dialog v-model="dialogVisible" width="800"  :title="title">
    <el-form :model="form" label-width="auto" style="margin-bottom: 60px;" >
      <el-form-item label="标签类型" required >
        <div style="display: flex;align-items: center;">
          <Dictionary
            style="min-width: 580px"
            v-model="form.groupType"
            type="select"
            dict-type="deviceLabelType"
            placeholder="请选择标签类型"
          />
          <el-button style="margin-left: 13px;"  @click="onTypeManagement" link type="primary">类型管理</el-button>
        </div>

      </el-form-item>
      <el-form-item label="标签名称">
        <el-input  v-model="form.value" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <XButton title="确定"  width="200px"  gradient  @click="DictionaryAdd" />
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>
  <ImportDictionary ref="importFormRef" dictType="deviceLabel" @success="getList"   />
</template>
<script lang="ts" setup>/** 初始化 **/
import {useRouter} from "vue-router";
import * as DeptApi from "@/api/system/dept";
import {
  getAccessoryTypePage,
  getDicByDictType,
  AccessoryTypeAdd,
  AccessoryTypeUpdate,
  RandomCode
} from "@/api/EquipmentManagement/DeviceConfig";
import download from '@/utils/download'
import {DictionaryExport} from "@/api/EquipmentManagement/SiteAdministration";
import ImportDictionary from "@/views/EquipmentManagement/DeviceConfig/components/ImportDictionary.vue";
const message = useMessage() // 消息弹窗
let router = useRouter();
let title=ref('')
interface QueryParams {
  name: string;
  type: string;
}
const list=ref([])
const loading=ref(false)
const dialogVisible=ref(false)
const form=ref({dictType: 'deviceLabel', })
const options=ref([{label:'测试类型',value:'ssd'}])
const total=ref(10)
const modelValue=ref('')
const queryParams = ref<QueryParams>({
  label: null,
  value: null,
  dictType: 'deviceLabel',
  pageSize: 10,
  parentId: null
});

/** 获取数据 */
const getList = async () => {
  // queryParams.value.pageNo = 1
  let data = await getAccessoryTypePage(queryParams.value)
  list.value = data.list
  total.value=data.total

}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()

}

/** 导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}
/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DictionaryExport(queryParams.value)
    download.excel(data, '设备标签数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
/** 编辑按钮 */
const openEdit = (value:any) => {
  title.value='编辑标签'
  form.value.id=value.id
  form.value.label=value.label
  form.value.parentId=value.parentId
  form.value.value=value.value
  modelValue.value=value.groupType.toString()
  // console.log('sssss',value.groupType.toString())
  form.value.groupType=value.groupType

  dialogVisible.value = true
}
/** 新增按钮 */
const TypeAdd = () => {
  form.value={dictType: 'deviceLabel',}
  title.value='新增标签'
  dialogVisible.value = true
}

//跳转类型管理
const onTypeManagement=()=>{
  router.push(
    {
    path:'Equipmentlabel'
  })
}


/** 新增确认按钮 */
const DictionaryAdd =async () => {
  if (form.value.groupType){
    let result;
    if (title.value=='编辑标签') {
      result=await AccessoryTypeUpdate(form.value)
    }else {
      const data = await RandomCode('deviceLabel')
      form.value.label= data
      result = await AccessoryTypeAdd(form.value)}

    try {
      dialogVisible.value = false
      form.value={
        label: '',
        value: '',
        dictType: 'deviceLabel',
        parentId: ''
      }
      ElMessage.success('操作成功')
      handleQuery()
    }finally {

    }
  }else {
    ElMessage.warning('请填写标签类型')
  }


}

/** 删除按钮 */
const TypeDelete = async (id:number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await getDicByDictType({id})
    // 刷新列表
    await handleQuery()
    ElMessage.success('操作成功')

  } catch {}
}
const queryFormRef = ref() // 搜索的表单
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 表格选中数据 */
const selectionChange = (value) => {
  // selectionList.value = value
  // console.log('选中数据',selectionList.value)
}
onMounted(() => {
  getList()
})
</script>
<style scoped>


</style>
