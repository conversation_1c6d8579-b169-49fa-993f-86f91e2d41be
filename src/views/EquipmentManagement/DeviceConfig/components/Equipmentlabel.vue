
<template>
  <div class="box">
    <!-- 头部导航 -->
    <header class="header">
      <div class="header-left">
        <el-button type="info" :icon="ArrowLeft" @click="goback()" plain>返回</el-button>
        <div class="title">设备标签配置-类型管理</div>
      </div>
      <div class="header-right">
      </div>
    </header>
    <ContentWrap>
      <div style="display: flex;justify-content: space-between;">
        <div></div>
        <div>
          <el-button plain @click="addition('create')">
          <Icon icon="ep:plus" />
          新增
        </el-button>
        </div>
      </div>
      <el-table
        ref="tableRef"
        :data="list"
        style="margin-top: 20px;"
        v-loading="loading"
        :header-cell-style="{
            'background-color': '#F4F6F8',
            'font-size': '14px',
            color: '#3B3C3D'
          }"
      >
        <el-table-column align="center" label="类型名称" prop="value" />
        <el-table-column align="center" label="标签颜色" prop="colourCode">
          <template #default="scope">
            <el-button v-if="scope.row.colourCode" :style="{background: scope.row.colourCode}"/>
          </template>
        </el-table-column>
        <el-table-column  align="center" label="操作">
          <template #default="scope">
            <el-button
              link
              preIcon="ep:basketball"
              title="功能权限"
              type="primary"
              @click="openEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              link
              preIcon="ep:basketball"
              title="功能权限"
              type="primary"
              @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
    <Dialog v-model="dialogVisible" width="800"  :title="title">
      <el-form :model="popfrom" label-width="auto" style="margin-bottom: 60px;" >
        <el-form-item label="类型名称" required>
          <el-input style="margin-left: 10px;" v-model="popfrom.value"  />
        </el-form-item>
        <el-form-item label="类型颜色" required>
         <div class="typecolor" v-for="itm in selectColor" :key="itm.label" >
           <div
             class="colour"
             :style="{background: itm.value}"
             :class="{ colourbr: itm.label == currentLabel }"
             @click="onselect(itm)"
           >
             {{ itm.label === currentLabel ? '✔' : '' }}
           </div>
         </div>
        </el-form-item>

      </el-form>
      <template #footer>
        <div style="display: flex;align-items: center;justify-content: center;">
          <XButton title="确定"  width="200px" gradient @click="DictionaryAdd" />
          <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
        </div>
      </template>
    </Dialog>
  </div>
</template>

<script setup>
import { reactive,onMounted,ref} from 'vue'
import { ArrowLeft} from '@element-plus/icons-vue'
import *as Deveapi from '@/api/EquipmentManagement/DeviceConfig'
import {useRouter,useRoute} from "vue-router";
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter();
const routers = useRoute();
const id= ref('')
const total= ref('')
const loading=ref(false)

const popfrom=ref({
  dictType:'deviceLabelType',
  value:'',
  colourCode:'#FFC7C7'
})
const dialogVisible=ref(false)
const title=ref('新增类型')
let queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  dictType:"deviceLabelType"
})
const list = ref([]) // 列表的数据
const Colore = ref({'#FFC7C7':'1','#FFE9C7':'2','#FFFFC7':'3','#C7F4DD':'4',
                          '#C7FFFF':'5','#C7E9FF':'6','#C7D2FF':'7',
}) // 列表的数据
const selectColor = ref(
  [
    {label:'1',value:'#FFC7C7'},
    {label:'2',value:'#FFE9C7'},
    {label:'3',value:'#FFFFC7'},
    {label:'4',value:'#C7F4DD'},
    {label:'5',value:'#C7FFFF'},
    {label:'6',value:'#C7E9FF'},
    {label:'7',value:'#C7D2FF'},
  ]
) // 颜色选择
const currentLabel=ref('1')
const colourCode=ref('#FFC7C7')
//返回
const goback = () =>{
  router.go(-1)
}

//新增按钮
const addition =()=>{
  popfrom.value={
    dictType:'deviceLabelType',
    value:'',
    colourCode:'#FFC7C7'
  }
  currentLabel.value='1'
  colourCode.value='#FFC7C7'
  title.value='新增类型'
  dialogVisible.value = true
}

//编辑按钮
const openEdit = (value) =>{
  currentLabel.value= Colore.value[value.colourCode]
  popfrom.value.value=value.value
  popfrom.value.colourCode=value.colourCode
  popfrom.value.label=value.label
  popfrom.value.id=value.id
  title.value='编辑类型'
  dialogVisible.value = true
}

/** 删除按钮操作 */
const handleDelete = async (id) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await Deveapi.getDicByDictType({id})
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

//弹框按钮
const DictionaryAdd = async ()=>{
  try {
    if (!popfrom.value.value) {
      return ElMessage.warning('请填写必填项')

    }
    if (title.value =='新增类型'){
      const res=await Deveapi.RandomCode('deviceLabelType')
      const data =await Deveapi.AccessoryTypeAdd({...popfrom.value,colourCode:colourCode.value,label:res})
      ElMessage.success('操作成功')
      dialogVisible.value = false
      getList()
    }else {

      const data =await Deveapi.AccessoryTypeUpdate({...popfrom.value,colourCode:colourCode.value})
      ElMessage.success('操作成功')
      dialogVisible.value = false
      getList()
    }

  } catch {

  } finally {

  }

}

// 颜色选择
const onselect=(value)=>{
  currentLabel.value=value.label
  colourCode.value=value.value
}

const getList = async ()=>{
  const data =await Deveapi.getAccessoryTypePage(queryParams)
  list.value =  data.list
  total.value=data.total
}

onMounted(async () => {
  getList()
})

</script>
<style lang="scss" scoped>
.box {
  display: flex;
  flex-direction: column;
  background: #f5f6f8;
}
.colour{
  width: 38px;
  height: 22px;
  border-radius: 3px;
  margin-left: 10px;
  border: 1px solid rgb(241 243 246);
  //text-align: center;
  //line-height: 22px;
  //cursor: pointer;
  //background: red;
}
.header {
  height: 48px;
  padding: 0 16px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);

  &-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .title {
    font-size: 16px;
    font-weight: 500;
  }
}

.typecolor{
  display: flex;
  align-items: center;
  .colour{
    width: 38px;
    height: 22px;
    border-radius: 3px;
    margin-left: 10px;
    border: 1px solid rgb(241 243 246);
    text-align: center;
    line-height: 22px;
    cursor: pointer;
    background: red;
  }
  .colourbr{
    border: 1px solid #2d8cf0;
  }
}

/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
