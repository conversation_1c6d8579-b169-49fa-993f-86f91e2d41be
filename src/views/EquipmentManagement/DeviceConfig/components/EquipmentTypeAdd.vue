<template>
  <div class="box">
    <header>
      <el-button type="info" :icon="ArrowLeft" @click="goback()" plain>返回</el-button>
<!--      <div class="itmdiv">类型管理-{{routers.query.name}}</div>-->
      <div class="itmdiv">设备类型-{{ routeParms.type=='add' ? '新增' : '编辑' }}</div>
      <div class="rightbutten">
        <XButton title="确认" @click="onConfirm"  gradient />
        <el-button  @click="goback()">取消</el-button>
      </div>
    </header>
    <div class="middle">
      <div style="padding:15px;">
        <el-form
          ref="ruleFormRef"
          style="max-width: 90%;"
          :model="ruleForm"
          inline
          :rules="rules"
          label-width="auto"
          class="demo-ruleForm"
          status-icon
        >
          <el-form-item label="类型编码" prop="typeCode" required>
            <el-input style="min-width: 300px;" disabled v-model="ruleForm.typeCode" />
          </el-form-item>
          <el-form-item label="类型名称" prop="typeName" required>
            <el-input style="min-width: 300px;" v-model="ruleForm.typeName" />
          </el-form-item>
<!--          <el-form-item label="图标" prop="typeCode">-->
<!--            &lt;!&ndash;            <el-input v-model="ruleForm.typeCode" />&ndash;&gt;-->
<!--          </el-form-item>-->
        </el-form>

        <div style="margin-left:10px;">
          <el-form-item  label="图标" prop="typeCode">
            <el-upload
              ref="upload"
              v-model:file-list="fileList"
              style="display: flex;align-items: center; margin-left: 26px;width: 30px;height: 30px;border: 1px dashed #c0ccda;padding-left: 7px;"
              :action="uploadUrl"
              :headers="headers"
              :limit="1"
              accept=".png,.bmp,.jpg,.jpeg"
              :before-upload="beforeUpload"
              :on-remove="handleRemove"
              :on-success="handleSuccess"
              :on-error="handleError"
              :on-exceed="handleExceed"
            >
              <el-icon><Plus /></el-icon>
            </el-upload>
          </el-form-item>
        </div>
      </div>
    </div>
    <div class="banner">
      <div class="left">
        <!-- top -->
        <div class="top">
          <p class="head">
            <span></span>
            <span>配置自定义字段</span>
          </p>
          <div class="custom-checkbox-container">
            <el-checkbox
              v-model="checkAll"
              :indeterminate="isIndeterminate"
              @change="handleCheckAllChange"
              class="checkbox-right"
            >全选/取消全选
            </el-checkbox>
            <el-checkbox-group
              v-model="checkedCities"
              @change="handleCheckedCitiesChange"
            >
              <el-checkbox
                v-for="city in cities"
                :key="city.customCode"
                :label="city.id"
                :value="city.id"
                class="checkbox-right"
              >
                {{ city.customName }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>
      <!-- bottom -->

      <div class="right">
        <div class="top">
          <p class="head">
            <span></span>
            <span>配置默认模版</span>
          </p>
          <div>
            <el-tabs tab-position="top" v-model="activeName" @tab-click="handleClick" style="height: 200px" class="demo-tabs">
<!--              <el-tab-pane-->
<!--                v-for="itm in Template"-->
<!--                :key="itm.id"-->
<!--                :label="itm.modelName"-->
<!--                :name="itm.id"-->
<!--              >-->
<!--&lt;!&ndash;                <div v-if="itm.modelUrl" class="imgContainer"><img :src="itm.modelUrl" alt=""></div>&ndash;&gt;-->
<!--&lt;!&ndash;                <div v-else>暂未获取到图片</div>&ndash;&gt;-->
<!--              </el-tab-pane>-->

            </el-tabs>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>
import {reactive, onBeforeMount, onMounted,  ref, nextTick} from 'vue'
import {ArrowLeft, Plus} from '@element-plus/icons-vue'
import {useRouter,useRoute} from "vue-router";
import {
  createDeviceType,
  getCustomNoPage, getDeviceTypeDetails,
  updateDeviceType
} from '@/api/EquipmentManagement/SiteAdministration'
import emitter  from '@/utils/eventBus'
import { ElMessage, ElMessageBox } from 'element-plus'
import {getRefreshToken} from "@/utils/auth";
import {RandomCode} from "@/api/EquipmentManagement/DeviceConfig";
const data = reactive({})
const fileList = ref([])
const uploadUrl=import.meta.env.VITE_BASE_URL + '/admin-api/infra/file/upload'
const headers = computed(() => ({
  Authorization: 'Bearer ' + getRefreshToken()
}))
let ruleForm=ref({})
let routeParms=ref({})
const router = useRouter();
const routers = useRoute();
const activeName = ref('');
let isIndeterminate = ref(false)
let checkAll = ref(false)
let checkedCities = ref([])
let cities =ref([]  )
let Template =ref([]  )
//返回)
const goback = () =>{
  router.go(-1)
}

const rules = {
  typeCode: [{ required: true, message: '请输入类型编码', trigger: 'blur' }],
  typeName: [{ required: true, message: '请输入类型名称', trigger: 'blur' }],

}

//获取自定义字段列表
async function getFieldList(){
  let data=await getCustomNoPage()
    cities.value=data
}

// 上传成功
const handleSuccess = (response, uploadFile) => {
  if (response.code === 0) {
    ElMessage.success('上传成功')
    ruleForm.value.iconUrl=response.data

    // 处理返回的文件信息
  } else {
    ElMessage.error(response.msg || '上传失败')
  }
}
// 删除
const handleRemove = () => {
  ruleForm.value.iconUrl=''
  fileList.value=[]
}
// 处理后端返回的数据，进行回显
const handleInitialData = (data) => {
  if (data.iconUrl) {
    fileList.value = [{
      name: '设备类型icon', // 可以自定义名称
      url: data.iconUrl, // 后端返回的图片地址
      status: 'success', // 设置状态为成功
      uid: -1 // 需要一个唯一标识
    }]
  }
}
// 上传前验证
const beforeUpload = (file) => {
  const isImage = ['image/png', 'image/bmp', 'image/jpeg', 'image/jpg'].includes(file.type);
  const isLt1M = file.size / 1024 / 1024 < 1;

  if (!isImage) {
    ElMessage.error('只能上传png、bmp、jpg格式的图片!');
    return false;
  }
  if (!isLt1M) {
    ElMessage.error('图片大小不能超过1MB!');
    return false;
  }
  return true;
}

// 上传失败
const handleError = (error) => {
  ElMessage.error('上传失败：' + (error.msg || '未知错误'))
  ruleForm.value.iconUrl=''
}

//模板二维码选择
function handleClick(tab, event) {
  nextTick(()=>{
  })

}

//获取二维码模板列表
async function getTemplateList(){
  // let {data:res}=await TemplateList()
  // if (res.code==200){
  //   Template.value=res.data
  //   if (routeParms.value.type == 'add'){
  //     activeName.value=res.data[0].id
  //   }
  // }else {
  //   ElMessage.error(res.message)
  // }
}

//全选/取消全选
function handleCheckAllChange(val) {
  // checkedCities.value = val ? cities.value : []
  checkedCities.value = val ? cities.value.map(city => city.id) : []
  isIndeterminate.value = false
}

//复选框勾选值
function handleCheckedCitiesChange(value) {
  const checkedCount = value.length
  checkAll.value = checkedCount === cities.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < cities.value.length
}

//类型确认按钮
let ruleFormRef=ref(null)
function onConfirm() {
  let params={
    ...ruleForm.value,
    customFieldIds:checkedCities.value,
    tempId:activeName.value,

  }
  ruleFormRef.value.validate(async (valid) => {
    if (valid) {
      if (routeParms.value.type == 'add') {
        let data = await createDeviceType(params)
          ElMessage.success('添加成功')
        ruleForm.value.iconUrl=''
        fileList.value=[]
        emitter.emit('event-name', '1')
          router.go(-1)
      } else {
        let  val = await updateDeviceType(params)
          ElMessage.success('修改成功')
        ruleForm.value.iconUrl=''
        fileList.value=[]
        emitter.emit('event-name', '1')
          router.go(-1)

      }
    }
  })
}


//编辑获取详情接口
async function getDetail(value){
  let data=await getDeviceTypeDetails(value)
  // if (res.code==200){
    ruleForm.value=data
    checkedCities.value=data.customFieldIds ?data.customFieldIds :[]
    // activeName.value=res.data.qrTempIds ? res.data.qrTempIds : ''
    const hasCustomFields = data.customFieldIds?.length > 0;
    const hasCheckedItems = checkedCities.value?.length > 0;
    handleInitialData(data)
    if (hasCustomFields && hasCheckedItems) {
      // 如果选中数量等于总数，则全选；否则部分选中
      checkAll.value =data.customFieldIds.length === checkedCities.value.length;
      isIndeterminate.value = !checkAll.value;
    } else {
      // 如果没有自定义字段或没有选中项
      checkAll.value = false;
      isIndeterminate.value = hasCheckedItems;
    }

  // }else {
  //   ElMessage.error(res.message)
  // }

}
//获取随机编码
const getrandomcode= async ()=>{
  let data=await RandomCode()
  ruleForm.value.typeCode=data
}

onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})

onMounted(() => {
  routeParms.value=routers.query
  getFieldList()
  getTemplateList()
  if (routeParms.value.type == 'edit') {
    getDetail(routeParms.value.id)
  }else {
    getrandomcode()
  }
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
</script>
<style lang="scss" scoped>
.box{
  background: #F5F6F8;
  position: relative;
  //height: 100vh;
  .middle{
    height: 120px;
    background: #FFFFFF;
  }
  header{
    // width: 1616px;
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    height: 48px;
    background: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 16px;
    margin-bottom: 16px;
    .elbutten{
      width: 80px;
      height: 32px;
      background: #EAEDF2;
      border-radius: 4px 4px 4px 4px;
      border: 0;
      font-size: 14px;
      color: #323336;
      padding-left: 0;
      img{
        width: 24px;
        height: 24px;
      }
      span{
        display: inline-block;
        margin-top: 2px;
      }
    }
    .rightbutten{
      margin-right: 10px;
    }
    p{
      font-weight: 400;
      font-size: 14px;
      margin-left: 24px;
      span{
        &:first-child{
          color: #9C9DA2;
        }
      }
    }
  }
  .banner{
    width: 100%;
    display: flex;
    margin-top: 10px;

    .head{
      padding-bottom: 11px;
      border-bottom: 1px solid  #EAEDF2;
      margin:0 0 23px 0;
      display: flex;
      align-items: center;
      //justify-content: space-between;
      span{
        font-weight: bold;
        font-size: 16px;
        color: #323336;
        &:first-child{
          display: inline-block;
          width: 3px;
          height: 16px;
          background: linear-gradient( 180deg, #16B9FA 0%, #0058FF 100%);
          border-radius: 2px 2px 2px 2px;
          margin-right: 9px;
        }
      }
    }
    .left{
      flex: 1;
      // padding: 16px 24px;
      margin-right: 16px;
      .top,.bottom{
        min-height: 492px;
        max-height: 492px;
        overflow: auto;
        background: #FFFFFF;
        padding: 16px 16px;
        div{
          font-size: 14px;
          padding-left: 0;
          display: flex;
          flex-direction: column;
          font-family: Microsoft YaHei, Microsoft YaHei;
          p{
            margin: 0 0 24px 0;
            color: #323336;
            display: flex;
            span{
              display: inline-block;
              min-width: 90px;
              &:last-child{
                color: #6A6B71;
                min-width: none;
                margin-left: 24px;
              }
            }
          }
        }
        .none{
          justify-items: center;
          align-items: center;
          p{
            font-size: 14px;
            color: #9C9DA2;
          }
          img{
            width: 160px;
            height: 160px;
          }
        }
      }
      .bottom{
        margin-top: 16px;
        min-height: 304px;
      }
    }
    .right{
      width: 400px;
      background: #FFFFFF;
      padding: 20px;
      .btn{
        margin-left: 49%;
      }
      .imgContainer{
        width: 100%;
        max-height: 400px;
        overflow: auto;
        img {
          display: block; /* 去除底部间隙 */
          margin: 0 auto; /* 水平居中 */
          max-width: 100%; /* 最大宽度为容器的100% */
          height: auto; /* 高度自动调整以保持���高比 */
        }
      }
      .el-timeline{
        padding-left: 0;
        .el-timeline-item{
          padding-bottom: 48px;
        }
        .el-timeline-item__node--primary{
          color: #0058FF;
        }
        .el-timeline-item__node.is-hollow{
          border-width: 3px;
        }
        .el-timeline-item__timestamp{
          font-size: 13px;
        }
      }
    }
  }
}
:deep(.custom-checkbox-container) {
  padding: 0;
  width: 100%;

  .el-checkbox-group {
    width: 100%;
  }

  .checkbox-right {
    margin: 0 0 8px 0;
    padding: 8px 5px;
    width: 100%;
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    align-items: center;
    background-color: #F5F6F8;
    border-radius: 4px;

    :deep(.el-checkbox__label) {
      flex: 1;
      margin-right: auto;
      padding-left: 0;
    }

    :deep(.el-checkbox__input) {
      margin-left: 8px;
      flex-shrink: 0;
    }
  }
}
</style>
