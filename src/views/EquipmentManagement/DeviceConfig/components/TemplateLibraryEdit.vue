<template>
  <div class="box">
    <header>
      <el-button
        type="info"
        :icon="ArrowLeft"
        @click="goback()"
        plain
      >返回</el-button>
      <div class="itmdiv">二维码模板库-新增</div>
      <div class="rightbutten">
        <XButton title="确认" @click="confirmDialog"  gradient />
        <el-button  @click="router.go(-1)">取消</el-button>
      </div>
    </header>
    <div class="middle">
      <div style="padding:15px;">
        <el-form
          ref="ruleFormRef"
          style="max-width: 90%;"
          :model="ruleForm"
          :rules="rules"
          label-width="auto"
          class="demo-ruleForm"
          status-icon
        >
          <el-form-item label="模板编码" prop="tempCode" required>
            <el-input v-model="ruleForm.tempCode" />
          </el-form-item>

          <el-form-item label="模板名称" prop="tempName" required>
            <el-input v-model="ruleForm.tempName" />
          </el-form-item>

          <el-form-item label="模板说明" prop="tempDesc" >
            <el-input v-model="ruleForm.tempDesc" />
          </el-form-item>

          <el-form-item label="模板尺寸" required>
            <el-form-item prop="length"
                          class="nested-form-item"
                          :rules="[
             { required: true, message: '请输入长度', trigger: 'blur' },
             { type: 'number', message: '长度必须为数字', trigger: 'blur',
              transform: value => Number(value) }
              ]">
              <el-input v-model="ruleForm.length" placeholder="长度" />
            </el-form-item>

            <span style="margin: 0 10px">*</span>
            <el-form-item prop="width" class="nested-form-item" :rules="[
                { required: true, message: '请输入宽度', trigger: 'blur' },
                { type: 'number', message: '宽度必须为数字', trigger: 'blur', transform: value => Number(value) }
               ]">
              <el-input v-model="ruleForm.width" placeholder="宽度" />
            </el-form-item>
            <span style="margin: 0 10px">px</span>
          </el-form-item>
          <el-form-item label="上传文件" prop="tempHtml" required>
            <el-upload
              v-model:file-list="fileList"
              v-if="!fileList.length"
              :action="upload"
              list-type="picture-card"
              :limit="1"
              name="htmlFile"
              :headers="uploadHeaders"
              :before-upload="beforeTemplateUpload"
              accept=".html"
              :on-remove="handleRemove"
              :on-success="handleUploadSuccess"
              :on-exceed="handleExceed"
            >
              <el-icon v-if="!fileList.length"><Plus /></el-icon>
            </el-upload>
            <div v-else>
              <div class="uploaded-file">
                已上传文件: {{ fileList[0].name }}
                <el-button type="text" @click="handleRemove(fileList[0])">删除</el-button>
              </div>
            </div>
            <!--            <el-dialog v-model="dialogVisible">-->
            <!--              <img w-full :src="dialogImageUrl" alt="Preview Image" />-->
            <!--            </el-dialog>-->
          </el-form-item>

          <el-form-item label="背景颜色" prop="backgroundType" required>
            <el-radio-group @click="changegss($event)" v-model="ruleForm.backgroundType" size="large">
              <el-radio-button :label="1" >纯色</el-radio-button>
              <el-radio-button :label="2" >图片</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="背景取色器" prop="background" v-if="ruleForm.backgroundType=='1'" required>
            <div class="demo-color-block" >
              <!--              <span class="demonstration">背景取色器</span>-->
              <el-color-picker size="large" v-model="ruleForm.background" />
            </div>
          </el-form-item>
          <el-form-item label="背景图上传" prop="background" v-if="ruleForm.backgroundType=='2'" required>
            <el-upload
              v-model:file-list="backgroundList"
              v-if="!backgroundList?.length"
              :action="backgroundUpload"
              list-type="picture-card"
              name="file"
              :limit="1"
              :headers="uploadHeaders"
              :on-preview="handlePictureCardPreview"
              :before-upload="backgroundTemplateUpload"
              :on-remove="backgroundRemove"
              accept=".jpg,.jpeg,.png"
              :on-success="backgroundUploadSuccess"
              :on-exceed="handleExceed"
            >
              <el-icon v-if="backgroundList?.length<1" ><Plus /></el-icon>
            </el-upload>
            <div v-else>
              <div class="uploaded-file" style="display: flex;align-items: center;">
                <div
                  :style="{
                    width: '200px',
                    height: '200px',
                    backgroundImage: `url(${ruleForm.background})`,
                    backgroundPosition: 'center',
                    backgroundSize: 'contain',
                    backgroundRepeat: 'no-repeat'
                            }"></div>
                <el-button type="text" @click="backgroundRemove(fileList[0])">删除</el-button>
              </div>
            </div>
          </el-form-item>
        </el-form>
        <div class="bottomStyle">
          <!--          <div class="demo-color-block" v-if="ruleForm.backgroundType=='1'">-->
          <!--            <span class="demonstration">背景取色器</span>-->
          <!--            <el-color-picker size="large" v-model="ruleForm.color1" />-->
          <!--          </div>-->

        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import {reactive, onBeforeMount, onMounted, ref, } from 'vue'
import { ArrowLeft,Plus} from '@element-plus/icons-vue'
import {useRouter,useRoute} from "vue-router";
import { ElMessage } from 'element-plus'
import { getRefreshToken } from '@/utils/auth'
import {
  createQRTemplate,
  getQRTemplateEdit,
  updateQRTemplate
} from "@/api/EquipmentManagement/SiteAdministration";
const data = reactive({})
let ruleForm=ref({})
let ruleFormRef=ref()
let backgroundUpload=import.meta.env.VITE_BASE_URL + '/admin-api/infra/file/upload'
const upload=import.meta.env.VITE_BASE_URL + '/admin-api/infra/temp-info/upload'
// let upload='http://*************:48080'
// let upload='api/model/upload'
const router = useRouter();
let params = ref({})
const routers = useRoute();
let fileList = ref([])
let backgroundList = ref([])
let radio = ref('')
// const uploadHeaders = ref({
//   // 在这里添加需要的请求头
//   'token': sessionStorage.getItem('Token')
// })
const uploadHeaders = computed(() => ({
  Authorization: 'Bearer ' + getRefreshToken()
}))
//返回)
const goback = () =>{
  router.go(-1)
}


let  rules=reactive({
  tempCode: [
    { required: true, message: '请输入模板编码', trigger: 'blur' },
  ],
  tempName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
  ],
  length: [
    { required: true, message: '请输入模板尺寸', trigger: 'blur' },
  ],
  backgroundType: [
    { required: true, message: '请选择背景颜色', trigger: 'change' },
  ],
  background: [
    { required: true, message: '请选择背景', trigger: 'blur' },
  ],
  tempHtml: [
    { required: true, message: '请上传二维码模板', trigger: 'blur' },
  ],
})




function changegss(value){
  console.log(value)
  ruleForm.value.background=''
  backgroundList.value = []
  // nextTick(() => {
  //   if (ruleForm.value.backgroundType=='2'&& params.value.name =='编辑'){
  //     console.log('sssssssss',ruleForm.value.background)
  //     return
  //   }
  //   if (ruleForm.value.backgroundType=='1'&& params.value.name =='编辑'){
  //     console.log('sssssssss1111',ruleForm.value.background)
  //     return
  //   }
  // })

}


onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {

  params.value = routers.query

  if  (params.value.name=='新增'){
    ruleForm.value.backgroundType='1'
    ruleForm.value.background=''
  }

  if (params.value && params.value.id) {
    // getDetail(params.value.id)
    getDetail(params.value.id)
  }
})

//编辑获取详情
async function getDetail(value) {
  let data=await getQRTemplateEdit(value)

    ruleForm.value=data
    if (data.tempHtml){
      fileList.value[0]={name:'模板二维码'}
    }
    if (data.background && data.backgroundType=='2'){
      backgroundList.value[0]={name:data.background}
    }
  // }else{
  //   ElMessage.error(res.message)
  // }
}


//背景上传成功回调
function backgroundUploadSuccess(response, uploadFile, uploadFiles) {
  if (response.code == 0) {  // 假设你的接口返回格式包含 code 字段
    ElMessage.success('上传成功')
    // 保存返回的文件URL或其他信息
    ruleForm.value.background = response.data
  }
  else {
    ElMessage.error(response.message || '上传失败')
  }
}

const backgroundRemove = (uploadFile) => {
  backgroundList.value = []
  ruleForm.value.background=''
}

//上传模板成功回调
function handleUploadSuccess(response, uploadFile, uploadFiles) {
  if (response.code == 0) {  // 假设你的接口返回格式包含 code 字段
    ElMessage.success('上传成功')
    // 保存返回的文件URL或其他信息
    ruleForm.value.tempHtml = response.data
  } else {
    ElMessage.error(response.message || '上传失败')
  }
}

const handleRemove = (uploadFile) => {
  fileList.value = []
  ruleForm.value.tempHtml=''
}




//新增编辑确认
async function confirmDialog() {
  ruleFormRef.value.validate(async valid => {
    if (valid) {
      if (routers.query.name=='新增'){
        let data=await createQRTemplate(ruleForm.value)
          ElMessage.success('新增成功')
          router.go(-1)

      }else {
        let val=await updateQRTemplate(ruleForm.value)
          ElMessage.success('编辑成功')
          router.go(-1)
      }

    }
  })
}


const handleExceed = (files) => {
  ElMessage.warning('只能上传一张图片')
}


// 添加新函数来验证文件上传
const beforeTemplateUpload = (file) => {
  const isHTML = file.type === 'text/html'
  if (!isHTML) {
    ElMessage.error('只能上传HTML文件!')
    return false
  }
  return true
}// 添加新函数来验证文件上传

const backgroundTemplateUpload = (file) => {
  // 定义允许的文件类型
  const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']

  // 检查文件类型
  const isAllowedType = allowedTypes.includes(file.type)

  // 可以同时检查文件大小（比如限制在 2MB 以内）
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isAllowedType) {
    ElMessage.error('只能上传 JPG/PNG 格式的图片!')
    return false
  }

  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }

  return true
}

</script>
<style lang="scss" scoped>
.box {
  background: #F5F6F8;
  position: relative;
  .middle {
    min-height: 508px;
    background: #FFFFFF;
  }

  header {
    // width: 1616px;
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    height: 48px;
    background: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 16px;
    margin-bottom: 16px;

    .elbutten {
      width: 80px;
      height: 32px;
      background: #EAEDF2;
      border-radius: 4px 4px 4px 4px;
      border: 0;
      font-size: 14px;
      color: #323336;
      padding-left: 0;

      img {
        width: 24px;
        height: 24px;
      }

      span {
        display: inline-block;
        margin-top: 2px;
      }
    }

    .rightbutten {
      margin-right: 10px;
    }

    p {
      font-weight: 400;
      font-size: 14px;
      margin-left: 24px;

      span {
        &:first-child {
          color: #9C9DA2;
        }
      }
    }
  }

  .bottomStyle{
    margin-left: 90px;
  }

}
:deep(.custom-upload) {
  .el-upload--picture-card {
    border: none !important;  // 移除边框
    background: none !important;  // 移除背景
    margin: 0 !important;  // 移除外边距
    padding: 0 !important;  // 移除内边距
  }

  .el-upload-list--picture-card {
    .el-upload-list__item {
      border: none !important;  // 移除已上传项的边框
      margin: 0 !important;
    }
  }

  // 如果还需要调整大小
  .el-upload, .el-upload-list__item {
    width: auto !important;  // 或设置具体尺寸
    height: auto !important;  // 或设置具体尺寸
  }
}

</style>
