
<template>
  <ContentWrap class="h-1/1">
    <span class="TitleStyle">规格型号配置</span>
  </ContentWrap>
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="flex flex-wrap items-start -mb-15px"
    >
      <el-form-item label="" prop="value" class="!mr-3">
        <el-input
          v-model="queryParams.value"
          class="!w-240px"
          clearable
          placeholder="输入规格型号id/名称"
        />
      </el-form-item>
      <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
        <div>
          <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:device-dict:query']"  gradient @click="handleQuery" />
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />
            重置
          </el-button>
        </div>
        <div>
          <el-button
            plain
            @click="TypeAdd"
            v-hasPermi="['infra:device-dict:create']"
          >
            <Icon icon="ep:plus" />
            新增
          </el-button>
          <el-button
            plain
            @click="handleImport"
            v-hasPermi="['infra:device-dict:import']"
          >
            <Icon icon="ep:upload" />
            导入
          </el-button>
          <el-button
            v-hasPermi="['infra:device-dict:import']"
            plain
            @click="handleExport" :loading="exportLoading"
          >
            <Icon icon="ep:download" />
            导出
          </el-button>
        </div>
      </div>
    </el-form>
  </ContentWrap>
  <!-- 列表 -->
  <ContentWrap>
    <el-table
      ref="myTable"
      v-loading="loading"
      @selection-change="selectionChange"
      :data="list">
      <el-table-column align="center" label="规格型号id" prop="label" />
      <el-table-column align="center" label="规格型号名称" prop="value" />
      <el-table-column :width="200" align="center" label="操作">
        <template #default="scope">
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            v-hasPermi="['infra:device-dict:update']"
            @click="openEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            v-hasPermi="['infra:device-dict:delete']"
            @click="TypeDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <Dialog v-model="dialogVisible" width="800"  :title="title">
    <el-form :model="form" label-width="auto" style="margin-bottom: 60px;" >
<!--      <el-form-item label="规格型号编码">-->
<!--        <el-input  :disabled="title=='编辑规格型号'" v-model="form.label" />-->
<!--      </el-form-item>-->
      <el-form-item label="规格型号名称">
        <el-input  v-model="form.value" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <XButton title="确定"  width="200px"  gradient  @click="DictionaryAdd" />
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>
  <ImportDictionary ref="importFormRef" dictType="modelConfig"  />
</template>
<script lang="ts" setup>/** 初始化 **/
import {useRouter} from "vue-router";
import * as DeptApi from "@/api/system/dept";
import {
  getAccessoryTypePage,
  AccessoryTypeUpdate,
  AccessoryTypeAdd,
  getDicByDictType, RandomCode
} from "@/api/EquipmentManagement/DeviceConfig";
import {DictionaryExport} from "@/api/EquipmentManagement/SiteAdministration";
import ImportDictionary
  from "@/views/EquipmentManagement/DeviceConfig/components/ImportDictionary.vue";
import download from '@/utils/download'
const message = useMessage() // 消息弹窗
let router = useRouter();
let title=ref('')
interface QueryParams {
  name: string;
  type: string;
}
const list=ref([])
const loading=ref(false)
const dialogVisible=ref(false)
const form=ref({dictType: 'modelConfig',})
const total=ref(10)
const queryParams = ref<QueryParams>({
  label: '',
  value: '',
  dictType: 'modelConfig',
  parentId: '',
  pageSize: 10,
});

/** 获取数据 */
const getList = async () => {
  // queryParams.value.pageNo = 1
  let data = await getAccessoryTypePage(queryParams.value)
  list.value =  data.list
  total.value=data.total

}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()

}

/** 新增确认按钮 */
const DictionaryAdd = async () => {
  let result;
  if (title.value=='编辑规格型号') {
    result=await AccessoryTypeUpdate(form.value)
  }else {
    const data = await RandomCode('modelConfig')
    form.value.label= data
    result = await AccessoryTypeAdd(form.value)}

  try {
    dialogVisible.value = false
    form.value={
      label: '',
      value: '',
      dictType: 'modelConfig',
      parentId: ''
    }
    ElMessage.success('操作成功')
    handleQuery()
  }finally {

  }

}

/** 导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DictionaryExport(queryParams.value)
    download.excel(data, '规划型号数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}


/** 编辑按钮 */
const openEdit = (value:any) => {
  title.value='编辑规格型号'
  form.value.id=value.id
  form.value.label=value.label
  form.value.value=value.value
  dialogVisible.value = true
}
/** 新增按钮 */
const TypeAdd = () => {
  form.value={
    dictType: 'modelConfig',

  }
  title.value='新增规格型号'
  dialogVisible.value = true
  // router.push('EquipmentTypeAdd')
}
/** 删除按钮 */
const TypeDelete = async (id:number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await getDicByDictType({id})
    // 刷新列表
    await handleQuery()
    ElMessage.success('操作成功')

  } catch {}
}
const queryFormRef = ref() // 搜索的表单
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 表格选中数据 */
const selectionChange = (value) => {
  // selectionList.value = value
  // console.log('选中数据',selectionList.value)
}
onMounted(() => {
  getList()
})
</script>
<style scoped>


</style>
