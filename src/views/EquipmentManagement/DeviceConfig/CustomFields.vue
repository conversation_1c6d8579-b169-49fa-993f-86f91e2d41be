
<template>
  <ContentWrap class="h-1/1">
    <span class="TitleStyle">自定义字段库</span>
  </ContentWrap>
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="flex flex-wrap items-start -mb-15px"
    >
      <el-form-item label="" prop="customName" class="!mr-3">
        <el-input
          v-model="queryParams.customName"
          class="!w-240px"
          clearable
          placeholder="字段名称"
        />
      </el-form-item>

      <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
        <div>
          <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:custom-field-info:query']" gradient @click="handleQuery" />
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />
            重置
          </el-button>
        </div>
       <div>
         <el-button
           plain
           @click="TypeAdd"
           v-hasPermi="['infra:custom-field-info:create']"
         >
           <Icon icon="ep:plus" />
           新增
         </el-button>
       </div>
      </div>
    </el-form>
  </ContentWrap>
  <!-- 列表 -->
  <ContentWrap>
    <el-table
      ref="myTable"
      v-loading="loading"
      @selection-change="selectionChange"
      :data="list">
      <el-table-column align="left" type="selection"   width="55" />
      <el-table-column type="index" label="序号" width="55" align="left">
        <template #default="scope">
          <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column align="left" label="Code" prop="customCode" />
      <el-table-column align="left" label="字段名称" prop="customName" />
      <el-table-column align="left" label="字段类型" prop="verifyType">
        <template #default="scope">
          {{options[scope.row.verifyType]}}
        </template>
      </el-table-column>
      <el-table-column align="left" label="字段长度" prop="fieldLength" />
      <el-table-column align="left" label="字段描述" prop="fieldDesc" />
      <el-table-column align="left" label="是否必填" prop="required" >
        <template #default="scope">
          {{scope.row.required=='0'? '非必填' : '必填'}}
        </template>
      </el-table-column>
      <el-table-column :width="200" align="left" label="操作">
        <template #default="scope">
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openEdit(scope.row)"
            v-hasPermi="['infra:custom-field-info:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="TypeDelete(scope.row.id)"
            v-hasPermi="['infra:custom-field-info:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <Dialog v-model="dialogVisible" width="800"  :title="title">
    <div class="dialog-content">
      <div class="leftCenter">
        <div>
          <el-form
            ref="ruleFormRef"
            style="max-width: 600px"
            :model="ruleForm"
            :rules="rules"
            label-width="auto"
            class="demo-ruleForm"
            :size="formSize"
            status-icon
          >
            <el-form-item label="Code" prop="customCode" >
              <el-input v-model="ruleForm.customCode" />
            </el-form-item>
            <el-form-item label="字段名称" prop="customName" >
              <el-input v-model="ruleForm.customName" />
            </el-form-item>
            <el-form-item label="字段描述" prop="fieldDesc">
              <el-input type="textarea" v-model="ruleForm.fieldDesc" />
            </el-form-item>
            <el-form-item label="字段类型" prop="fieldDesc">
              <span>文本</span>
            </el-form-item>
            <el-form-item label="字段长度" prop="fieldLength">
              <el-input v-model="ruleForm.fieldLength" />
            </el-form-item>
            <el-form-item label="是否必填" prop="required" >
              <el-radio-group v-model="ruleForm.required">
                <el-radio :label=1 >是</el-radio>
                <el-radio :label=0 >否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>

      </div>
    </div>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <XButton title="确定" width="200px" gradient @click="addCustom" />
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>/** 初始化 **/
import {useRouter} from "vue-router";
import {
  getCustomPage,
  createCustom,
  updateCustom, deleteCustom
} from '@/api/EquipmentManagement/SiteAdministration'
const message = useMessage() // 消息弹窗
let router = useRouter();
let title=ref('')
interface QueryParams {
  name: string;
  type: string;
}
const list=ref([])
const loading=ref(false)
const dialogVisible=ref(false)
const form=ref({})
const options=ref(['文本','自定义','身份证','信用代码','手机号','电话'])
const total=ref(10)
const ruleForm=ref({})
const queryParams = ref<QueryParams>({
  pageNo:1,
  pageSize: 10,
  customName:'',

});

/** 获取数据 */
const getList = async () => {
  queryParams.value.pageNo = 1
let data = await getCustomPage(queryParams.value);
  list.value =  data.list
  total.value=data.total
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()

}
/** 编辑按钮 */
const openEdit = (value:any) => {
  title.value='编辑字段'
  ruleForm.value={...value}
  dialogVisible.value = true
}
/** 新增按钮 */
const TypeAdd = () => {
  title.value='新增字段'
  ruleForm.value={}
  dialogVisible.value = true
}


const addCustom = async () =>{
  if (title.value=='新增字段'){
    let data = await createCustom(ruleForm.value);
    ElMessage.success('操作成功')
    dialogVisible.value = false
    ruleForm.value={}
    getList()
  }else {
   let val=await updateCustom(ruleForm.value);
    ElMessage.success('操作成功')
    dialogVisible.value = false
    ruleForm.value={}
    getList()
  }


}

/** 删除按钮 */
const TypeDelete = async (id:string) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await deleteCustom(id)
    await getList()
    ElMessage.success('操作成功')
    // 刷新列表

  } catch {}
}
const queryFormRef = ref() // 搜索的表单
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 表格选中数据 */
const selectionChange = (value) => {
  // selectionList.value = value
  // console.log('选中数据',selectionList.value)
}
onMounted(() => {
  getList()
})
</script>
<style scoped>
.dialog-content {
  //display: flex;
  //gap: 20px;
  background: rgb(243 243 243);
  //margin-top: -30px !important;
  .leftCenter{
    //flex: 1;
    //margin: 0 auto;
    //margin-left: 125px;
    padding: 30px;
    background: rgb(243 243 243);
  }
  .rightCenter{
    flex: 1;
    padding: 10px;
    max-height: 350px;
    overflow: auto;
    background: rgb(243 243 243);
  }
}

</style>
