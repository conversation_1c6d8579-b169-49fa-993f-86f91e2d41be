
<template>
  <ContentWrap class="h-1/1">
    <span class="TitleStyle">辅件类型配置</span>
  </ContentWrap>
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="flex flex-wrap items-start -mb-15px"
    >
      <el-form-item label="" prop="value" class="!mr-3">
        <el-input
          v-model="queryParams.value"
          class="!w-240px"
          clearable
          placeholder="输入类型编号/名称"
        />
      </el-form-item>
      <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
        <div>
          <XButton title="查询" v-hasPermi="['infra:device-dict:query']" preIcon="ep:search" gradient @click="handleQuery" />
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />
            重置
          </el-button>
        </div>
        <div>
          <el-button
            plain
            @click="TypeAdd"
            v-hasPermi="['infra:device-dict:create']"
          >
            <Icon icon="ep:plus" />
            新增
          </el-button>
          <el-button
            plain
            @click="handleImport"
            v-hasPermi="['infra:device-dict:import']"

          >
            <Icon icon="ep:upload" />
            导入
          </el-button>
          <el-button
            plain
            @click="handleExport" :loading="exportLoading"
            v-hasPermi="['infra:device-dict:export']"

          >
            <Icon icon="ep:download" />
            导出
          </el-button>
        </div>
      </div>
    </el-form>
  </ContentWrap>
  <!-- 列表 -->
  <ContentWrap>
    <el-table
      ref="myTable"
      row-key="id"
      v-loading="loading"
      @selection-change="selectionChange"
      :data="list"
      :header-cell-style="{
             'background-color': '#F4F6F8',
            'font-size': '14px',
            'color': '#3B3C3D',}"
    >
      <el-table-column align="center" type="selection"   width="55" />
      <el-table-column align="center" label="类型名称" prop="value" />
      <el-table-column align="center" label="类型编码" prop="label" />
      <el-table-column align="left" label="图标" prop="iconUrl">
        <template #default="scope">
          <el-avatar shape="square" v-if="scope.row.iconUrl" :size="50" :src="scope.row.iconUrl" />
          <span v-else></span>
        </template>
      </el-table-column>
      <el-table-column :width="280" align="center" label="操作">
        <template #default="scope">
          <el-button
            v-if="scope.row.parentId=='0'"
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openSubtype(scope.row)"
          >
            新增子类型
          </el-button>
          <el-button
            link
            v-if="scope.row.parentId=='0'"
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="ImportSubtypes(scope.row.id)"
          >
           导入子类型
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openDetails(scope.row)"
            v-hasPermi="['infra:device-dict:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="TypeDelete(scope.row.id)"
            v-hasPermi="['infra:device-dict:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
<!--    <Pagination-->
<!--      v-model:limit="queryParams.pageSize"-->
<!--      v-model:page="queryParams.pageNo"-->
<!--      :total="total"-->
<!--      @pagination="getList"-->
<!--    />-->
  </ContentWrap>
  <Dialog v-model="dialogVisible" width="800"  :title="title">
    <el-form :model="form" label-width="auto" style="margin-bottom: 60px;" >
      <el-form-item label="类型编码" required>
        <el-input disabled  v-model="form.label" />
      </el-form-item>
      <el-form-item label="类型名称" required>
        <el-input v-model="form.value" />
      </el-form-item>
      <el-form-item  label="图标" prop="typeCode">
        <el-upload
          ref="upload"
          v-model:file-list="fileList"
          style="display: flex;align-items: center; width: 30px;height: 30px;border: 1px dashed #c0ccda;padding-left: 7px;"
          :action="uploadUrl"
          :headers="headers"
          :limit="1"
          accept=".png,.bmp,.jpg,.jpeg"
          :before-upload="beforeUpload"
          :on-remove="handleRemove"
          :on-success="handleSuccess"
          :on-error="handleError"
          :on-exceed="handleExceed"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
      </el-form-item>
      <el-form-item  label="" style="margin-left: 35px;" prop="typeCode">
        <div>注：图片格式必须为 png,bmp,jpg；不可大于 1M；建议使用 png 格式图片，以保持最佳效果；建议图片尺寸为 144px*144px</div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <XButton title="确定"  width="200px"  gradient  @click="DictionaryAdd" />
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>

  <ImportDictionary ref="importFormRef" dictType="accessoryType" @success="getList" :parentId="parentId" />
</template>
<script lang="ts" setup>/** 初始化 **/
import {useRouter} from "vue-router";
import {
  AccessoryTypeAdd,
  AccessoryTypeUpdate,
  getAccessoryTypePage,
  getDicByDictType, RandomCode
} from '@/api/EquipmentManagement/DeviceConfig'
import download from '@/utils/download'
import ImportDictionary from './components/ImportDictionary.vue'
import {DictionaryExport} from "@/api/EquipmentManagement/SiteAdministration";
import {Plus} from "@element-plus/icons-vue";
import {ref} from "vue";
import {getRefreshToken} from "@/utils/auth";
const message = useMessage() // 消息弹窗
let router = useRouter();
let title=ref('')
const fileList = ref([])
const selectionList = ref([])
const uploadUrl=import.meta.env.VITE_BASE_URL + '/admin-api/infra/file/upload'
const headers = computed(() => ({
  Authorization: 'Bearer ' + getRefreshToken()
}))
interface QueryParams {
  typeCode: string;
}
interface QueryParams {
  name: string;
  type: string;
}
const list=ref([])
const loading=ref(false)
const parentId=ref('')
const dialogVisible=ref(false)
const form=ref({
  label: '',
  value: '',
  dictType: 'accessoryType',
  iconUrl:'',
  parentId: ''
})
const total=ref(10)
const queryParams = ref<QueryParams>({
  pageNo:1,
  pageSize: 1000,
  label: '',
  dictType: 'accessoryType',
});

/** 获取数据 */
const getList  = async () => {
  queryParams.value.pageNo = 1
 let data = await getAccessoryTypePage(queryParams.value)
  list.value = buildTree(data.list)
  total.value=data.total
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()

}

/** 导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    let ids= selectionList.value.map(itm=>itm.id)
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DictionaryExport({...queryParams.value,ids})
    download.excel(data, '设备数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}


/** 导入子类型 */
const ImportSubtypes = (id:number) => {
  importFormRef.value.open()
  parentId.value=id

}


/** 新增子类型按钮 */
const openSubtype = (value:any) => {
  getRandomCode()
  form.value.parentId=value.id
  title.value='新增子类型'
  dialogVisible.value = true
}

/** 编辑按钮 */
const openDetails = (value:any) => {
  form.value.id=value.id
  form.value.label=value.label
  form.value.parentId=value.parentId
  form.value.value=value.value
  title.value='编辑'
  dialogVisible.value = true
}

/** 新增按钮 */
const TypeAdd = () => {
  form.value={dictType: 'accessoryType',}
  getRandomCode()
  title.value='新增一级类型'
  dialogVisible.value = true
  // router.push('EquipmentTypeAdd')
}
/** 删除按钮 */
const TypeDelete = async (id:number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await getDicByDictType({id})
    // 刷新列表
    await handleQuery()
    ElMessage.success('操作成功')

  } catch {}
}
const queryFormRef = ref() // 搜索的表单
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 新增确认按钮 */
const DictionaryAdd =async () => {
  if (form.value.value && form.value.label){
    let result;
    if (title.value=='编辑') {
      result=await AccessoryTypeUpdate(form.value)
    }else { result = await AccessoryTypeAdd(form.value)}

    try {
      dialogVisible.value = false
      form.value={
        label: '',
        value: '',
        dictType: 'accessoryType',
        parentId: ''
      }
      ElMessage.success('操作成功')
      handleQuery()
    }finally {

    }
  }else {
    ElMessage.warning('请填写必填项')
  }


}

/** 表格选中数据 */
const selectionChange = (value) => {
  selectionList.value = value
  // console.log('选中数据',selectionList.value)
}

const buildTree=(items: TreeNode[], parentId?: number): TreeNode[] =>{
  const itemMap = {};
  const tree = [];

  // 首先将所有项存入映射表，用id作为键
  items.forEach(item => {
    itemMap[item.id] = { ...item, children: [] };
  });

  // 遍历所有项，将它们添加到它们的父项的children数组中
  items.forEach(item => {
    if (item.parentId) {
      const parent = itemMap[item.parentId];
      if (parent) {
        parent.children.push(itemMap[item.id]);
      }
    } else {
      // 如果没有parentId，则认为是根节点
      tree.push(itemMap[item.id]);
    }
  });

  return tree;
}

//获取随机code
const getRandomCode=async()=>{
  let data=await RandomCode('accessoryType')
  form.value.label=data
  form.value.iconUrl=''
  form.value.value=''
  fileList.value=[]

}

// 上传成功
const handleSuccess = (response, uploadFile) => {
  if (response.code === 0) {
    ElMessage.success('上传成功')
    form.value.iconUrl=response.data

    // 处理返回的文件信息
    console.log('sdsds',form.value)
  } else {
    ElMessage.error(response.msg || '上传失败')
  }
}
// 删除
const handleRemove = () => {
  form.value.iconUrl=''
  fileList.value=[]
}
// 处理后端返回的数据，进行回显
const handleInitialData = (data) => {
  if (data.iconUrl) {
    fileList.value = [{
      name: '设备类型icon', // 可以自定义名称
      url: data.iconUrl, // 后端返回的图片地址
      status: 'success', // 设置状态为成功
      uid: -1 // 需要一个唯一标识
    }]
  }
}
// 上传前验证
const beforeUpload = (file) => {
  const isImage = ['image/png', 'image/bmp', 'image/jpeg', 'image/jpg'].includes(file.type);
  const isLt1M = file.size / 1024 / 1024 < 1;

  if (!isImage) {
    ElMessage.error('只能上传png、bmp、jpg格式的图片!');
    return false;
  }
  if (!isLt1M) {
    ElMessage.error('图片大小不能超过1MB!');
    return false;
  }
  return true;
}

// 上传失败
const handleError = (error) => {
  ElMessage.error('上传失败：' + (error.msg || '未知错误'))
  form.value.iconUrl=''
}


onMounted(() => {
  getList()
})
</script>
<style scoped>


</style>
