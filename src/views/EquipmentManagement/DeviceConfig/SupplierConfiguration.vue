
<template>
  <ContentWrap class="h-1/1">
    <span class="TitleStyle">供应商配置</span>
  </ContentWrap>
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="flex flex-wrap items-start -mb-15px"
    >
      <el-form-item label="" prop="value" class="!mr-3">
        <el-input
          v-model="queryParams.value"
          class="!w-240px"
          clearable
          placeholder="输入供应商/名称"
        />
      </el-form-item>

      <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
    <div>
      <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:device-dict:query']" gradient @click="handleQuery" />
      <el-button @click="resetQuery">
        <Icon class="mr-5px" icon="ep:refresh" />
        重置
      </el-button>
    </div>
        <div>
          <el-button
            plain
            @click="TypeAdd"
            v-hasPermi="['infra:device-dict:create']"
          >
            <Icon icon="ep:plus" />
            新增
          </el-button>
          <el-button
            plain
            @click="handleImport"
            v-hasPermi="['infra:device-dict:import']"
          >

            <Icon icon="ep:upload" />
            导入
          </el-button>
          <el-button
            plain
            @click="handleExport" :loading="exportLoading"
            v-hasPermi="['infra:device-dict:export']"
          >
            <Icon icon="ep:download" />
            导出
          </el-button>
        </div>
      </div>
    </el-form>
  </ContentWrap>
  <!-- 列表 -->
  <ContentWrap>
    <el-table
      ref="myTable"
      v-loading="loading"
      @selection-change="selectionChange"
      :data="list">
      <el-table-column align="center" label="供应商编码" prop="label" />
      <el-table-column align="center" label="供应商名称" prop="value" />
      <el-table-column :width="200" align="center" label="操作">
        <template #default="scope">
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openEdit(scope.row)"
            v-hasPermi="['infra:device-dict:update']"

          >
            编辑
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="TypeDelete(scope.row.id)"
            v-hasPermi="['infra:device-dict:delete']"

          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <Dialog v-model="dialogVisible" width="800"  :title="title">
    <el-form :model="form" label-width="auto" style="margin-bottom: 60px;" >
      <el-form-item label="供应商名称" required>
        <el-input  v-model="form.value"  />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
<!--        <el-button  style="width: 200px;" type="primary" >确定</el-button>-->
        <XButton title="确定"  width="200px" gradient @click="DictionaryAdd" />
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>

  <ImportDictionary ref="importFormRef" dictType="supplierDirectory"  @success="getList" />

</template>
<script lang="ts" setup>/** 初始化 **/
import {useRouter} from "vue-router";
import * as DeptApi from "@/api/system/dept";
import {
  getAccessoryTypePage,
  AccessoryTypeUpdate,
  AccessoryTypeAdd,
  getDicByDictType, RandomCode
} from "@/api/EquipmentManagement/DeviceConfig";
import ImportDictionary from './components/ImportDictionary.vue'
import {DictionaryExport} from "@/api/EquipmentManagement/SiteAdministration";
import download from '@/utils/download'
const message = useMessage() // 消息弹窗
let router = useRouter();
let title=ref('')
interface QueryParams {
  name: string;
  type: string;
}
const list=ref([])
const loading=ref(false)
const dialogVisible=ref(false)
const form=ref({
  label:'',
  dictType:'supplierDirectory'
})
const total=ref(10)
const queryParams = ref<QueryParams>({
  pageNo:1,
  pageSize: 10,
  label: '',
  dictType: 'supplierDirectory',
});

/** 获取数据 */
const getList = async () => {
  // queryParams.value.pageNo = 1
  let data = await getAccessoryTypePage(queryParams.value)
  // list.value = buildTree(data.list)
  list.value = data.list
  total.value=data.total

}
/** 导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}
/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DictionaryExport(queryParams.value)
    download.excel(data, '供应商数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()

}
/** 编辑按钮 */
const openEdit = (value:any) => {
  title.value='编辑供应商'
  form.value.id=value.id
  form.value.label=value.label
  form.value.value=value.value
  dialogVisible.value = true
}
/** 新增按钮 */
const TypeAdd = () => {
  title.value='新增供应商'
  form.value={
    dictType:'supplierDirectory'
  }
  dialogVisible.value = true

  // router.push('EquipmentTypeAdd')
}
/** 删除按钮 */
const TypeDelete = async (id:number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await getDicByDictType({id})
    // 刷新列表
    await handleQuery()
    ElMessage.success('操作成功')

  } catch {}
}
const queryFormRef = ref() // 搜索的表单
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 新增确认按钮 */
const DictionaryAdd = async () => {
if (form.value.value){
  let result;
  if (title.value=='编辑供应商') {
    result=await AccessoryTypeUpdate(form.value)
  }else {
    const data = await RandomCode('supplierDirectory')
    form.value.label= data
    result = await AccessoryTypeAdd(form.value)}

  try {
    dialogVisible.value = false
    form.value={
      label: '',
      value: '',
      dictType: 'supplierDirectory',
      parentId: ''
    }
    ElMessage.success('操作成功')
    handleQuery()
  }finally {

  }
}else {
  ElMessage.warning('供应商名称不能为空')
}


}


/** 表格选中数据 */
const selectionChange = (value) => {
  // selectionList.value = value
  // console.log('选中数据',selectionList.value)
}
onMounted(() => {
  getList()
})
</script>
<style scoped>


</style>
