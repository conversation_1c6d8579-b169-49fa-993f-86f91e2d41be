<template>
  <ContentWrap class="h-1/1">
    <span class="TitleStyle">设备目录配置</span>
  </ContentWrap>
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
    >
      <el-form-item label="" prop="value" class="!mr-3">
        <el-input
          v-model="queryParams.value"
          class="!w-240px"
          clearable
          placeholder="输入字段Code/名称"
        />
      </el-form-item>

      <el-form-item class="!mr-3">
        <XButton title="查询" preIcon="ep:search" gradient @click="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  <!-- 列表 -->
  <ContentWrap>
    <el-table
      ref="myTable"
      row-key="id"
      v-loading="loading"
      @selection-change="selectionChange"
      :data="list">
      <el-table-column align="center" label="设备字段名称" prop="value" />
      <el-table-column align="center" label="code" prop="label" />
      <el-table-column :width="200" align="center" label="列表展示">
        <template #default="{ row }">
<!--          <el-switch-->
<!--            v-model="value2"-->
<!--            class="ml-2"-->
<!--            style="&#45;&#45;el-switch-on-color: #13ce66; &#45;&#45;el-switch-off-color: #ff4949"-->
<!--          />-->
          <el-checkbox
            :checked="row.status== 0"
            label=""
            @click="handleCheckboxClick(row)"
            size="large" />
        </template>
      </el-table-column>
      <el-table-column :width="200" align="center" label="排序">
        <template #default="scope">
          <el-button
            link
            size="large"
            type="primary"
            @click="openSubtype('top',scope.row)"
          >
            <el-icon>
              <Top />
            </el-icon>
          </el-button>
          <el-button
            link
            size="large"
            type="primary"
            @click="openSubtype('bottom',scope.row)"
          >
            <el-icon>
              <Bottom />
            </el-icon>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
<!--    <Pagination-->
<!--      v-model:limit="queryParams.pageSize"-->
<!--      v-model:page="queryParams.pageNo"-->
<!--      :total="total"-->
<!--      @pagination="getList"-->
<!--    />-->
  </ContentWrap>
</template>
<script lang="ts" setup>/** 初始化 **/
import {useRouter} from "vue-router";
import * as DeptApi from "@/api/system/dept";
import {
  Top,Bottom
} from '@element-plus/icons-vue'
import {AccessoryTypeUpdate, getAccessoryTypePage,DictionarySort} from "@/api/EquipmentManagement/DeviceConfig";
import type { TenantVO } from '@/api/EquipmentManagement/DeviceConfig'
const message = useMessage() // 消息弹窗
let router = useRouter();
let title=ref('')

interface TreeNode {
  id: string;
  label: string;
  value: string;
  status: number;
  dictType: string;
  loading?: boolean;
  parentId?: string;
  children?: TreeNode[];
}

const list = ref<TreeNode[]>([])
const loading = ref(false)
const disableding=ref(false)
const dialogVisible=ref(false)
const form=ref({})
const total = ref(10)
const queryParams = ref<TenantVO>({
  pageNo: 1,
  pageSize: 100,
  label: '',
  dictType: 'deviceTab',
  value: '',
  parentId: '',
  id: '',
  sort: '',
  typeCode: '',
})

/** 获取数据 */
const getList = async () => {
  queryParams.value.pageNo = 1
  let data = await getAccessoryTypePage(queryParams.value)
  // list.value = buildTree(data.list)
  list.value = [...data.list]
  total.value=data.total
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()

}

const handleCheckboxClick = async (row: TreeNode) => {
  // 如果正在处理中，直接返回
  if (row.loading) return
  
  try {
    // 设置loading状态
    row.loading = true
    
    // 调用接口
    const newStatus = row.status === 0 ? 1 : 0
    const res = await AccessoryTypeUpdate({
      id: row.id,
      label: row.label,
      value: row.value,
      dictType: row.dictType,
      parentId: row.parentId || '',
      pageNo: 1,
      pageSize: 10,
      sort: '',
      typeCode: '',
      status:newStatus
    })
    
    if (res) {
      row.status=newStatus
        handleQuery()
      // 接口成功，更新状态
      // const index = list.value.findIndex(item => item.id === row.id)
      // if (index !== -1) {
      //   // 使用 Vue 3 的响应式更新方式
      //   list.value[index] = {
      //     ...list.value[index],
      //     status: newStatus
      //   }
      //   // 强制触发更新
      //   list.value = [...list.value]
      // }
      ElMessage.success('状态更新成功')
    } else {
      // 接口失败，保持原状态
      ElMessage.error(res.message || '状态更新失败')
    }
  } catch (error) {
    // 发生错误，保持原状态
    ElMessage.error('状态更新失败')
  } finally {
    // 清除loading状态
    row.loading = false
  }
}

/** 排序按钮操作 */
const openSubtype = (sort: string, value: TreeNode) => {
  if (sort === 'top') {
    DictionarySort({
      id: value.id,
      dictType: value.dictType,
      sortType: '0',
    })
  } else {
    DictionarySort({
      id: value.id,
      dictType: value.dictType,
      sortType: '1',
    })
  }
  // nextTick(()=>{
  //   handleQuery()
  // })

  setTimeout(()=>{
    handleQuery()
  },200)


}

const queryFormRef = ref() // 搜索的表单
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
const buildTree = (items: TreeNode[], parentId?: string): TreeNode[] => {
  const itemMap: Record<string, TreeNode> = {}
  const tree: TreeNode[] = []

  // 首先将所有项存入映射表，用id作为键
  items.forEach(item => {
    itemMap[item.id] = { ...item, children: [] }
  })

  // 遍历所有项，将它们添加到它们的父项的children数组中
  items.forEach(item => {
    if (item.parentId) {
      const parent = itemMap[item.parentId]
      if (parent && parent.children) {
        parent.children.push(itemMap[item.id])
      }
    } else {
      // 如果没有parentId，则认为是根节点
      tree.push(itemMap[item.id])
    }
  })

  return tree
}

const selectionChange = (selection: TreeNode[]) => {
  // Handle selection change
  console.log('Selection changed:', selection)
}

onMounted(() => {
  getList()
})
</script>
<style scoped>


</style>
