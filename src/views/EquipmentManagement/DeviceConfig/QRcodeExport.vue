
<template>
  <ContentWrap class="h-1/1">
    <span class="TitleStyle">二维码导入记录</span>
  </ContentWrap>
  <!-- 列表 -->
  <ContentWrap>
    <el-table
      ref="myTable"
      v-loading="loading"
      @selection-change="selectionChange"
      :data="list">
<!--      <el-table-column align="left" type="selection"   width="55" />-->
      <el-table-column type="index" label="序号" width="55" align="left">
        <template #default="scope">
          <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column align="left" label="导出时间" prop="exportTime" />
      <el-table-column align="left" label="导出数量" prop="exportNum" />
      <el-table-column align="left" label="状态" prop="exportStatus">
        <template #default="scope">
          {{scope.row.exportStatus=='0'? '导出失败': '已导出'}}
        </template>
      </el-table-column>
      <el-table-column align="left" label="操作账户" prop="name2" />
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
</template>
<script lang="ts" setup>/** 初始化 **/
import {useRouter} from "vue-router";
import {QRExportPage} from "@/api/EquipmentManagement/SiteAdministration";
const message = useMessage() // 消息弹窗
let router = useRouter();
interface QueryParams {
  name: string;
  type: string;
}
const list=ref([])
const loading=ref(false)
const total=ref(10)
const queryParams = ref<QueryParams>({
  pageNo:1,
  pageSize: 10,
});

const selectionChange=()=>{

}

/** 获取数据 */
const getList = async () => {
  let data=await QRExportPage(queryParams.value)
  list.value = data.list
}

onMounted(() => {
  getList()
})
</script>
<style scoped>


</style>
