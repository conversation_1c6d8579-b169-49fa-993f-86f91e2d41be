
<template>
  <ContentWrap class="h-1/1">
    <span class="TitleStyle">二维码模板库</span>
  </ContentWrap>
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="flex flex-wrap items-start -mb-15px"
    >
      <el-form-item label="" prop="tempName" class="!mr-3">
        <el-input
          v-model="queryParams.tempName"
          class="!w-240px"
          clearable
          placeholder="模板编号/名称"
        />
      </el-form-item>
      <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
        <div>
          <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:temp-info:query']" @click="handleQuery"  gradient />
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />
            重置
          </el-button>
        </div>
        <div>
          <el-button
            plain
            @click="TypeAdd"
            v-hasPermi="['infra:temp-info:create']"
          >
            <Icon icon="ep:plus" />

            新增
          </el-button>
          <el-button
            plain
            @click="TypeDelete"
            :icon="Delete"
            v-hasPermi="['infra:temp-info:delete']"
          >
            删除
          </el-button>
        </div>
      </div>
    </el-form>
  </ContentWrap>
  <!-- 列表 -->
  <ContentWrap>
    <el-table
      ref="myTable"
      v-loading="loading"
      @selection-change="selectionChange"
      :data="list">
      <el-table-column align="left" type="selection"   width="55" />
      <el-table-column type="index" label="序号" width="55" align="left">
        <template #default="scope">
          <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column align="left" label="模版编码" prop="tempCode" />
      <el-table-column align="left" label="模版名称" prop="tempName" />
      <el-table-column align="left" label="模版尺寸" prop="length">
        <template #default="scope">
          <span>{{scope.row.length}}*{{scope.row.width}}</span>
        </template>
      </el-table-column>
      <el-table-column :width="200" align="left" label="操作">
        <template #default="scope">
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openEdit(scope.row.id)"
            v-hasPermi="['infra:temp-info:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openPreview(scope.row.tempUrl)"
          >
           预览
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <el-image-viewer
    v-if="showPreview"
    :url-list="srcList"
    show-progress
    @close="showPreview = false"
  />
<!--  <Dialog v-model="dialogVisible" width="800"  :title="title">-->

<!--    <template #footer>-->
<!--      <div style="display: flex;align-items: center;justify-content: center;">-->
<!--        <el-button  style="width: 200px;" type="primary" >确定</el-button>-->
<!--        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>-->
<!--      </div>-->
<!--    </template>-->
<!--  </Dialog>-->
</template>
<script lang="ts" setup>/** 初始化 **/
import {useRouter} from "vue-router";
import { Delete } from '@element-plus/icons-vue'
import {codeTemplatePage, deleteQRTemplate} from "@/api/EquipmentManagement/SiteAdministration";
const message = useMessage() // 消息弹窗
let router = useRouter();
let title=ref('')
interface QueryParams {
  pageNo: string;
  pageSize: string;
  tempName: string;
}
const list=ref([])
const loading=ref(false)
const showPreview=ref(false)
const dialogVisible=ref(false)
const form=ref({})
const selectionList=ref([])
const srcList=ref([])
const options=ref([{label:'测试类型',value:'ssd'}])
const total=ref(10)
const queryParams = ref<QueryParams>({
  pageNo:1,
  pageSize: 10,
  tempName:''
});

/** 获取数据 */
const getList = async () => {
  queryParams.value.pageNo = 1
  let data=await codeTemplatePage(queryParams.value)
  list.value=data.list
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()

}
/** 预览按钮 */
const openPreview = (url:string) => {
  srcList.value=[]
  if (url){
    srcList.value.push(url)
    console.log('url,',srcList.value)
    showPreview.value=true
  }else {
    ElMessage.warning('暂无预览图')
  }

  // title.value='预览'
  // dialogVisible.value = true
}
/** 新增按钮 */
const TypeAdd = () => {
  router.push(
    {
    path:'TemplateLibraryEdit',
      query:{
        name:'新增'
      }
  })
}
/** 编辑按钮 */
const openEdit = (id:string) => {
  router.push(
    {
    path:'TemplateLibraryEdit',
      query:{
        name:'编辑',
        id
      }
  })
}
/** 删除按钮 */
const TypeDelete = async () => {
  if (selectionList.value.length>0){
    const ids = selectionList.value.map((item) => item.id);
    try {
      // 删除的二次确认
      await message.delConfirm()
      // 发起删除
      await deleteQRTemplate({ids})
      // 刷新列表
      await getList()
      message.success('操作成功')
    } catch {}
  }else {
    message.warning('请选择数据')
  }

}
const queryFormRef = ref() // 搜索的表单
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 表格选中数据 */
const selectionChange = (value) => {
  selectionList.value = value
  // console.log('选中数据',selectionList.value)
}
 onMounted(() => {
    getList()
 })
</script>
<style scoped>


</style>
