
<template>
  <ContentWrap class="h-1/1">
    <span class="TitleStyle">设备类型配置</span>
  </ContentWrap>
  <ContentWrap>

    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="flex flex-wrap items-start -mb-15px"
    >
      <el-form-item label="" prop="typeCode" class="!mr-3">
        <el-input
          v-model="queryParams.typeCode"
          class="!w-240px"
          clearable
          placeholder="输入类型编号/名称"
        />
      </el-form-item>
      <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
        <div>
          <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:device-type:query']" gradient @click="handleQuery" />
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />
            重置
          </el-button>
        </div>
        <div>
          <el-button
            plain
            @click="TypeAdd"
            v-hasPermi="['infra:device-type:create']"
          >
            <Icon icon="ep:plus" />
            新增
          </el-button>
          <el-button
            plain
            @click="handleImport"
            v-hasPermi="['infra:device-type:import']"
          >
            <Icon icon="ep:upload" />
            导入
          </el-button>
          <el-button
            plain
            @click="handleExport" :loading="exportLoading"
            v-hasPermi="['infra:device-type:export']"

          >
            <Icon icon="ep:download" />
            导出
          </el-button>
        </div>
      </div>
    </el-form>
  </ContentWrap>
  <!-- 列表 -->
  <ContentWrap>
    <el-table
      ref="myTable"
      row-key="id"
      v-loading="loading"
      @selection-change="selectionChange"
      :data="list">
      <el-table-column align="left" type="selection"   width="55" />
      <el-table-column align="left" label="类型名称" prop="typeName" />
      <el-table-column align="left" label="类型编码" prop="typeCode" />
      <el-table-column align="left" label="图标" prop="iconUrl">
        <template #default="scope">
          <el-avatar shape="square" v-if="scope.row.iconUrl" :size="50" :src="scope.row.iconUrl" />
          <span v-else></span>
        </template>
      </el-table-column>
      <el-table-column align="left" label="自定义字段" prop="customFieldIds" />
      <el-table-column align="left" label="默认二维码模板" prop="tempUrl" />
      <el-table-column :width="280" align="left" label="操作">
        <template #default="scope">
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            v-if="scope.row.parentId==0"
            @click="openSubtype(scope.row.id)"
          >
            新增子类型
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            v-if="scope.row.parentId==0"
            @click="ImporType(scope.row.id)"
          >
            导入子类型
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            v-hasPermi="['infra:device-type:update']"
            @click="openDetails(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            v-hasPermi="['infra:device-type:delete']"

            title="功能权限"
            type="primary"
            @click="TypeDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
<!--    <Pagination-->
<!--      v-model:limit="queryParams.pageSize"-->
<!--      v-model:page="queryParams.pageNo"-->
<!--      :total="total"-->
<!--      @pagination="getList"-->
<!--    />-->
  </ContentWrap>
  <Dialog v-model="dialogVisible" width="800"  :title="DialogTitle">
    <el-form :model="addForm" label-width="auto" style="margin-bottom: 60px;" >
      <el-form-item label="类型编码" required>
        <el-input disabled v-model="addForm.typeCode" />
      </el-form-item>
      <el-form-item label="类型名称" required>
        <el-input v-model="addForm.typeName" />
      </el-form-item>
      <el-form-item  label="图标" prop="typeCode">
        <el-upload
          ref="upload"
          v-model:file-list="fileList"
          style="display: flex;align-items: center; width: 30px;height: 30px;border: 1px dashed #c0ccda;padding-left: 7px;"
          :action="uploadUrl"
          :headers="headers"
          :limit="1"
          accept=".png,.bmp,.jpg,.jpeg"
          :before-upload="beforeUpload"
          :on-remove="handleRemove"
          :on-success="handleSuccess"
          :on-error="handleError"
          :on-exceed="handleExceed"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
      </el-form-item>
      <el-form-item  label="" style="margin-left: 35px;" prop="typeCode">
        <div>注：图片格式必须为 png,bmp,jpg；不可大于 1M；建议使用 png 格式图片，以保持最佳效果；建议图片尺寸为 144px*144px</div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <XButton title="确定"  width="200px" gradient @click="addSubtype" />
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>

  <ImportComponents ref="importFormRef" path="infra/device-type/import/template" fileName="设备类型导入模板" title="设备类型导入" downloadPath="infra/device-type/get/template" @success="getList" />

  <ImportDictionary :judge="false" ref="importFormRefCope" dictType="accessoryType" :parentId="parentId"  @success="getList" />

</template>
<script lang="ts" setup>/** 初始化 **/
import {useRouter} from "vue-router";
import {RandomCode, getTypelist} from '@/api/EquipmentManagement/DeviceConfig'
import {
  createDeviceType,
  deleteDeviceType, devicetypeExportexcel, updateDeviceType
} from "@/api/EquipmentManagement/SiteAdministration";
import emitter from '@/utils/eventBus'
import download from '@/utils/download'
import ImportDictionary from './components/ImportDictionary.vue'
import {Plus} from "@element-plus/icons-vue";
import {ref} from "vue";
import {getRefreshToken} from "@/utils/auth";
const message = useMessage() // 消息弹窗
let router = useRouter();
const fileList = ref([])
const uploadUrl=import.meta.env.VITE_BASE_URL + '/admin-api/infra/file/upload'
const headers = computed(() => ({
  Authorization: 'Bearer ' + getRefreshToken()
}))
interface QueryParams {
  typeCode: string;
}
const DialogTitle=ref('新增子类型')
const parentId=ref('')
const list=ref([])
const selectionList=ref([])
const loading=ref(false)
const dialogVisible=ref(false)
const addForm=ref({parentId:'',typeName:''})
const total=ref(10)
const queryParams = ref<QueryParams>({
  typeCode:'',
});

/** 获取数据 */
const getList = async () => {
  // queryParams.value.pageNo = 1
 let data=await getTypelist(queryParams.value)
  list.value = buildTree(data.list)
  total.value=data.total
}

/** 搜索按钮操作 */
const handleQuery = () => {
  // queryParams.value.pageNo = 1
  getList()

}

/** 导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}
/** 导入子类型 */
const importFormRefCope = ref()
const ImporType = (id:number) => {
  parentId.value=id
  importFormRefCope.value.open(id)
}


/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    let ids= selectionList.value.map(itm=>itm.id)
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await devicetypeExportexcel({...queryParams.value,ids})
    download.excel(data, '设备数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 新增子类型按钮 */
const openSubtype = async (id:string) => {
 let data=await RandomCode()
  addForm.value.typeCode=data
  DialogTitle.value='新增子类型'
  addForm.value.parentId=id
  addForm.value.typeName=''
  fileList.value=[]
  addForm.value.iconUrl=''
  dialogVisible.value = true
}

/** 新增子类型确认按钮 */
const addSubtype = async () => {
if (addForm.value.typeCode && addForm.value.typeName ){
  let data= DialogTitle.value=='新增子类型'  ? await createDeviceType(addForm.value) : await updateDeviceType(addForm.value)
  ElMessage.success('操作成功')
  dialogVisible.value = false
  getList()
}else {
  ElMessage.warning('请填写必填项')
}

}
emitter.on('event-name', (payload) => {
  // console.log('111111',payload)
  resetQuery()
  // 处理事件
})
// 组件卸载时取消监听
onUnmounted(() => {
  // emitter.off('event-name')
})
/** 新增按钮 */
const TypeAdd = () => {
  router.push({
    path:'EquipmentTypeAdd',
    query:{
      type:'add'
    }
  })
}
/** 编辑按钮 */
const openDetails = (value:any) => {
  if(value.parentId=='0'){
    router.push({
      path:'EquipmentTypeAdd',
      query:{
        type:'edit',
        id:value.id}
    })
  }else {
    DialogTitle.value='编辑子类型'
    addForm.value={...value}
    dialogVisible.value = true
  }

}
/** 删除按钮 */
const TypeDelete = async (id:string) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await deleteDeviceType(id)
    // 刷新列表
    await getList()
    message.success('操作成功')

  } catch {}
}
const queryFormRef = ref() // 搜索的表单
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}


const buildTree=(items: TreeNode[], parentId?: number): TreeNode[] =>{
  const itemMap = {};
  const tree = [];

  // 首先将所有项存入映射表，用id作为键
  items.forEach(item => {
    itemMap[item.id] = { ...item, children: [] };
  });

  // 遍历所有项，将它们添加到它们的父项的children数组中
  items.forEach(item => {
    if (item.parentId) {
      const parent = itemMap[item.parentId];
      if (parent) {
        parent.children.push(itemMap[item.id]);
      }
    } else {
      // 如果没有parentId，则认为是根节点
      tree.push(itemMap[item.id]);
    }
  });

  return tree;
}
// 上传成功
const handleSuccess = (response, uploadFile) => {
  if (response.code === 0) {
    ElMessage.success('上传成功')
    addForm.value.iconUrl=response.data

    // 处理返回的文件信息
  } else {
    ElMessage.error(response.msg || '上传失败')
  }
}
// 删除
const handleRemove = () => {
  addForm.value.iconUrl=''
  fileList.value=[]
}
// 处理后端返回的数据，进行回显
const handleInitialData = (data) => {
  if (data.iconUrl) {
    fileList.value = [{
      name: '设备类型icon', // 可以自定义名称
      url: data.iconUrl, // 后端返回的图片地址
      status: 'success', // 设置状态为成功
      uid: -1 // 需要一个唯一标识
    }]
  }
}
// 上传前验证
const beforeUpload = (file) => {
  const isImage = ['image/png', 'image/bmp', 'image/jpeg', 'image/jpg'].includes(file.type);
  const isLt1M = file.size / 1024 / 1024 < 1;

  if (!isImage) {
    ElMessage.error('只能上传png、bmp、jpg格式的图片!');
    return false;
  }
  if (!isLt1M) {
    ElMessage.error('图片大小不能超过1MB!');
    return false;
  }
  return true;
}

// 上传失败
const handleError = (error) => {
  ElMessage.error('上传失败：' + (error.msg || '未知错误'))
  addForm.value.iconUrl=''
}


/** 表格选中数据 */
const selectionChange = (value) => {
  selectionList.value = value
  // console.log('选中数据',selectionList.value)
}
onMounted(() => {
  getList()
})
</script>
<style scoped>


</style>
