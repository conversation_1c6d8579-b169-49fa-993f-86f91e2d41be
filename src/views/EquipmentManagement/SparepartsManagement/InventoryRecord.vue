<template>
  <div>
    <ContentWrap>
      <el-tabs v-model="activeName" class="demo-tabs" >
        <el-tab-pane label="入库" name="first">
          <StorageRecord/>
        </el-tab-pane>
        <el-tab-pane label="出库" name="second">
          <StorageOutbound/>
        </el-tab-pane>
      </el-tabs>
    </ContentWrap>
  </div>
</template>
<script lang="ts" setup>
import StorageRecord from './components/StorageRecord.vue';
import StorageOutbound from './components/StorageOutbound.vue';


defineOptions({ name: 'InventoryRecord' })


const activeName = ref('first') // 列表的总页数




/** 初始化 **/
onMounted(() => {
})
</script>
<style scoped>


</style>
