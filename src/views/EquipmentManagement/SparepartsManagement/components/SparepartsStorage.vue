<template>
  <div>
    <Dialog v-model="dialogVisible" width="1000" :title="title">
     <div class="dialog-content">
      <div class="dialog-details"   v-for="(itm,index) in 5 " :key="index" >
       <div class="dialog-title">
         <div>备件名称:<span>xxxx备件</span></div>
         <div>品牌:<span>摄像头-球机</span></div>
         <div>规格型号:<span>xxxxx型号</span></div>
         <div>单位:<span>台</span></div>
       </div>
        <div class="dialog-quantity">
          <div v-if="title=='入库'" class="quantityLeft">
            <div style="margin: 10px;">当前可用库存: <span style="margin-left: 10px;" >3</span></div>
            <div style="margin: 10px;">
              <el-form
              ref="ruleFormRef"
              style="max-width: 600px"
              :model="ruleForm"
              label-width="auto"
              class="demo-ruleForm"
              status-icon
            >
              <el-form-item label="入库数量" prop="name">
                <el-input v-model="ruleForm.name" />
              </el-form-item>
              <el-form-item label="入库说明" prop="name">
                <el-input type="textarea" :rows="2" v-model="ruleForm.name" />
              </el-form-item>

            </el-form>
            </div>
          </div>
          <div v-if="title=='出库'" class="quantityLeft">
            <div style="margin: 10px;">当前可用库存: <span style="margin-left: 10px;" >3</span></div>
            <div style="margin: 10px;">
              <el-form
              ref="ruleFormRef"
              style="max-width: 600px"
              :model="ruleForm"
              label-width="auto"
              class="demo-ruleForm"
              status-icon
            >
              <el-form-item label="入库数量" prop="name">
                <el-input v-model="ruleForm.name" />
              </el-form-item>
              <el-form-item label="入库说明" prop="name">
                <el-input type="textarea" :rows="2" v-model="ruleForm.name" />
              </el-form-item>

            </el-form>
            </div>
          </div>
          <div v-if="title=='入库'" class="quantityRight">
            <div style="margin: 10px; margin-bottom: 10px; ">
              <el-form
              ref="ruleFormRef"
              style="max-width: 600px"
              :model="ruleForm"
              label-width="auto"
              class="demo-ruleForm"
              status-icon
            >
              <el-form-item  prop="name">
                <div style="display: flex; gap: 10px;">
                  <el-form-item label="合格证" prop="name">
                    <el-radio-group v-model="ruleForm.resource">
                      <el-radio value="Sponsorship">有</el-radio>
                      <el-radio value="Venue">无</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="说明书" prop="name">
                    <el-radio-group v-model="ruleForm.resource">
                      <el-radio value="Sponsorship">有</el-radio>
                      <el-radio value="Venue">无</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>

              </el-form-item>
              <el-form-item label="包装是否完整" prop="name">
                <el-radio-group v-model="ruleForm.resource">
                  <el-radio value="Sponsorship">是</el-radio>
                  <el-radio value="Venue">否</el-radio>
                </el-radio-group>
              </el-form-item>
                <el-form-item label="附件" prop="name">
                  <el-upload
                    ref="upload"
                    class="upload-demo"
                    style="display: flex;"
                    action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
                    :limit="1"
                    :on-exceed="handleExceed"
                    :auto-upload="false"
                  >
                    <template #trigger>
                      <el-button type="primary">点击上传</el-button>
                    </template>
                  </el-upload>
              </el-form-item>

            </el-form>
            </div>

          </div>
          <div v-if="title=='出库'" class="quantityRight">
            <div style="margin: 10px;">
              <el-form
                ref="ruleFormRef"
                style="max-width: 600px"
                :model="ruleForm"
                label-width="auto"
                class="demo-ruleForm"
                status-icon
              >

                <el-form-item label="所属项目" prop="name">
                  <el-select v-model="ruleForm.region" placeholder="please select your zone">
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </el-form-item>

                <el-form-item label="附件" prop="name">
                  <el-upload
                    ref="upload"
                    class="upload-demo"
                    style="display: flex;"
                    action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
                    :limit="1"
                    :on-exceed="handleExceed"
                    :auto-upload="false"
                  >
                    <template #trigger>
                      <el-button type="primary">点击上传</el-button>
                    </template>
                  </el-upload>
                </el-form-item>

              </el-form>
            </div>

          </div>
        </div>
      </div>
     </div>
      <template #footer>
        <div style="display: flex;align-items: center;justify-content: center;">
          <el-button  style="width: 200px;" type="primary" >确定</el-button>
          <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
        </div>
      </template>
    </Dialog>
  </div>
</template>
<script lang="ts" setup>

defineOptions({ name: 'SparepartsStorage' })

let dialogVisible=ref(false)
let title=ref('')
let ruleForm=ref({})

const open = (type: string, id?: number) => {
  if (type=='store'){
    title.value=`入库`
  }else {
    title.value=`出库`
  }
  dialogVisible.value = true
}

defineExpose({ open })

const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}

const submitUpload = () => {
  upload.value!.submit()
}

/** 初始化 **/
onMounted(() => {
})
</script>
<style scoped>
.dialog-content{
  max-height: 500px;
  overflow: auto;
 .dialog-details{
   margin-top: 10px;
  height: 250px;
   border: 1px solid #DCDCDC;
   .dialog-title{
     display: flex;

     align-items: center;
     background: #f3f7fe;
     margin: 15px;
     padding: 10px 0;
     div{
       margin-left: 15px;
     }
     span{
       margin-left: 20px;
     }
   }
   .dialog-quantity{
     margin: 15px;
     display: flex;
     .quantityLeft{
       width: 300px;
       background: #f3f7fe;
       height: 167px;

     }
     .quantityRight{
       width: 680px;
       margin-left: 10px;
       background: #f3f7fe;
       height: 167px;
     }
   }
 }
}


</style>
