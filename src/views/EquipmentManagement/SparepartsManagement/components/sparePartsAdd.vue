<template>
  <div class="box">
    <header>
      <el-button type="info" :icon="ArrowLeft" @click="goback()" plain>返回</el-button>
      <div class="itmdiv">备件列表-新增</div>
      <div class="rightbutten">
        <el-button type="primary" @click="validateForms" >保存</el-button>
        <el-button @click="onReset" >重置</el-button>
      </div>
    </header>

    <div class="banner">
      <div class="editCenter">
        <el-form
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          label-width="110px"
          class="demo-ruleForm"
          :size="formSize"
          status-icon
        >
          <el-form-item label="备件名称" prop="code">
            <el-input v-model="ruleForm.code" />
          </el-form-item>
          <el-form-item label="备件类型" prop="name">
            <el-select v-model="ruleForm.region" placeholder="please select your zone">
              <el-option label="Zone one" value="shanghai" />
              <el-option label="Zone two" value="beijing" />
            </el-select>
          </el-form-item>
          <el-form-item label="供应商" prop="name">
            <el-select v-model="ruleForm.region1" placeholder="please select your zone">
              <el-option label="Zone one" value="shanghai" />
              <el-option label="Zone two" value="beijing" />
            </el-select>
          </el-form-item>
          <el-form-item label="规格型号" prop="projectName">
            <el-input v-model="ruleForm.projectName" />
          </el-form-item>
          <el-form-item label="品牌" prop="orgName">
            <el-input v-model="ruleForm.orgName" />
          </el-form-item>
          <el-form-item label="单位" prop="areaName">
            <el-input v-model="ruleForm.areaName" />
          </el-form-item>
          <el-form-item label="入库数量" prop="address">
            <el-input v-model="ruleForm.address" />
          </el-form-item>
          <el-form-item label="包装是否完整">
            <el-radio-group v-model="ruleForm.resource">
              <el-radio value="Sponsor">是</el-radio>
              <el-radio value="Venue">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="合格证">
            <el-radio-group v-model="ruleForm.resource1">
              <el-radio value="Sponsor">有</el-radio>
              <el-radio value="Venue">无</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="说明书">
            <el-radio-group v-model="ruleForm.resource2">
              <el-radio value="Sponsor">有</el-radio>
              <el-radio value="Venue">无</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注" prop="longitude">
            <el-input type="textarea" :rows="4" maxlength="30" v-model="ruleForm.longitude" />
          </el-form-item>

        </el-form>
        <el-form
          ref="customFieldsRef"
          style="max-width: 600px"
          :model="customFieldValues"
          label-width="110px"
          class="demo-ruleForm"
          :size="formSize"
          status-icon
        >
<!--          <el-form-item-->
<!--            v-for="itm, in customFields"
                :key="itm.customCode"-->
<!--            :label="`${itm.customName}`"-->
<!--            :rules="generateRules(itm)"-->
<!--            :prop="`${itm.customCode}`">-->
<!--            <el-input  v-model="customFieldValues[itm.customCode]"-->
<!--                       :placeholder="itm.regularMessage || `请输入${itm.customName}`"/>-->
<!--          </el-form-item>-->
        </el-form>
      </div>

    </div>
  </div>
</template>

<script setup>
import { reactive,toRefs,onBeforeMount,onMounted,watch,ref} from 'vue'
import { Search ,CirclePlus,Edit,Delete, ArrowLeft} from '@element-plus/icons-vue'
import {useRouter,useRoute} from "vue-router";
import {ElMessage} from "element-plus";
// import {EditUpdate, getDetails, getTypedropdown} from "@/api/AssetList/index";
const { t } = useI18n() // 国际化
const data = reactive({})
let  rules=reactive({
  // name: [
  //   { required: true, message: '请输入资产名称', trigger: 'blur' },
  // ],
  // projectName: [
  //   { required: true, message: '请输入项目名称', trigger: 'blur' },
  // ],
  // typeName: [
  //   { required: true, message: '请选中类型名称', trigger: 'blur' },
  // ],
  // areaName: [
  //   { required: true, message: '请输入位置', trigger: 'blur' },
  // ],
  // address: [
  //   { required: true, message: '请输入详细位置', trigger: 'blur' },
  // ],
  // latitude: [
  //   { required: true, message: '请输入纬度', trigger: 'blur' },
  // ],
  // longitude: [
  //   { required: true, message: '请输入经度', trigger: 'blur' },
  // ],
})
let ruleFormRef = ref()
let TypeList = ref([])
let customFields = ref([])
let customFieldsList = ref([])
let customFieldValues = ref({})
let ruleForm=ref({})
const router = useRouter();
const routers = useRoute();
//返回
const goback = () =>{
  router.go(-1)
}

let customFieldsRef = ref()
//保存按钮
async function onSave(){
  // let parms={assertInfo:ruleForm.value ,customFields:customFieldsList.value}
  // let {data:res}=await EditUpdate(parms)
  // if (res.code==200){
  //   ElMessage({
  //     type: 'success',
  //     message: '保存成功'
  //   });
  //   router.go(-1)
  // }else{
  //   ElMessage(res.message)
  // }
}

//表单验证
const validateForms = async () => {
  try {
    const [form1Valid, form2Valid] = await Promise.all([
      ruleFormRef.value.validate().catch(() => false),
      customFieldsRef.value.validate().catch(() => false)
    ])

    if (form1Valid && form2Valid) {
      console.log('表单验证通过',customFieldValues.value,ruleForm.value)
      if (customFieldValues.value){
        customFieldsList.value=Object.keys(customFieldValues.value).map(key => ({
          customCode: key,
          customValue: customFieldValues.value[key]
        }))
      }

      onSave()
      // 执行提交逻辑
    } else {
      console.log('表单验证失败')
      console.log('dww1d1d11',customFieldValues.value)
    }
  } catch (error) {
    console.log('验证过程出错:', error)
  }
}


//获取详情
// async function getdetails(value){
//   let {data:res}=await getDetails(value)
//   if (res.code==200){
//     ruleForm.value=res.data.assertInfo
//     customFields.value=res.data.customFields || []
//     // customFieldValues.value = JSON.parse(JSON.stringify(res.data.customFields)) || []
//     const values = {}
//     res.data.customFields.forEach(field => {
//       values[field.customCode] = field.customValue || ''
//       // values[field.customCode] =''
//     })
//     customFieldValues.value = values
//   }else{
//     ElMessage(res.message)
//   }
// }

// 生成验证规则
const generateRules = (field) => {
  const rules = []
  // 必填验证
  if (field.required === '1') {
    rules.push({
      required: true,
      message: field.regularMessage || `请输入${field.customName}`,
      trigger: ['blur', 'change']
    })
  }

  // 字段长度验证
  if (field.fieldLength) {
    rules.push({
      max: field.fieldLength,
      message: `长度不能超过${field.fieldLength}个字符`,
      trigger: ['blur', 'change']
    })
  }


  // 正则表达式验证
  if (field.regularExpression) {
    try {
      // 移除前后的斜杠
      const regexStr = field.regularExpression.replace(/^\/|\/$/g, '')

      // 将字符串中的 \d 替换为实际的数字匹配模式
      const processedRegex = regexStr.replace(/\\d/g, '\\d')
      // 创建正则表达式对象
      const regex = new RegExp(processedRegex)

      rules.push({
        validator: (rule, value, callback) => {
          // 调试信息
          // console.log('原始正则:', field.regularExpression)
          // console.log('处理后的正则:', regex)
          // console.log('验证值:', value)
          // console.log('验证结果:', regex.test(value))
          if (!value) {
            callback()
            return
          }

          if (regex.test(value)) {
            callback()
          } else {
            callback(new Error(field.regularMessage || '格式不正确'))
          }
        },
        trigger: ['blur', 'change']
      })
    } catch (error) {
      console.error('正则表达式错误:', error)
    }
  }
  return rules

}

//重置
function onReset() {
  ruleForm.value={}
  customFieldValues.value={}
}



//获取资产类型
// async function  getAssetType(){
//   let {data:res}=await getTypedropdown()
//   if (res.code==200){
//     TypeList.value=res.data
//   }else{
//     ElMessage(res.message)
//   }
// }


// watch(
//   () => data,
//   () => {
// // console.log('watch');
//   },
//   {deep: true,immediate: true,}
// );
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  // routers.query.id && getdetails( routers.query.id)
  // getAssetType()
})
</script>
<style lang="scss" scoped>
.box{
  //background: #F5F6F8;
  //position: relative;
  //height: 75vh;     // 设置容器高度为视口高度
  header{
    // width: 1616px;
    //position: sticky;
    //top: 10px;
    //left: 10px;
    //right: 0;
    //z-index: 100;
    height: 48px;
    background: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 16px;
    margin-bottom: 16px;
    .itmdiv{
      margin-left: 10px;
    }
    .elbutten{
      width: 80px;
      height: 32px;
      background: #EAEDF2;
      border-radius: 4px 4px 4px 4px;
      border: 0;
      font-size: 14px;
      color: #323336;
      padding-left: 0;
      img{
        width: 24px;
        height: 24px;
      }
      span{
        display: inline-block;
        margin-top: 2px;
      }
    }
    .rightbutten{
      margin-right: 10px;
    }
    p{
      font-weight: 400;
      font-size: 14px;
      margin-left: 24px;
      span{
        &:first-child{
          color: #9C9DA2;
        }
      }
    }
  }
  .banner{
    width: 100%;
    display: flex;
    background: #FFFFFF;
    .editCenter{
      width: 100%;
      margin: 30px;
    }
  }
}
</style>
