<template>
  <div class="head-container">
    <el-input v-model="deptName" class="mb-20px" clearable placeholder="搜索类型">
      <template #prefix>
        <Icon icon="ep:search" />
      </template>
    </el-input>
  </div>
  <div class="head-container">
    <el-tree
      ref="treeRef"
      :data="deptList"
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
      :props="defaultProps"
      default-expand-all
      highlight-current
      :current-node-key="defaultSelectedKey"
      node-key="id"
      @node-click="handleNodeClick"
    >
      <template #default="{ node, }">
        <span class="custom-tree-node">
          <Icon :icon="node.expanded ? 'ep:folder-opened' : 'ep:folder'" class="mr-4px" />
          <span>{{ node.label }}</span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script lang="ts" setup>
import { ElTree } from 'element-plus'
import * as DeptApi from '@/api/system/dept'
import { defaultProps, handleTree } from '@/utils/tree'
import {defineProps, nextTick} from "vue";
import {getdepartmentList} from "@/api/system/dept";
const props=defineProps({

  // typeId: {
  //   type: [String, Number],
  //   required: true
  // },
  // Id: {
  //   type: [String, Number],
  //   required: true
  // },
  // activeTab: {
  //   type: [String, Number],
  //   required: true
  // } ,

})
defineOptions({ name: 'SystemUserDeptTree' })
// 添加默认选中的key
const defaultSelectedKey = ref('')
const deptName = ref('')
const deptList = ref<Tree[]>([]) // 树形结构
const treeRef = ref<InstanceType<typeof ElTree>>()

/** 获得部门树 */
const getTree = async (id?: Number ) => {

  // if (props.typeId && props.Id) {
  //   const res = await DeptApi.getdepartmentList(props.typeId)
  //   deptList.value = []
  //   deptList.value.push(...handleTree(res))
  //
  //
  //   // 设置默认选中第一个节点
  //   if (deptList.value.length > 0) {
  //     defaultSelectedKey.value = props.Id
  //     // 触发第一个节点的点击事件
  //     handleNodeClick(props.Id)
  //   }
  // }else {
  //   const res = await DeptApi.getdepartmentList(id)
  //   deptList.value = []
  //   deptList.value.push(...handleTree(res))
  //   if (deptList.value.length > 0) {
  //     defaultSelectedKey.value = deptList.value[0].id
  //     // 触发第一个节点的点击事件
  //     handleNodeClick(deptList.value[0])
  //   }
  // }





}

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}

/** 处理部门被点击 */
const handleNodeClick = async (row: { [key: string]: any }) => {
  emits('node-click', row)
}
const emits = defineEmits(['node-click'])

/** 监听deptName */
watch(deptName, (val) => {
  treeRef.value!.filter(val)
})

// 添加对 typeId 的监听
// watch(() => props.typeId, async (newVal) => {
//   if (newVal) {
//     await getTree()
//   }
// }, { immediate: true })

/** 监听标签页变化并更新列表 */
// watch(
//   () => props.activeTab,
//   (newVal) => {
//     // 使用 nextTick 等待 DOM 更新完成
//     nextTick(async () => {
//       if (newVal == '0') {
//         await getTree(null)
//         // queryParams.type = 1
//       } else {
//         await getTree(newVal)
//         console.log('收到其他值:', typeof newVal) //
//         // queryParams.type = 0
//       }
//
//     })
//   },
//   // { immediate: true }
// )

/** 初始化 */
onMounted(async () => {
  await getTree()
})
</script>
