<template>

  <div class="mb-4">
<!--    <el-button type="primary" plain>全部申请: 112</el-button>-->
    <el-button type="warning" plain>处理中：22</el-button>
<!--    <el-button type="success" plain>报废通过：35</el-button>-->
<!--    <el-button type="danger" plain>报销驳回：35</el-button>-->
  </div>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="flex flex-wrap items-start -mb-15px"
    >
      <el-form-item label="" prop="name">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="输入申请编号/备件名称/规格型号"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="备件类型" prop="status" class="!mr-3">
        <el-select v-model="queryParams.status" class="!w-140px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请人员" prop="status" class="!mr-3">
        <el-select v-model="queryParams.status" class="!w-140px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请类型" prop="status" class="!mr-3">
        <el-select v-model="queryParams.status" class="!w-140px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
        <div>
          <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:device-overview-info:query']" gradient @click="handleQuery" />
          <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
        </div>
        <div>
          <el-button plain v-hasPermi="['infra:device-overview-info:export']" @click="handleExport" :loading="exportLoading">
            <Icon icon="ep:download" />导出
          </el-button>
        </div>
        </div>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table ref="myTable" v-loading="loading" @selection-change="selectionChange" :data="list">
      <el-table-column align="left" type="selection"   width="55" />
      <el-table-column type="index" label="序号" width="55" align="left">
        <template #default="scope">
          <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column align="left" label="申请编号" prop="code" />
      <el-table-column align="left" label="备件名称" prop="name" />
      <el-table-column align="left" label="备件类型" prop="name" />
      <el-table-column align="left" label="品牌" prop="name" />
      <el-table-column align="left" label="规格型号" prop="name" />
      <el-table-column align="left" label="申请类型" prop="name" />
      <el-table-column align="left" label="申请数量" prop="name" />
      <el-table-column align="left" label="单位" prop="name" />
      <el-table-column align="left" label="申请时间" prop="name" />
      <el-table-column align="left" label="申请人" prop="name" />
      <el-table-column align="left" label="状态" prop="name" />

      <el-table-column :width="180" align="left" label="操作">
        <template #default="scope">
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openDetails(scope.row)"
          >
            详情
          </el-button>
<!--          <el-button-->
<!--            link-->
<!--            preIcon="ep:basketball"-->
<!--            title="功能权限"-->
<!--            type="primary"-->
<!--          >-->
<!--            取消-->
<!--          </el-button>-->
<!--          <el-button-->
<!--            link-->
<!--            preIcon="ep:basketball"-->
<!--            title="功能权限"-->
<!--            type="primary"-->
<!--            @click="onApply"-->
<!--          >-->
<!--            重新申请-->
<!--          </el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <!-- 审批弹窗 -->
  <Dialog v-model="dialogVisible" width="1000" title="审核">
    <div class="examine">
      <div>申请编号: <span>xxxxxx</span></div>
      <div>备件名称: <span>xxxxxx</span></div>
      <div>申请类型: <span>出库</span></div>
      <div>申请数量: <span>1</span></div>
      <div>申请时间: <span>2025/01/01 11:00:00</span></div>
      <div>申请时间: <span>2025/01/01 11:00:00</span></div>
      <div>申请人: <span style="margin-left: 52px;">研发部-张三</span></div>
      <div>状态: <span style="margin-left: 65px;">待审核</span></div>
    </div>

    <div>
      <el-form :model="examineForm" label-width="auto" style="margin-top: 30px;">

        <el-form-item label="审核结果">
          <el-select v-model="examineForm.region" placeholder="请选择" >
            <el-option label="通过" value="shanghai" />
            <el-option label="驳回" value="beijing" />
          </el-select>
        </el-form-item>
        <el-form-item label="审核说明">
          <el-input v-model="examineForm.name" :rows="4" show-word-limit maxlength="30" type="textarea" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <el-button  style="width: 200px;" type="primary" >确定</el-button>
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>

  <Dialog v-model="applydialogVisible" width="1000" title="重新申请">
    <div class="dialog-content">
      <div class="dialog-details"  >
        <div class="dialog-title">
          <div>备件名称:<span>xxxx备件</span></div>
          <div>品牌:<span>摄像头-球机</span></div>
          <div>规格型号:<span>xxxxx型号</span></div>
          <div>单位:<span>台</span></div>
        </div>
        <div class="dialog-quantity">
          <div  class="quantityLeft">
            <div style="margin: 10px;">当前可用库存: <span style="margin-left: 10px;" >3</span></div>
            <div style="margin: 10px;">
              <el-form
                ref="ruleFormRef"
                style="max-width: 600px"
                :model="ruleForm"
                label-width="auto"
                class="demo-ruleForm"
                status-icon
              >
                <el-form-item label="入库数量" prop="name">
                  <el-input v-model="ruleForm.name" />
                </el-form-item>
                <el-form-item label="入库说明" prop="name">
                  <el-input type="textarea" :rows="2" v-model="ruleForm.name" />
                </el-form-item>

              </el-form>
            </div>
          </div>
          <div  class="quantityRight">
            <div style="margin: 10px;">
              <el-form
                ref="ruleFormRef"
                style="max-width: 600px"
                :model="ruleForm"
                label-width="auto"
                class="demo-ruleForm"
                status-icon
              >

                <el-form-item label="所属项目" prop="name">
                  <el-select v-model="ruleForm.region" placeholder="please select your zone">
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </el-form-item>

                <el-form-item label="附件" prop="name">
                  <el-upload
                    ref="upload"
                    class="upload-demo"
                    style="display: flex;"
                    action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
                    :limit="1"
                    :on-exceed="handleExceed"
                    :auto-upload="false"
                  >
                    <template #trigger>
                      <el-button type="primary">点击上传</el-button>
                    </template>
                  </el-upload>
                </el-form-item>

              </el-form>
            </div>

          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <el-button  style="width: 200px;" type="primary" >确定</el-button>
        <el-button style="width: 200px;" @click="applydialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { useRouter } from "vue-router";
import * as RoleApi from '@/api/system/role'

import {modifyStatus} from "@/api/system/role";

defineOptions({ name: 'PluginList' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // vue-router
const loading = ref(true) // 列表的加载中
const applydialogVisible = ref(false) // 列表的加载中
const dialogVisible = ref(false) //  关联设备弹窗的显示隐藏
const total = ref(0) // 列表的总页数
const examineForm = ref({}) // 列表的总页数
const list = ref([]) // 列表的数据
const deviceslist = ref([{code:'ssssss11',name:'杆件'}]) // 列表的数据
const ruleForm = ref([{code:'ssssss11',name:'杆件'}]) // 列表的数据
const selectionList = ref([]) // 列表选中的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined,
  createTime: []
})
const devicesParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中


/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 新增按钮操作 */
const openAdd = () => {
  router.push('AccessoriesAdd')
}

/** 详情按钮操作 */
const openDetails = (value:number) => {
  router.push('InventoryTodoDetails')
}

/** 审批按钮 */
const oneXamine = () => {
  dialogVisible.value = true
}
/** 重新申请按钮 */
const onApply = () => {
  applydialogVisible.value = true
}

/** 表格选中数据 */
const selectionChange = (value) => {
  selectionList.value = value
  console.log('选中数据',selectionList.value)
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}


const myTable=ref()
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  if (selectionList.value.length <1) {
    message.warning('请选择要删除的角色！')
  }else {
    const ids = selectionList.value.map((item) => item.id).join(',')
    try {
      // 删除的二次确认
      await message.delConfirm()
      // 发起删除
      // await RoleApi.deleteRole(ids)
      message.success(t('common.delSuccess'))
      // 刷新列表
      // await getList()
    } catch {
      myTable.value.clearSelection();
    }
  }
}



//单个状态修改
const SingleState = async (type: string,row:any) => {
  console.log('sssss',row)
  let parms={...row,status:type== 'Disable'? '1' : '0'}
  try {
    // 操作的二次确认
    await message.confirm('你确定要操作该角色状态吗？')
    // 发起删除
    await modifyStatus([ parms])
    message.success('操作成功！')
    // 刷新列表
    await getList()
  } catch {

  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // const data = await PostApi.getList(deptName.value)
    // console.log('排班管理',data)
    // list.value = data
    // total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await RoleApi.exportRole(queryParams)
    download.excel(data, '角色列表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
<style scoped>
.examine{
  padding: 20px;
  background: rgb(242 242 242);
  div{
    margin-top: 15px;
    span{
      margin-left: 40px;
    }
  }
}
.dialog-content{
  max-height: 500px;
  overflow: auto;
  .dialog-details{
    margin-top: 10px;
    height: 250px;
    border: 1px solid #DCDCDC;
    .dialog-title{
      display: flex;

      align-items: center;
      background: #f3f7fe;
      margin: 15px;
      padding: 10px 0;
      div{
        margin-left: 15px;
      }
      span{
        margin-left: 20px;
      }
    }
    .dialog-quantity{
      margin: 15px;
      display: flex;
      .quantityLeft{
        width: 300px;
        background: #f3f7fe;
        height: 167px;

      }
      .quantityRight{
        width: 680px;
        margin-left: 10px;
        background: #f3f7fe;
        height: 167px;
      }
    }
  }
}

</style>
