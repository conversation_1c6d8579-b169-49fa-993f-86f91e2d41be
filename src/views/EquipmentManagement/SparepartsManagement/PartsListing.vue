<template>

  <div class="mb-4">
    <el-button type="primary" plain>备件库存: 112</el-button>
    <el-button type="success" plain>入库：35</el-button>
    <el-button type="warning" plain>出库：35</el-button>
  </div>

  <el-row :gutter="20">
    <!-- 左侧部门树 -->
    <el-col :span="4" :xs="24">
      <ContentWrap class="h-1/1">
        <DeptTree  @node-click="handleDeptNodeClick" />
      </ContentWrap>
    </el-col>
    <el-col :span="20" :xs="24">
      <!-- 搜索 -->
      <ContentWrap>
        <el-form
          class="flex flex-wrap items-start -mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
        >
          <el-form-item label="" prop="username" class="!mr-3">
            <el-input
              v-model="queryParams.username"
              placeholder="输入备件名称/规格型号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="供应商" prop="mobile" class="!mr-3">
            <el-select
              v-model="queryParams.deptId"
              placeholder="请选择"
              clearable
              class="!w-240px"
            >
              <el-option
                v-for="dict in dataUnit"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
          <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
            <div>
              <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:device-overview-info:query']" gradient @click="handleQuery" />
              <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
            </div>
            <div>
              <el-button plain v-hasPermi="['infra:device-overview-info:create']" @click="addition('create')">
                <Icon icon="ep:plus" />
                新增
              </el-button>
              <el-button
                plain
                @click="openForm('store')"
              >
                入库
              </el-button>
              <el-button
                plain
                @click="openForm('Outbound')"
              >
                出库
              </el-button>
              <el-button  v-hasPermi="['infra:device-overview-info:import']" plain> <Icon icon="ep:upload" /> 导入 </el-button>
              <el-button plain v-hasPermi="['infra:device-overview-info:export']" @click="handleExport" :loading="exportLoading">
                <Icon icon="ep:download" />导出</el-button>
            </div>
          </div>



        </el-form>
      </ContentWrap>
      <ContentWrap>
        <el-table  ref="tableRef" @selection-change="checkList"  v-loading="loading" :data="list">
          <el-table-column label="序号" type="selection" align="left" width="55" />

          <el-table-column label="序号" type="index" align="left" width="55" />
          <template v-for="dict in columnList" :key="dict.key">
            <el-table-column
              v-if="dict.key == 'operate'"
              :label="dict.label"
              align="left"
              width="160"
              :show-overflow-tooltip="true"
            >
              <template  #default="scope">
                  <el-button type="primary" style="width: 30px;" @click="openDetails(scope.row.id)" text>详情</el-button>
                  <el-button type="primary" style="width: 30px;" @click="addition()" text>编辑</el-button>
                  <el-button type="primary" style="width: 30px;" text>删除</el-button>
              </template>
            </el-table-column>
          <el-table-column
            v-else
            :label="dict.label"
            align="left"
            :prop="dict.key"
            :show-overflow-tooltip="true"
          />
          </template>
        </el-table>

        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>

    <!--    入库出库-->
    <SparepartsStorage ref="formRef" />
<!--    <AdvancedSearch ref="formRef"/>-->
<!--&lt;!&ndash;    配置二维码&ndash;&gt;-->
<!--    <ConfigureQRcode ref="QRcodeRef" />-->


  </el-row>

</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { checkPermi } from '@/utils/permission'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { CommonStatusEnum } from '@/utils/constants'
import {
  ref,
  reactive,
  onMounted,
  defineProps,
  nextTick
} from 'vue'
import * as UserApi from '@/api/system/user'
import { useRouter } from "vue-router";
import DeptTree from './components/DeptTree.vue'
import SparepartsStorage from './components/SparepartsStorage.vue'

import * as PostApi from "@/api/system/post";
const route = useRouter()
const roleListLoaded = ref(false)
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
let dataUnit=ref([])
let selectionList=ref([])
let dialogTitle=ref('')
let dialogVisible=ref(false)
let postList=ref([])
let columnList=ref([{label:'备件名称',key:'sb'},{label:'备件类型',key:'mc'},{label:'供应商',key:'lx'},
  {label:'规格型号',key:'qy'},{label:'库存数量',key:'gys'},{label:'可用库存',key:'gys1'},{label:'入库数量',key:'gys2'},
  {label:'出库数量',key:'gys3'},{label:'最新入库时间',key:'gys4'},{label:'最新出库时间',key:'gys5'},
  {label:'操作',key:'operate'}
] )  // 列的集合
let statusList=ref([{
  label: '启用',
  value: '0'
}, {
  label: '禁用',
  value: '1'
}])
const loading = ref(true) // 列表的加载中
const total = ref(10) // 列表的总页数
const list = ref([]) // 列表的数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orgAccessStatus:'1',
  username: undefined,
  mobile: undefined,
  status: undefined,
  deptId: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  // try {
  //   const data = await UserApi.getUserPage(queryParams)
  //   list.value = data.list
  //   total.value = data.total
  // } finally {
    loading.value = false
  // }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
/** 新增/编辑按钮 */
const addSite = (value:string) => {
  console.log(value)
  route.push('SiteAddition')

}
/** 重置密码弹窗 */
const openPop = (value) => {
  if (value=='create'){
    dialogVisible.value=true
    dialogTitle.value='用户添加成功'
  }
  getList()
}

let tableRef= ref()
/** 启用禁用按钮 */
const DisableEnable = async (type:string) => {
  if (selectionList.value.length<1){
    message.warning('请选择要操作的数据')
    return
  }
  try {
    // 修改状态的二次确认
    const text = type == '0' ? '启用' : '停用'
    await message.confirm('确认要"' + text + '""' + '这' + selectionList.value.length + '个用户吗?')
    // 发起修改状态
    const ids = selectionList.value.map((item) => item.id)
    await UserApi.updateUserStatus([...ids], type)
    // 刷新列表
    await getList()
  } catch {
    tableRef.value?.clearSelection()
    // 取消后，进行恢复按钮

  }

}
/** 表格选中数据 */
const checkList = (value:any) => {
  selectionList.value=value

}






/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.roleId=''
  handleQuery()
}

/** 处理部门被点击 */
const handleDeptNodeClick = async (row) => {
  queryParams.deptId = row.id
  await getList()
}

// /** 入库出库 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}



/** 修改用户状态 */
const handleStatusChange = async (row: UserApi.UserVO) => {
  try {
    // 修改状态的二次确认
    const text = row.status === CommonStatusEnum.ENABLE ? '启用' : '停用'
    await message.confirm('确认要"' + text + '""' + row.username + '"用户吗?')
    // 发起修改状态
    await UserApi.updateUserStatus([row.id], row.status)
    // 刷新列表
    await getList()
  } catch {
    // 取消后，进行恢复按钮
    row.status =
      row.status === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE : CommonStatusEnum.ENABLE
  }
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await UserApi.exportUser(queryParams)
    download.excel(data, '用户数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 操作分发 */
const handleCommand = (command: string, row: UserApi.UserVO) => {
  switch (command) {
    case 'handleDelete':
      handleDelete(row.id)
      break
    case 'handleResetPwd':
      handleResetPwd(row)
      break
    case 'handleRole':
      handleRole(row)
      break
    default:
      break
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await UserApi.deleteUser(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 重置密码 */
const handleResetPwd = async (row: UserApi.UserVO) => {
  try {
    // 重置的二次确认
    const result = await message.prompt(
      '请输入"' + row.username + '"的新密码',
      t('common.reminder')
    )
    const password = result.value
    // 发起重置
    await UserApi.resetUserPwd(row.id, password)
    message.success('修改成功，新密码是：' + password)
  } catch {}
}

/** 分配角色 */
const assignRoleFormRef = ref()
const handleRole = (row: UserApi.UserVO) => {
  assignRoleFormRef.value.open(row)
}

// 获取角色列表的方法
const getRoleList = async () => {
  if (roleListLoaded.value) return // 如果已经加载过，直接返回
  try {
    postList.value = await PostApi.getRoleList()
    roleListLoaded.value = true
  } catch (error) {
    console.error('获取角色列表失败:', error)
  }
}

/** 初始化 */
onMounted(async () => {
  // console.log('初始化',props.activeTab)
  getList()
  // dataUnit.value = await DeptApi.getDeptPage(queryParams)
  // getRoleList()
  // postList.value = await PostApi.getRoleList()
  // console.log('sssssdsdsadasdasdas',postList.value)
})




/** 配置二维码*/
const QRcodeRef= ref()
const Configure = () => {
  QRcodeRef.value.open()
}

//新增编辑按钮
const addspareParts = () => {
  // QRcodeRef.value.open()
  route.push('sparePartsAdd')
}
//详情按钮
const openDetails = (value:number) => {
  // QRcodeRef.value.open()
  route.push('sparePartsDetails')
}
//处理站点设备
const getGysList = (row) => {
  return row?.gys ? row.gys.split(',') : []
}


</script>
<style scoped>
.TableDevice{
  display: flex;
  align-items: center;
  min-width: 300px;
  overflow-x: auto;
  padding: 4px 0;
}
.TableDevice::-webkit-scrollbar {
  height: 6px;
}

.TableDevice::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.TableDevice::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.TableDevice::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
.mb-4{
  padding:10px;
  background: #ffffff;
}
</style>
