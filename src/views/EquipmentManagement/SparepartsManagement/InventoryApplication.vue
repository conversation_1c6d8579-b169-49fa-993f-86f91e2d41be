<template>
  <div>
    <ContentWrap>
      <el-tabs v-model="activeName" class="demo-tabs" >
        <el-tab-pane label="待处理" name="first">
          <inventoryTodo/>
        </el-tab-pane>
        <el-tab-pane label="处理中" name="second">
          <inventoryProcessing/>
        </el-tab-pane>
        <el-tab-pane label="已完结" name="Completed">
          <inventoryCompleted/>
        </el-tab-pane>
      </el-tabs>
    </ContentWrap>
  </div>
</template>
<script lang="ts" setup>
import inventoryTodo from './components/inventoryTodo.vue';
import inventoryProcessing from './components/inventoryProcessing.vue';
import inventoryCompleted from './components/inventoryCompleted.vue';

defineOptions({ name: 'InventoryApplication' })

const activeName = ref('first') // 列表的总页数


/** 初始化 **/
onMounted(() => {

})
</script>
<style scoped>


</style>
