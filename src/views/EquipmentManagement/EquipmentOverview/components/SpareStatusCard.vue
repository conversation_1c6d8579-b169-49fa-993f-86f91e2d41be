<template>
  <div class="flex flex-col justify-around h-[330px] py-6">
    <div
      v-for="(item, index) in list"
      :key="index"
      @click="select(index)"
      :class="[
        'cursor-pointer flex items-center p-2  rounded-md border border-solid relative',
        index === selectedIndex
          ? 'selected-card border-[#7AABFF] bg-[#F5F9FF]'
          : 'border-[#ECEDEE] bg-white'
      ]"
    >
      <div class="w-12 h-12 flex items-center justify-center rounded">
        <img :src="item.icon" class="size-[32px]" />
      </div>
      <div class="ml-1 flex flex-col">
        <span :class="['text-sm', 'text-[#545658]']">
          {{ item.name }}
        </span>
        <span
          :class="[
            'text-xl font-bold',
            index === selectedIndex ? 'text-[#0053DF]' : 'text-[#545658]'
          ]"
        >
          {{ item.num }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue'

interface SpareItem {
  name: string
  num: number
  icon: string
}

const props = defineProps<{
  list: SpareItem[]
  selected?: number
}>()

const emit = defineEmits(['update:selected'])

const selectedIndex = ref(props.selected ?? 0)

watch(
  () => props.selected,
  (val) => {
    if (val !== undefined) selectedIndex.value = val
  }
)

const select = (index: number) => {
  selectedIndex.value = index
  emit('update:selected', index)
}
</script>
<style scoped>
.selected-card::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -6px; /* 可调，控制三角离容器距离 */
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-left: 6px solid #7aabff; /* 蓝色三角 */
}
</style>
