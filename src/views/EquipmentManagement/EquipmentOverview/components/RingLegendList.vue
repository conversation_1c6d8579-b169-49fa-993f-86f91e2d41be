<template>
  <div class="w-full text-[14px] text-[#333] pl-2">
    <div class="flex justify-between px-1 text-[#999] font-medium mb-[20px] pr-[15px]">
      <span>{{ name }}</span>
      <span>数量</span>
      <span>占比</span>
    </div>
    <div
      v-for="(item, index) in data"
      :key="index"
      class="flex justify-between h-[44px] leading-[44px] rounded-l-[4px] border border-solid border-[#ECEDEE] mb-8 ml-[-20px] mr-[-9px] pl-[20px] pr-[15px]"
    >
      <!-- 类型 + 色块 -->
      <div class="flex items-center relative">
        <span
          class="w-2 h-2 rounded-[2px] absolute left-[-16px]"
          :style="{ backgroundColor: colorGroup[index % colorGroup.length] }"
        ></span>
        <span>{{ item.key }}</span>
      </div>
      <!-- 数量 -->
      <div>{{ item.value }}</div>
      <!-- 占比 -->
      <div>{{ item.point }}%</div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface RingItem {
  key: string
  value: number
  point: number
}

defineProps<{
  data: RingItem[]
  colorGroup: string[]
  name: string
}>()
</script>

<style scoped></style>
