<template>
  <div ref="chartRef" class="w-full h-[300px]"></div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts/core'
import { <PERSON><PERSON>hart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import type { ECharts, EChartsOption } from 'echarts'

echarts.use([
  BarChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  CanvasRenderer
])

interface SupplierData {
  name: string
  values: number[]
}

const props = defineProps<{
  categories: string[] // e.g. ['设备类型1', '设备类型2', ...]
  data: SupplierData[] // e.g. [{ name: '供应商1', values: [1000, 2000, ...] }, ...]
}>()

const chartRef = ref<HTMLDivElement | null>(null)
let chartInstance: ECharts | null = null
const colors = ['#FFBF3E', '#FF577F', '#4394FF', '#41D6BD']

const initChart = () => {
  if (!chartRef.value) return
  chartInstance = echarts.init(chartRef.value)
  const backgroundData = props.data.map((item) => item.values.reduce((sum, val) => sum + val, 0))
  const maxStackValue = Math.max(...backgroundData)

  const baseBackground = {
    name: '背景',
    type: 'bar',
    barGap: '-80%', // 与主柱重叠
    barWidth: 15,
    itemStyle: {
      color: '#F1F7FF'
    },
    label: {
      show: true,
      position: 'right',
      formatter: (params: any) => backgroundData[params.dataIndex],
      color: '#333',
      fontWeight: 500,
      fontSize: 12,
      offset: [8, 0]
    },
    data: backgroundData.map(() => maxStackValue), // 背景长度统一
    silent: true, // 不响应事件
    tooltip: { show: false }, // 👈 不显示在 tooltip 中

    z: 1
  }
  const series = props.categories.map((type, index) => ({
    name: type,
    type: 'bar',
    stack: 'total',
    barWidth: 8,
    itemStyle: {
      color: colors[index % colors.length]
    },
    emphasis: { focus: 'series' },
    label: { show: false },
    data: props.data.map((item) => item.values[index])
  }))

  const option: EChartsOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    legend: {
      data: props.categories,
      itemWidth: 8,
      itemHeight: 8,
      icon: 'rect'
    },
    grid: {
      left: '3%',
      right: '100',
      bottom: '3%',
      top: '30',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      max: maxStackValue
    },
    yAxis: {
      axisLine: { show: false }, // 隐藏坐标轴轴线
      axisTick: { show: false }, // 隐藏刻度

      type: 'category',
      data: props.data.map((item) => item.name)
    },
    series: [baseBackground, ...series]
  }
  chartInstance.setOption(option)
}

onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      initChart()
    }, 100) // 或者 200ms，看布局复杂度
  })
  window.addEventListener('resize', resizeChart)
})

watch(
  () => props.data,
  () => {
    initChart()
  },
  { deep: true }
)

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onBeforeUnmount(() => {
  // 移除监听
  window.removeEventListener('resize', resizeChart)
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})
</script>

<style scoped>
/* 可根据需要自定义样式 */
</style>
