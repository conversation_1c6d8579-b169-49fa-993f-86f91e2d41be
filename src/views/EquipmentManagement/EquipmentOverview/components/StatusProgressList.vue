<template>
  <div class="space-y-8">
    <div
      v-for="(item, index) in list"
      :key="index"
      class="flex items-center justify-between bg-[#F7F9FB] rounded-md py-3 px-4"
    >
      <!-- 左侧文字 -->
      <div class="text-[#333] text-[14px] font-normal w-[60px] whitespace-nowrap">
        {{ item.name }}
      </div>

      <!-- 中间进度条 -->
      <div class="flex-1 mx-1 relative h-[20px] rounded-full bg-white overflow-hidden">
        <div
          class="h-full rounded-full flex items-center justify-center text-white text-[12px] font-medium"
          :style="{
            width: `${Math.round(item.point * 100)}%`,
            backgroundColor: item.color
          }"
        >
          {{ Math.round(item.point * 100) }}%
        </div>
      </div>

      <!-- 右侧数值 -->
      <div class="text-[#333] text-[14px] font-normal w-[10px] text-right">
        {{ item.num }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  list: {
    name: string
    point: number // 百分比(0~1)
    num: number
    color: string
  }[]
}>()
</script>

<style scoped></style>
