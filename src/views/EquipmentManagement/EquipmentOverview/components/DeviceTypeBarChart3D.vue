<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts/core'
import { GridComponent, TooltipComponent, DatasetComponent } from 'echarts/components'
import { CustomChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
import type { ECharts, EChartsOption, GraphicComponentOption } from 'echarts'

echarts.use([GridComponent, TooltipComponent, DatasetComponent, CustomChart, CanvasRenderer])

interface ChartData {
  key: string
  value: number
}

const props = defineProps<{
  data: ChartData[]
}>()

const chartRef = ref<HTMLDivElement | null>(null)
let chartInstance: ECharts | null = null

const renderChart = () => {
  if (!chartRef.value) return

  if (!chartInstance) {
    chartInstance = echarts.init(chartRef.value)
  }

  const categories = props.data.map((d) => d.key)

  const option: EChartsOption = {
    tooltip: {
      trigger: 'item'
    },
    grid: {
      left: 20,
      right: 40,
      bottom: 20,
      top: 40,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisTick: { show: false },
      axisLine: { lineStyle: { color: '#ccc' } },
      axisLabel: { color: '#333' }
    },
    yAxis: {
      type: 'value',
      name: '单位:个',
      nameTextStyle: {
        color: '#919AAA',
        padding: [0, 0, 0, -40] // 上右下左，左移 10 像素
      },
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { lineStyle: { color: '#eee' } },
      axisLabel: { color: '#919AAA' }
    },
    series: [
      {
        type: 'custom',
        renderItem: (params, api) => {
          const x = api.coord([api.value(0), 0])[0]
          const y = api.coord([0, api.value(1)])[1]
          const y0 = api.coord([0, 0])[1]

          const gradientFront = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#4DA0FF' },
            { offset: 1, color: '#0077FF' }
          ])

          const gradientRight = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#047AFF' },
            { offset: 1, color: '#0065D8' }
          ])

          const gradientTop = new echarts.graphic.LinearGradient(1, 0, 0, 1, [
            { offset: 0, color: '#68AFFF' },
            { offset: 1, color: '#5DA8FF' }
          ])

          const bgBarWidth = 20
          const barWidth = 10
          const offset = 4

          const xCenter = x + barWidth / 2
          const xBg = xCenter - bgBarWidth / 2

          const shapes: GraphicComponentOption[] = [
            // 背景柱
            {
              type: 'rect',
              shape: {
                x: xBg,
                y: 30,
                width: bgBarWidth + 4,
                height: y0 - 30
              },
              style: {
                fill: '#F1F7FF'
              },
              emphasis: {
                style: {
                  fill: '#F1F7FF' // same as normal style
                }
              }
            },
            // 正面
            {
              type: 'polygon',
              shape: {
                points: [
                  [x, y],
                  [x + barWidth, y],
                  [x + barWidth, y0],
                  [x, y0]
                ]
              },
              style: { fill: gradientFront }
            },
            // 右侧面
            {
              type: 'polygon',
              shape: {
                points: [
                  [x + barWidth, y],
                  [x + barWidth + offset, y - offset],
                  [x + barWidth + offset, y0 - offset],
                  [x + barWidth, y0]
                ]
              },
              style: { fill: gradientRight }
            },
            // 顶部
            {
              type: 'polygon',
              shape: {
                points: [
                  [x, y],
                  [x + offset, y - offset],
                  [x + barWidth + offset, y - offset],
                  [x + barWidth, y]
                ]
              },
              style: { fill: gradientTop }
            }
          ]

          return {
            type: 'group',
            children: shapes
          }
        },
        encode: {
          x: 0,
          y: 1
        },
        data: props.data.map((item) => [item.key, item.value])
      }
    ]
  }

  chartInstance.setOption(option)
}

onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      renderChart()
    }, 100) // 或者 200ms，看布局复杂度
    window.addEventListener('resize', resizeChart)
  })
})
watch(() => props.data, renderChart, { deep: true })

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onBeforeUnmount(() => {
  // 移除监听
  window.removeEventListener('resize', resizeChart)
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 330px;
}
</style>
