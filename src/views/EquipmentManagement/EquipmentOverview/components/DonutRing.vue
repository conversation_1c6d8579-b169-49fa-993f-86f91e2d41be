<template>
  <div ref="chartRef" class="w-full"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts/core'
import { TooltipComponent, LegendComponent, GraphicComponent } from 'echarts/components'
import { PieChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
import type { ECharts, EChartsOption } from 'echarts'

echarts.use([TooltipComponent, LegendComponent, PieChart, CanvasRenderer, GraphicComponent])

interface RingItem {
  key: string
  value: number
}

const props = defineProps<{
  data: RingItem[] // [{ key: '类型1', value: 100 }]
  total: number
  label: string // 如 “备件库存数”
  colorGroup: string[] // 环形颜色组
}>()

const hexToRGBA = (hex: string, alpha: number): string => {
  const r = parseInt(hex.slice(1, 3), 16)
  const g = parseInt(hex.slice(3, 5), 16)
  const b = parseInt(hex.slice(5, 7), 16)
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}

const chartRef = ref<HTMLDivElement | null>(null)
let chartInstance: ECharts | null = null

const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)

  const option: EChartsOption = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '背景环',
        type: 'pie',
        radius: ['55%', '70%'],
        silent: true,
        label: { show: false },
        labelLine: { show: false },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 2
        },
        data: props.data.map((item, i) => ({
          name: item.key,
          value: item.value,
          itemStyle: {
            color: hexToRGBA(props.colorGroup[i % props.colorGroup.length], 0.3)
          }
        }))
      },
      {
        name: props.label,
        type: 'pie',
        radius: ['70%', '90%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 2
        },
        data: props.data.map((item, i) => ({
          name: item.key,
          value: item.value,
          itemStyle: {
            color: props.colorGroup[i % props.colorGroup.length]
          }
        }))
      }
    ],
    graphic: [
      {
        type: 'group',
        left: 'center',
        top: 'center',
        children: [
          {
            type: 'text',
            left: 'center',
            top: '-20',
            style: {
              text: props.total.toString(),
              fontSize: 28,
              fontWeight: 'bold',
              fill: '#000'
            }
          },
          {
            type: 'text',
            left: 'center',
            top: '10',
            style: {
              text: props.label,
              fontSize: 14,
              fill: '#000'
            }
          }
        ]
      }
    ]
  }

  chartInstance.setOption(option)
}

onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      initChart()
    }, 100) // 或者 200ms，看布局复杂度
    window.addEventListener('resize', resizeChart)
  })
})
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onBeforeUnmount(() => {
  // 移除监听
  window.removeEventListener('resize', resizeChart)
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

watch(
  () => [props.data, props.total, props.label, props.colorGroup],
  () => {
    initChart()
  },
  { deep: true }
)
</script>

<style scoped></style>
