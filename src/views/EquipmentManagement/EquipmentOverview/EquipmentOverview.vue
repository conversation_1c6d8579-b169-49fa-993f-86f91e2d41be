<template>
  <div>
    <ContentWrap>
      <!-- 基础信息统计 -->
      <div class="title">设备基础信息统计</div>
      <el-row :gutter="16">
        <el-col :span="4" v-for="item in baseStats" :key="item.label">
          <div
            class="relative flex items-center pl-[14px] pr-2px py-[14px] rounded-lg"
            :style="{ background: item.bg }"
          >
            <!-- 左侧 icon -->
            <template v-if="item.icon">
              <img :src="item.icon" class="size-[56px] mr-1" />
            </template>
            <template v-else>
              <el-progress
                class="mr-2"
                type="circle"
                :percentage="item.point"
                :width="43"
                :stroke-width="5"
                :color="item.pointColor"
                stroke-linecap="round"
                :show-text="true"
              >
                <template #default="{ percentage }">
                  <div
                    class="size-[43px] leading-[35px] percent-inner flex justify-center items-center"
                    :style="{ border: `1px solid ${item.pointBg}` }"
                  >
                    <div
                      class="size-[35px] leading-[35px] percent-inner"
                      :style="{ background: item.pointBg }"
                    >
                      {{ percentage }}%
                    </div>
                  </div>
                </template>
              </el-progress>
            </template>
            <!-- 中间文字 -->
            <div class="z-2 pl-2">
              <div class="text-sm text-[#545658]">{{ item.label }}</div>
              <div class="text-xl font-black text-[#3B3C3D] text-center">{{ item.value }}</div>
            </div>

            <!-- 右侧背景图片（带透明度） -->
            <img
              v-if="item.img"
              :src="item.img"
              class="absolute size-[72px] right-2 bottom-2 pointer-events-none"
            />
          </div>
        </el-col>
      </el-row>
    </ContentWrap>

    <!-- 图表区域 -->
    <el-row :gutter="16">
      <!-- 设备类型统计 -->
      <el-col :span="14">
        <ContentWrap>
          <div class="title-bar">
            <div class="title">设备类型统计</div>
            <div
              class="detail-link"
              @click="handleViewDetail('设备类型统计', 'ManagementCenter/devices')"
              >查看详情 <Icon class="top-[3px]" icon="ep:arrow-right" />
            </div>
          </div>
          <div class="h-full w-full">
            <DeviceTypeBarChart3D :data="chartData" />
          </div>
        </ContentWrap>
      </el-col>

      <!-- 备件信息统计 -->
      <el-col :span="10">
        <ContentWrap>
          <div class="title-bar">
            <div class="title">备件信息统计</div>
            <div
              class="detail-link"
              @click="handleViewDetail('备件信息统计', 'SparepartsManagement/PartsListing')"
              >查看详情<Icon class="top-[3px]" icon="ep:arrow-right"
            /></div>
          </div>
          <div class="h-full w-full flex px-1">
            <div class="w-[30%] min-w-[145px]">
              <SpareStatusCard v-model:selected="selectedIndex" :list="spareAllData" />
            </div>
            <div class="w-[40%] relative">
              <DonutRing
                :data="pieDataA"
                :total="pieTotalA"
                :label="pieLabelA"
                :colorGroup="colorGroupA"
                class="h-[300px]"
              />
            </div>
            <div class="w-[30%] pl-2 min-w-[120px]">
              <RingLegendList :data="pieDataA" name="类型" :colorGroup="colorGroupA" />
            </div>
          </div>
        </ContentWrap>
      </el-col>
    </el-row>

    <el-row :gutter="16">
      <!-- 供应商信息统计 -->
      <el-col :span="14">
        <ContentWrap>
          <div class="title-bar">
            <div class="title">供应商信息统计</div>
            <div
              class="detail-link"
              @click="handleViewDetail('供应商信息统计', 'DeviceConfig/SupplierConfiguration')"
              >查看详情 <Icon class="top-[3px]" icon="ep:arrow-right"
            /></div>
          </div>
          <div class="h-full w-full">
            <SupplierBarChart :categories="categories" :data="supplierData" />
          </div>
        </ContentWrap>
      </el-col>

      <!-- 设备维修信息 -->
      <el-col :span="10">
        <ContentWrap>
          <div class="title-bar">
            <div class="title">设备维修情况</div>
            <div
              class="detail-link"
              @click="handleViewDetail('设备维修情况', 'maintenance/RepairList')"
              >查看详情<Icon class="top-[3px]" icon="ep:arrow-right"
            /></div>
          </div>
          <div class="h-full w-full flex">
            <div class="w-[35%] relative">
              <DonutRing
                :data="pieDataB"
                :total="674"
                label="设备维修总数"
                :colorGroup="colorGroupB"
                class="h-[300px]"
              />
            </div>
            <div
              class="w-[30%] min-w-[120px] pl-2 h-[300px] flex justify-center flex-row"
              :style="{ borderRight: 'solid 1px #F6F7F9' }"
            >
              <RingLegendList class="pr-2" :data="pieDataB" name="状态" :colorGroup="colorGroupB" />
            </div>
            <div class="w-[35%] pl-2 h-[300px]">
              <div class="mb-[20px]"
                ><span class="text-[14px] font-normal text-[#545658]">报废申请：</span
                ><span class="ml-2 text-[14px] font-semibold text-[#545658]">112</span></div
              >
              <StatusProgressList :list="progressList" />
            </div>
          </div>
        </ContentWrap>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import DeviceTypeBarChart3D from './components/DeviceTypeBarChart3D.vue'
import SupplierBarChart from './components/SupplierBarChart.vue'
import SpareStatusCard from './components/SpareStatusCard.vue'
import DonutRing from './components/DonutRing.vue'
import RingLegendList from './components/RingLegendList.vue'
import StatusProgressList from './components/StatusProgressList.vue'
import { formatDecimal } from '@/utils'

import * as deviceOverviewApi from '@/api/infra/deviceOverview'
import icon1 from './assets/icon-1.png'
import icon2 from './assets/icon-2.png'
import icon6 from './assets/icon-1.png'
import iconA from './assets/icon-a.png'
import iconB from './assets/icon-b.png'
import iconC from './assets/icon-c.png'

import img1 from './assets/img-1.png'
import img2 from './assets/img-2.png'
import img3 from './assets/img-3.png'
import img4 from './assets/img-4.png'
import img5 from './assets/img-5.png'
import img6 from './assets/img-6.png'
defineOptions({ name: 'EquipmentOverview' })

const chartData = ref([])
const categories = ref([])

const supplierData = ref([])
const selectedIndex = ref(0)

const baseStats = ref([
  { label: '设备总数', value: 0, bg: '#F3F8FF', icon: icon1, img: img1 },
  { label: '运营设备数', value: 900000, bg: '#F2FCFF', icon: icon2, img: img2 },
  {
    label: '在保设备数',
    value: 0,
    bg: '#F2FCF8',
    point: 0,
    pointColor: '#009467',
    pointBg: '#05c489',
    img: img3
  },
  {
    label: '临保设备数',
    value: 0,
    bg: '#FFF9F5',
    point: 0,
    pointColor: '#C15200',
    pointBg: '#ff6c00',
    img: img4
  },
  {
    label: '过保设备数',
    value: 0,
    bg: '#FFF5F6',
    point: 0,
    pointColor: '#BF0316',
    pointBg: '#f8283c',
    img: img5
  },
  { label: '备件库存数', value: 0, bg: '#F9F5FF', icon: icon6, img: img6 }
])
const spareAllData = ref([
  { name: '备件库存数', num: 0, icon: iconA },
  { name: '备件入库数', num: 0, icon: iconB },
  { name: '备件出库数', num: 0, icon: iconC }
])

const colorGroupA = ['#005FFF', '#00B9FF', '#35EDF3', '#BEF81C', '#FB8600', '#FFD21D']
const colorGroupB = ['#5ACE62', '#005FFF', '#FF7065']
const pieDataA = ref([])
const pieDataB = ref([
  { key: '维修中', value: 100, point: formatDecimal(20) },
  { key: '已维修', value: 100, point: formatDecimal(20) },
  { key: '已报废', value: 100, point: formatDecimal(20) }
])

const progressList = ref([
  { name: '待审批', point: 0.7, num: 50, color: '#6C63FF' },
  { name: '报废通过', point: 0.5, num: 100, color: '#00C27A' },
  { name: '报废驳回', point: 0.25, num: 100, color: '#FF7F30' }
])

const router = useRouter() // 路由对象
function handleViewDetail(section: string, path: string) {
  console.log(`查看详情: ${section},path:${path}`)
  router.push({ path })

  // TODO: 跳转或打开弹窗等逻辑
}

onMounted(() => {
  getData()
})

const getData = () => {
  getDeviceStatistics()
  getBasicCount()
  getSupplierCount()
  getSpareCount()
}

const getDeviceStatistics = async () => {
  const { deviceType } = await deviceOverviewApi.getDeviceStatistics()
  if (deviceType && Array.isArray(deviceType)) {
    chartData.value = deviceType.map((d) => ({ key: d.name, value: d.deviceTotal }))
  }
}
const spareData = ref(null)
const pieTotalA = ref(0)
const pieLabelA = ref('备件库存数')
watch(
  () => [spareData.value, selectedIndex.value],
  ([spareData, v]) => {
    console.log(spareData, v, 'spareData')
    if (spareData) {
      if (v === 2 && Array.isArray(spareData.outTypeCount)) {
        pieDataA.value = spareData.outTypeCount.map((d) => ({
          key: d.typeName,
          value: d.spareCount,
          point: formatDecimal(d.prop)
        }))
        pieTotalA.value = spareData.spareOutCount
        pieLabelA.value = '备件出库数'
      } else if (v === 1 && Array.isArray(spareData.inTypeCount)) {
        pieDataA.value = spareData.inTypeCount.map((d) => ({
          key: d.typeName,
          value: d.spareCount,
          point: formatDecimal(d.prop)
        }))
        pieTotalA.value = spareData.spareInCount
        pieLabelA.value = '备件入库数'
      } else if (v === 0 && Array.isArray(spareData.spareTypeCount)) {
        pieDataA.value = spareData.spareTypeCount.map((d) => ({
          key: d.typeName,
          value: d.spareCount,
          point: formatDecimal(d.prop)
        }))
        pieTotalA.value = spareData.spareCount
        pieLabelA.value = '备件库存数'
      }
    }
  },
  { immediate: true }
)
const getBasicCount = async () => {
  const data = await deviceOverviewApi.getBasicCount()
  if (data) {
    baseStats.value[0].value = data.allCount || 0
    baseStats.value[1].value = data.projectCount || 0
    baseStats.value[2].value = data.inWarrantyCount || 0
    baseStats.value[2].point = formatDecimal((data.inWarrantyCount * 100) / data.allCount) || 0
    baseStats.value[3].value = data.nearWarrantyCount || 0
    baseStats.value[3].point = formatDecimal((data.nearWarrantyCount * 100) / data.allCount) || 0
    baseStats.value[4].value = data.outWarrantyCount || 0
    baseStats.value[4].point = formatDecimal((data.outWarrantyCount * 100) / data.allCount) || 0
  }
}
const getSupplierCount = async () => {
  const { supplierCountList, typeList } = await deviceOverviewApi.getSupplierCount()
  if (typeList && Array.isArray(typeList)) {
    categories.value = typeList
  }
  if (supplierCountList && Array.isArray(supplierCountList)) {
    supplierData.value = supplierCountList.map((d) => ({ name: d.supplierName, values: d.count }))
  }
}

const getSpareCount = async () => {
  const data = await deviceOverviewApi.getSpareCount()
  spareData.value = data
  if (data) {
    baseStats.value[5].value = data.spareCount || 0

    spareAllData.value[0].num = data.spareCount || 0
    spareAllData.value[1].num = data.spareInCount || 0
    spareAllData.value[2].num = data.spareOutCount || 0
  }
}
</script>

<style lang="scss" scoped>
.title {
  position: relative;
  font-family: Microsoft YaHei;
  font-weight: bold;
  font-size: 16px;
  color: #3b3c3d;
  text-align: left;
  font-style: normal;
  text-transform: none;
  line-height: 24px;
  margin: 3px 0;
  padding-left: 20px;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 6px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #005fff;
    border: 2px solid #d4e4ff;
  }
}

.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.detail-link {
  position: relative;
  bottom: 2px;
  font-family:
    Microsoft YaHei,
    Microsoft YaHei;
  font-weight: 400;
  font-size: 12px;
  color: #6b727e;
  cursor: pointer;
}

.percent-inner {
  font-family:
    Microsoft YaHei,
    Microsoft YaHei;
  font-weight: bold;
  font-size: 10px;
  border-radius: 50%;
  color: #ffffff;
}
</style>
