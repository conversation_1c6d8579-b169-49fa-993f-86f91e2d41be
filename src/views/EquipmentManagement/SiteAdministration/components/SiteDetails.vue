<template>
  <div class="box">
    <header>
      <el-button type="info" :icon="ArrowLeft" @click="goback()" plain>返回</el-button>
      <div class="itmdiv">站点管理-详情</div>
      <div class="rightbutten">
      </div>
    </header>

    <div class="banner">
      <div class="left">
        <!-- top -->
        <div class="top">
          <p class="head">
            <span></span>
            <span>站点详情</span>
          </p>
          <div  class="MaintenanceRecord">
            <div v-for="(itm,index) in leftList" :key="index" style=" padding: 8px; ">
              <div class="BasicFields" style="background: #f2f2f2; padding: 8px;" v-if="dataList">
                <div class="txteStyle" style="width: 55%"><span class="keyStyle">{{itm.label+ ':'}}</span><div class="centerStyle">{{dataList[itm.key] }}</div></div>
              </div>
            </div>
          </div>
        </div>

        <div class="top" style="margin-top: 20px">
          <p class="head">
            <span></span>
            <span>站点设备</span>
          </p>
          <div>
            <el-table
              ref="myTable"
              :data="list"
              :max-height="400"
              style="width: 100%"
            >
              <el-table-column type="index" label="序号" width="55" align="left">
                <template #default="scope">
                  <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>
                </template>
              </el-table-column>
              <el-table-column align="left" label="设备编号" prop="assetsCode">
                <template #default="scope">
                  <el-button text link type="primary" @click="ToDetails(scope.row.id)">{{scope.row.assetsCode}}</el-button>
                </template>
              </el-table-column>
              <el-table-column align="left" label="设备名称" prop="assetsName" />
              <el-table-column align="left" label="设备类型" prop="assetsTypeName" />
              <el-table-column align="left" label="所属区域" prop="area" />
              <el-table-column align="left" label="供应商" prop="supplierName" />
              <el-table-column align="left" label="规格型号" prop="modelName" />
              <el-table-column align="left" label="质保状态" prop="warrantyStatus">
                <template #default="scope">
                  <span>{{scope.row.warrantyStatus=='1'? '已过保' :'质保中'}}</span>
                </template>
              </el-table-column>
              <el-table-column align="left" label="质保日期" prop="warrantyDate" />
              <el-table-column align="left" label="设备来源" prop="assetsSourceName" />
              <el-table-column align="left" label="标签" prop="label" />
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive,onBeforeMount,onMounted,ref} from 'vue'
import { ArrowLeft} from '@element-plus/icons-vue'
import {useRouter,useRoute} from "vue-router";
import {AdministrationDetails} from "@/api/EquipmentManagement/SiteAdministration";
import {getPropertyPage} from "@/api/EquipmentManagement/ManagementCenter";
const router = useRouter();
const routers = useRoute();
let id= ref('')
const activeName = ref('');
let Template =ref([]  )
let queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
})
const list = ref([]) // 列表的数据
const dataList = ref({
  siteName:'',
  longitude:'',
  areaName:'',
  deviceNumber:'',
})
const leftList = ref(   [
  {
    label: '站点名称',
    key: 'siteName',
  },
  {
    label: '经纬度',
    key: 'longitude',
  },
  {
    label: '所属区域',
    key: 'areaName',
  },
  {
    label: '设备数量',
    key: 'deviceNumber',
  },

])
//返回
const goback = () =>{
  router.go(-1)
}
//返回
const ToDetails = (id) =>{
  router.push({
    path:'ManagementCenter/DeviceDetails',
    query:{
      id
    }
  })
}

let customFieldsRef = ref()
onBeforeMount(() => {

})
onMounted(async () => {

  const data= await AdministrationDetails(routers.query.id)
  dataList.value.siteName=data.siteName
  dataList.value.areaName=data.areaName
  dataList.value.deviceNumber=data.deviceNumber
  dataList.value.longitude=data.longitude +','+data.latitude

  const res = await getPropertyPage({siteId :routers.query.id})
  list.value = res.list

})
</script>
<style lang="scss" scoped>
//.box{
  header{
    height: 48px;
    background: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 16px;
    margin-bottom: 16px;
    .itmdiv{
      margin-left: 10px;
    }
    .elbutten{
      width: 80px;
      height: 32px;
      background: #EAEDF2;
      border-radius: 4px 4px 4px 4px;
      border: 0;
      font-size: 14px;
      color: #323336;
      padding-left: 0;
      img{
        width: 24px;
        height: 24px;
      }
      span{
        display: inline-block;
        margin-top: 2px;
      }
    }
    .rightbutten{
      margin-right: 10px;
    }
    p{
      font-weight: 400;
      font-size: 14px;
      margin-left: 24px;
      span{
        &:first-child{
          color: #9C9DA2;
        }
      }
    }
  }
  .banner {

.top{
  background: #fff;
  border-radius: 4px;
  //height: calc(80% - 8px);
  display: flex;
  flex-direction: column;
  .txteStyle{
    display: flex;
  }
  .centerStyle{
    padding-left: 15px;
  }
}
    .head {
      padding: 16px;
      border-bottom: 1px solid #eaedf2;
      display: flex;
      align-items: center;
      gap: 8px;
      span {
        font-weight: bold;
        font-size: 16px;
        color: #323336;

        &:first-child {
          width: 3px;
          height: 16px;
          background: linear-gradient(180deg, #16B9FA 0%, #0058FF 100%);
          border-radius: 2px;
        }
      }
    }
  }
/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

</style>
