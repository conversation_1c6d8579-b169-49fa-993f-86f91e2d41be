<template>
  <div class="flex w-full h-[700px]">
    <!-- 右侧地图及搜索框 -->
    <div class="flex-1 relative">
      <!-- 顶部搜索 -->
      <div class="absolute top-2 left-70 z-10 bg-white shadow px-4 py-1 rounded">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入站点名称"
          clearable
          style="width: 300px"
        >
          <template #prepend>站点</template>
          <template #append>
            <el-button @click="handleSearch()" :icon="Search" />
          </template>
        </el-input>
      </div>
      <!-- 地图 -->
      <div class="w-full h-full" ref="mapContainer">
        <!-- 区域树 -->
        <div
          class="absolute top-2 left-2 z-10 bg-white shadow rounded p-2 w-64 max-h-[80%] overflow-auto"
        >
          <el-tree
            :data="areaTreeData"
            :props="{ children: 'children', label: 'name' }"
            node-key="id"
            accordion
            @node-click="handleAreaClick"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import Mapmost from '@mapmost/mapmost-webgl'
import { initMap, addMapImage, initPointLayer, setPoint } from '@/utils/mapmostUtils'
import { getDic, getSitePage, getDeviceList } from '@/api/infra/deviceDict'
import { Search } from '@element-plus/icons-vue'
import siteIcon from '../assets/siteIcon.png'
import clusteredIcon from '../assets/clusteredIcon.png'
import deviceIcon from '../assets/deviceIcon.png'
interface AreaTreeNode {
  id: number
  name: string
  parentId?: number
  weight?: number
  label?: string
  description?: string
  creator?: string
  updater?: string
  children?: AreaTreeNode[]
}

const mapContainer = ref<HTMLDivElement | null>(null)
const areaTreeData = ref<AreaTreeNode[]>([])
const searchKeyword = ref('')

const getData = () => {
  getAreaTree()
  handleSearch()
}

const getAreaTree = async () => {
  const data = await getDic({ dictType: 'oamArea' })
  areaTreeData.value = [
    {
      id: -1,
      name: '全部区域',
      children: data
    }
  ]
}

const choosedNodeId = ref(undefined)
const handleAreaClick = (node: any) => {
  choosedNodeId.value = node.id > 0 ? [node.id] : []
  handleSearch()
  // TODO: 可在此基础上实现地图区域定位等逻辑
}
const sitePointArr = ref<any>([])
const handleSearch = async () => {
  const params = {
    pageNo: 1,
    pageSize: 5000,
    siteName: searchKeyword.value,
    areaId: choosedNodeId.value
  }
  const { list } = await getSitePage(params)
  if (list && Array.isArray(list)) {
    sitePointArr.value = list.map((d) => ({ ...d, status: 'site', name: d.siteName }))
    setPoint(map, sitePointArr.value)
  }
}
let map: Mapmost.Map | null = null

onMounted(() => {
  if (mapContainer.value) {
    map = initMap(mapContainer.value)
    map.on('load', () => {
      addMapImage(map, siteIcon, 'siteIcon')
      addMapImage(map, clusteredIcon, 'cluster-icon')
      initPointLayer(map, ['site', 'siteIcon'])
      getData()

      map.on('click', 'unclustered-point', async function (e) {
        const features = map.queryRenderedFeatures(e.point, {
          layers: ['unclustered-point']
        })
        if (features.length > 0) {
          const props = features[0].properties
          const coords = features[0].geometry.coordinates
          const params = {
            pageNo: 1,
            pageSize: 100,
            siteId: props.id
          }
          const data = await getDeviceList(params)
          const popup = new Mapmost.Popup({ anchor: 'left', offset: [12, 0], closeButton: false })

          if (Array.isArray(data) && data.length > 0) {
            const content = `
    <div style="width: 165px; max-height: 300px; overflow-y: auto; font-size: 13px; color: #333;">
      <div style="font-weight: bold; padding: 6px 8px; border-bottom: 1px solid #eee;">
        设备列表（${data.length}）
      </div>
      ${data
        .map(
          (d) => `
        <div style="display: flex; align-items: center; padding: 8px; border-bottom: 1px solid #eee;">
          <img src="${d.assetsTypeIconUrl || deviceIcon}" style="width: 24px; height: 24px; margin-right: 8px;" />
          <div>
            <div style="font-weight: 600;">${d.assetsName}</div>
            <div style="font-size: 12px; color: #999;">${d.assetsTypeName || '暂无类型'}</div>
          </div>
        </div>
      `
        )
        .join('')}
    </div>
  `
            popup.setHTML(content)
          } else {
            popup.setHTML(`
    <div style="padding: 12px; font-size: 14px; color: #999;">暂无设备</div>
  `)
          }

          popup.setLngLat(coords).addTo(map)
        }
      })
    })
  }
})
</script>

<style scoped></style>
