<template>
  <doc-alert title="用户体系" url="https://doc.iocoder.cn/user-center/" />
  <doc-alert title="三方登陆" url="https://doc.iocoder.cn/social-user/" />
  <doc-alert title="Excel 导入导出" url="https://doc.iocoder.cn/excel-import-and-export/" />

  <el-row :gutter="20">
    <!-- 左侧部门树 -->
    <el-col :span="4" :xs="24">
      <ContentWrap class="h-1/1" style="background: rgb(241 243 246)">
        <DeptTrees  ref="treeRef" @node-click="handleDeptNodeClick" />
      </ContentWrap>
    </el-col>
    <el-col :span="20" :xs="24">

      <!-- 搜索 -->
      <ContentWrap>
        <el-form
          class="flex flex-wrap items-start -mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
        >
          <el-form-item label="" prop="siteName" class="!mr-3">
            <el-input
              v-model="queryParams.siteName"
              placeholder="输入站点名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
          <div>
            <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:device-site-info:query']" gradient @click="handleQuery" />
            <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
          </div>
            <div>
              <el-button
                plain
                @click="addSite('create')"
                v-hasPermi="['infra:device-site-info:create']"
              >
                <Icon icon="ep:plus" />
                新增
              </el-button>
              <el-button
                plain
                @click="handleImport"
                v-hasPermi="['infra:device-site-info:import']"
              >
                <Icon icon="ep:upload" /> 导入
              </el-button>
              <el-button
                plain
                @click="handleExport"
                :loading="exportLoading"
                v-hasPermi="['infra:device-site-info:export']"
              >
                <Icon icon="ep:download" />导出
              </el-button>
            </div>
          </div>
        </el-form>
      </ContentWrap>

      <ContentWrap>
        <el-table
          ref="tableRef"
          @selection-change="checkList"
          v-loading="loading"
          :data="list"
          :header-cell-style="{
             'background-color': '#F4F6F8',
            'font-size': '14px',
            'color': '#3B3C3D',}"
        >
          <el-table-column label="序号" type="selection" align="left" width="55" />

          <el-table-column label="序号" type="index" align="left" width="55" />
          <template v-for="dict in columnList" :key="dict.key">
            <el-table-column
              v-if="dict.key == 'operate'"
              :label="dict.label"
              align="left"
              width="160"
              :show-overflow-tooltip="true"
            >
              <template  #default="scope">
                  <el-button type="primary" style="width: 30px;" @click="openDetails(scope.row.id)" text>详情</el-button>
                  <el-button type="primary" style="width: 30px;" @click="openEdit(scope.row.id)" text>编辑</el-button>
                  <el-button type="primary" v-hasPermi="['infra:device-site-info:delete']" style="width: 30px;" @click="handleDelete(scope.row.id)"  text>删除</el-button>
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="dict.key == 'deviceNames'"
              :label="dict.label"
              align="left"
              min-width="300"
              :show-overflow-tooltip="true"
            >
              <template  #default="scope">
                <div class="TableDevice" >
                  <el-tag
                    v-for="(itm, index) in scope.row.deviceNames as string[]"
                    :key="index"
                    type="primary"
                    style="margin-right: 8px; flex-shrink: 0;"
                  >
                    {{itm}}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="dict.key == 'longitude'"
              :label="dict.label"
              align="left"
              :show-overflow-tooltip="true"
            >
              <template  #default="scope">
              <span>{{scope.row.longitude + '-' +scope.row.latitude }}</span>
              </template>
            </el-table-column>
          <el-table-column
            v-else
            :label="dict.label"
            align="left"
            :prop="dict.key"
            :show-overflow-tooltip="true"
          />
          </template>
        </el-table>

        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>

  </el-row>
  <ImportComponents
    ref="importFormRef"
    path="infra/device-site-info/import/template"
    fileName="站点导入模板"
    title="站点导入"
    :areaId="projectId"
    :downloadPath="DownloadPath"
    @success="getList"
  />
</template>
<script lang="ts" setup>
import download from '@/utils/download'
import {getSiteAdministrationPage,deleteAdministration,exportSiteAdministration} from "@/api/EquipmentManagement/SiteAdministration/index";
import {
  ref,
  reactive,
  onMounted,
} from 'vue'
import { useRouter } from "vue-router";
import DeptTrees  from './DeptTree.vue'
import emitter  from '@/utils/eventBus'

const route = useRouter()
const projectId = ref('') // 部门id
const DownloadPath = '/infra/device-site-info/get/template'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
let selectionList=ref([])
let columnList=ref([
  {label:'站点名称',key:'siteName'},{label:'经纬度',key:'longitude'},{label:'所属区域',key:'areaName'},
  {label:'设备数量',key:'deviceNumber'},{label:'站点设备',key:'deviceNames'},{label:'操作',key:'operate'}
] )  // 列的集合

const loading = ref(true) // 列表的加载中
const total = ref(10) // 列表的总页数
const list = ref([]) // 列表的数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  siteName:'',
  areaId:[],
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await getSiteAdministrationPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
/** 新增/编辑按钮 */
const addSite = (value:string) => {
  console.log(value)
  route.push('SiteAddition')

}

const openDetails = (id:number) => {
  route.push({
    path:'SiteDetails',
    query:{id:id}
  })
}
/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  if (projectId.value){
    importFormRef.value.open()
  }else {
    ElMessage.warning('请先选择区域')
  }

}

let tableRef= ref()

/** 表格选中数据 */
const checkList = (value:any) => {
  selectionList.value=value

}

//编辑按钮
const openEdit = (id:string) =>{
  route.push(
    {
    path:'SiteAddition',
      query:{id}
  }
  )
}

const treeRef = ref()
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  queryParams.areaId = []
  handleQuery()
  treeRef.value?.clearHighlight()
}

/** 处理部门被点击 */
const handleDeptNodeClick = async (row) => {
  queryParams.areaId = []
  queryParams.areaId.push(row.id)
  projectId.value = row.id
  await getList()
}

// /** 添加/修改操作 */
// const formRef = ref()
// const openForm = (type: string, id?: number) => {
//   formRef.value.open(type, id)
// }




/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    const ids=selectionList.value.map(item => item.id)
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await exportSiteAdministration({...queryParams,ids,areaId:''})
    download.excel(data, '站点管理.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await deleteAdministration(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

emitter.on('Site-change', (payload) => {
  // console.log('111111',payload)
  resetQuery()
  // 处理事件
})
// 组件卸载时取消监听
onUnmounted(() => {
  // emitter.off('event-name')
})

/** 初始化 */

onMounted(async () => {
  getList()

})

</script>
<style scoped>
.TableDevice{
  display: flex;
  align-items: center;
  min-width: 300px;
  overflow-x: auto;
  padding: 4px 0;
}
.TableDevice::-webkit-scrollbar {
  height: 6px;
}

.TableDevice::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.TableDevice::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.TableDevice::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
