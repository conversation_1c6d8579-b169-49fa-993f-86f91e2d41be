<template>
  <div class="site-addition">
    <!-- 头部 -->
    <header class="header">
      <div class="header-left">
        <el-button type="info" :icon="ArrowLeft" @click="goback()" plain>返回</el-button>

      </div>
      <div> <div class="title">{{`站点管理-${routers.query.id? '编辑' : '新增'}站点`}}</div></div>
      <div class="header-right">
        <XButton title="保存" @click="validateForms"  gradient />
        <el-button @click="onReset">取消</el-button>
      </div>
    </header>

    <!-- 主体内容 -->
    <div class="main-content">
      <!-- 左侧地图区域 -->
      <div class="content-left">
        <!-- 表单区域 -->
        <el-form
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          inline
          label-width="110px"
          class="site-form"
        >
<!--          <el-row :gutter="20">-->
<!--            <el-col :span="12">-->
              <el-form-item label="站点名称" prop="siteName" required>
                <el-input v-model="ruleForm.siteName" placeholder="请输入站点名称" />
              </el-form-item>
              <el-form-item label="所属区域" prop="areaId" required>
                <Dictionary
                  v-model="ruleForm.areaId"
                  type="cascader"
                  width="160px"
                  dict-type="oamArea"
                  :cascader-props="{
                multiple: false,
                checkStrictly: true,
                label: 'name',
                value: 'id'
              }"
                  max-collapse-tags="1"
                  placeholder="请选择区域"
                />
              </el-form-item>
<!--            </el-col>-->
<!--            <el-col :span="12">-->
              <el-form-item label="经纬度" required>
                <div class="coordinate-inputs">
                  <el-input
                    v-model="ruleForm.longitude"
                    placeholder="经度"
                  />
                  <el-input
                    v-model="ruleForm.latitude"
                    placeholder="纬度"
                  />
<!--                  <el-input-->
<!--                    v-model="choosedLngAndLat.lng"-->
<!--                    placeholder="经度"-->
<!--                  />-->
<!--                  <el-input-->
<!--                    v-model="choosedLngAndLat.lat"-->
<!--                    placeholder="纬度"-->
<!--                  />-->
                </div>
              </el-form-item>
<!--            </el-col>-->
<!--          </el-row>-->
        </el-form>

        <!-- 地图容器 -->
        <div class="map-wrapper">
          <MapPoints
            ref="mapPointRef"
            :setHeight="false"
            :showFind="false"
            :pointDisplay="true"
            :modelValue="MapLatitude"
            v-model="choosedLngAndLat"
            class="map-container"
          />
        </div>
      </div>

      <!-- 右侧资产列表 -->
      <div class="content-right">
         <div class="headerText">
           <div class="roundDot"></div>
           <div style="margin-left: 10px;">设备列表</div>
         </div>
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-form :inline="true" :model="devicesParams">
            <el-form-item>
              <el-input
                v-model="devicesParams.assetsName"
                style="width: 130px;"
                placeholder="输入设备编号/名称"
                clearable
              />
            </el-form-item>
            <el-form-item >
              <el-select  style="width: 75px;" v-model="devicesParams.regionMeter" placeholder="请选择"  clearable>
                <el-option label="5米" value="5" key="5" />
                <el-option label="10米" value="10" key="10" />
                <el-option label="15米" value="15" key="15" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <XButton title="查询" @click="handleQuery('device')"  gradient />
              <el-button @click="resetQuery('device')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <!-- 表格区域 -->
        <el-table
          ref="devicesTable"
          :data="deviceslist"
          @selection-change="selectionChange"
          max-height="250"
          class="asset-table"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="设备编号" prop="assetsCode" />
          <el-table-column label="设备名称" prop="assetsName" />
          <el-table-column label="所属项目" prop="projectName" />
          <el-table-column label="设备类型" prop="assetsTypeName" />
        </el-table>


        <div class="headerText" style="margin-top: 40px">
          <div class="roundDot"></div>
          <div style="margin-left: 10px;">辅件列表</div>
        </div>
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-form :inline="true" :model="AccParams">
            <el-form-item>
              <el-input
                v-model="AccParams.auxiliaryName"
                style="width: 130px;"
                placeholder="输入辅件编号/名称"
                clearable
              />
            </el-form-item>
            <el-form-item >
              <el-select  style="width: 75px;" v-model="AccParams.regionMeter" placeholder="请选择"  clearable>
                <el-option label="5米" value="5" key="5" />
                <el-option label="10米" value="10" key="10" />
                <el-option label="15米" value="15" key="15" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <XButton title="查询" @click="handleQuery('acc')"  gradient />
              <el-button @click="resetQuery('acc')">重置</el-button>
            </el-form-item>
          </el-form>

        </div>
        <!-- 表格区域 -->
        <el-table
          ref="AccTable"
          :data="AccList"
          :pointDisplay="false"
          @selection-change="selectionAccChange"
          max-height="300"
          class="asset-table"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="辅件编号" prop="auxiliaryCode" />
          <el-table-column label="辅件名称" prop="auxiliaryName" />
          <el-table-column label="辅件类型" prop="assetsTypeName" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive,onMounted,ref,watch, onUnmounted } from 'vue'
import { ArrowLeft} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import {useRouter,useRoute} from "vue-router";
import {getPropertyPage} from "@/api/EquipmentManagement/ManagementCenter";
import {
  getPropertyPagination,
  getAccessoriesList,
  CreateSite,
  AdministrationDetails,
  getAccessory,
  UpdateSite
} from '@/api/EquipmentManagement/SiteAdministration'
import emitter  from '@/utils/eventBus'
const { t } = useI18n() // 国际化
const data = reactive({})
const choosedLngAndLat = ref({ lng:'' , lat: '' })
let  rules=reactive({
  siteName: [
    { required: true, message: '请输入站点名称', trigger: 'blur' },
  ],
  areaId: [
    { required: true, message: '请选择所属区域', trigger: 'blur' },
  ],
  latitude: [
    { required: true, message: '请输入纬度', trigger: 'blur' },
  ],
  longitude: [
    { required: true, message: '请输入经度', trigger: 'blur' },
  ],
})
let ruleFormRef = ref()
let devicesTable = ref()
let AccTable = ref()
let customFieldsList = ref([])
// 所有选中项的 id 数组
const selectedIds = ref([])
let selectionList = ref([])  //设备选中数据
// 所有选中项的 id 数组
const selectedAccIds = ref([])
let selectionAccList = ref([])  //辅件选中数据
let customFieldValues = ref({})
let MapLatitude = ref({lng:'',log:''})  //地图经纬度
let ruleForm=ref({

})
const router = useRouter();
const routers = useRoute();

const deviceslist = ref([]) // 设备列表的数据

const AccList = ref([]) // 辅件列表的数据
const devicesParams = ref({
  regionMeter:'5',
  assetsName:'',
  latitude:'',
  longitude:'',
})
const AccParams = ref({
  auxiliaryName:'',
  regionMeter:'5',
})
//返回
const goback = () =>{
  router.go(-1)
}

let customFieldsRef = ref()
//保存按钮
async function onSave(){

}

//获取设备不分页列表数据
const getdeviceList = async ()=>{

  let data=await getPropertyPagination({...devicesParams.value,'latitude':ruleForm.value.latitude,'longitude':ruleForm.value.longitude})
  // let data=await getPropertyPagination({...devicesParams.value})
  deviceslist.value=[]
  try {
    if (data && data.length>0){
      data.forEach(item=>{
        if (!item.siteId || routers.query.id && routers.query.id ==item.siteId){
          deviceslist.value.push(item)
        }
      })
    }

    // deviceslist.value=data
    setTableSelection()
  }finally {

  }
}

//获取辅件不分页列表
const getAccList = async ()=>{
  let data=await getAccessoriesList({...AccParams.value,'latitude':ruleForm.value.latitude,'longitude':ruleForm.value.longitude})
  try {
    AccList.value=[]
    if (data.list && data.list.length>0){
      data.list.forEach(item=>{
        if (!item.siteId || routers.query.id && routers.query.id ==item.siteId){
          AccList.value.push(item)
        }
      })
    }
    // AccList.value=data.list
    setTableAccSelection()
  }finally {
    console.log('deviceslist.value',AccList.value,data.list)
  }
}

//查询
const handleQuery = (type:string)=>{
  if (type ==='device'){
    getdeviceList()
  }else{
    getAccList()
  }
}

//重置
const resetQuery = (type:string)=>{
  if (type ==='device'){
    devicesParams.value={
      regionMeter:'5',
      assetsName:'',
      latitude:ruleForm.value.latitude,
      longitude:ruleForm.value.longitude,
    }
    getdeviceList()
  }else {
    AccParams.value={
      auxiliaryName:'',
      regionMeter:'5',
      latitude:ruleForm.value.latitude,
      longitude:ruleForm.value.longitude,
    }
    getAccList()

  }

}

onMounted(async () => {
  if (routers.query.id){
   let data=await AdministrationDetails(routers.query.id)
    ruleForm.value.siteName=data.siteName
    ruleForm.value.id=data.id
    ruleForm.value.longitude=data.longitude
    ruleForm.value.latitude=data.latitude
    ruleForm.value.areaId=data.areaId
    let val= await getPropertyPage({siteId :routers.query.id})
    if (val.list?.length>0){
      selectedIds.value=val.list.map(p=>p.id)
      // console.log('selectedIds',selectedIds.value)
      setTableSelection()
    }
    let code= await getAccessory({siteId :routers.query.id})
    if (code.list?.length>0){
      selectedAccIds.value=code.list.map(p=>p.id)
      console.log('selectedIds',selectedAccIds.value)
      setTableAccSelection()
    }
  }
  getdeviceList()
  getAccList()
})

onUnmounted(() => {

})

const mapPointRef = ref()
/** 表格选中数据 */
const selectionChange = (selection) => {

  // 表格选择变化处理
  selectionList.value = selection
  // 更新总选择数组
  const currentPageIds = selection.map(item => item.id)
  // 获取当前页所有数据的 id
  const currentPageAllIds = deviceslist.value.map(item => item.id)

  // 从总选择中移除当前页取消选择的数据
  selectedIds.value = selectedIds.value.filter(id =>
    !currentPageAllIds.includes(id) || currentPageIds.includes(id)
  )
  // 添加当前页新选择的数据
  currentPageIds.forEach(id => {
    if (!selectedIds.value.includes(id)) {
      selectedIds.value.push(id)
    }
  })
  const sitePointArr= selection.map((d) => ({ ...d, status: 'site', name: d.assetsName }))
  mapPointRef.value.setPoint( sitePointArr)
}

//
// 切换页面时设置选中状态
const setTableSelection = () => {
  // 等待表格数据更新
  // 改用 Set 提高查找效率
  const selectedIdSet = new Set(selectedIds.value.map(String));

  setTimeout(() => {
    deviceslist.value.forEach(row => {
      const rowIdStr = String(row.id);
      if (selectedIdSet.has(rowIdStr)) {
        devicesTable.value?.toggleRowSelection(row, true);
      }
    });
  }, 200);
}

/** 辅件表格选中数据 */
const selectionAccChange = (selection) => {
  // 表格选择变化处理
  selectionAccList.value = selection
  // 更新总选择数组
  const currentPageIds = selection.map(item => item.id)
  // 获取当前页所有数据的 id
  const currentPageAllIds = AccList.value.map(item => item.id)

  // 从总选择中移除当前页取消选择的数据
  selectedAccIds.value = selectedAccIds.value.filter(id =>
    !currentPageAllIds.includes(id) || currentPageIds.includes(id)
  )
  // 添加当前页新选择的数据
  currentPageIds.forEach(id => {
    if (!selectedAccIds.value.includes(id)) {
      selectedAccIds.value.push(id)
    }
  })
  const sitePointArr= selection.map((d) => ({ ...d, status: 'site', name: d.assetsName }))
  mapPointRef.value.setPoint( sitePointArr)
}

const setTableAccSelection = () => {
  // 等待表格数据更新
  // 改用 Set 提高查找效率
  const selectedIdSet = new Set(selectedAccIds.value.map(String));

  setTimeout(() => {
    AccList.value.forEach(row => {
      const rowIdStr = String(row.id);
      if (selectedIdSet.has(rowIdStr)) {
        AccTable.value?.toggleRowSelection(row, true);
      }
    });
  }, 200);
}


//表单验证
 const validateForms = async () => {
  if (ruleForm.value.siteName && ruleForm.value.longitude && ruleForm.value.latitude && ruleForm.value.areaId ) {
    let areaId =  ruleForm.value.areaId[ruleForm.value.areaId.length - 1]
    let parms={
      ...ruleForm.value,
      deviceIds:selectedIds.value,
      auxiliaryIds:selectedAccIds.value,
      areaId
    }
    // console.log('ruleForm', parms)
    if (ruleForm.value.id){
      let data= await UpdateSite(parms)
    }else {
      let data= await CreateSite(parms)
    }
    emitter.emit('Site-change', '1')
    ElMessage.success('操作成功')
    // 触发列表刷新
    goback()
  }else {
    ElMessage.warning('请填写必填项')
  }
 }



//重置
function onReset() {
  ruleForm.value={}
  customFieldValues.value={}
}

watch(
  () => choosedLngAndLat.value,
  (newValue, oldValue) => {
    console.log('经纬度已更改:', newValue.lng);
    ruleForm.value.longitude=newValue.lng;
    ruleForm.value.latitude=newValue.lat;
    getdeviceList()
    getAccList()
    // 在这里更新地图视图或其他操作
  },
  { immediate: true }
)


</script>
<style lang="scss" scoped>
.site-addition {
  height: 100vh;
  background: #f5f6f8;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .header {
    height: 48px;
    padding: 0 16px;
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .title {
        font-size: 16px;
        font-weight: 500;
      }
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    gap: 16px;
    padding: 0 16px 16px;
    min-height: 600px;
    max-height: calc(100vh - 64px);
    overflow: hidden;

    .content-left {
      flex: 1;
      background: #fff;
      padding: 20px;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      min-width: 0;
      overflow: hidden;

      .site-form {
        flex-shrink: 0;
        margin-bottom: 20px;
      }

      .coordinate-inputs {
        display: flex;
        gap: 10px;

        .el-input {
          width: calc(50% - 5px);
        }
      }

      .map-wrapper {
        flex: 1;
        min-height: 400px;
        max-height: calc(100vh - 250px);
        position: relative;
        overflow: hidden;
        //border: 1px solid #eaedf2;
        border-radius: 4px;

        .map-container {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          min-width: 400px;
          min-height: 400px;
        }
      }
    }

    .content-right {
      width: 480px;
      background: #fff;
      padding: 20px;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      max-height: 100%;
      overflow: hidden;

      .search-area {
        flex-shrink: 0;
        margin-bottom: 16px;
      }

      .asset-table {
        flex: 1;
        overflow: auto;
        //min-height: 400px;
        //max-height: calc(100% - 100px);
      }
     .headerText{
       display: flex;
       align-items: center;
       margin-bottom: 20px;
       .roundDot{
         width: 10px;
         height: 10px;
         border-radius: 50%;
         background: #0c66ff;
       }
     }
    }
  }
}

// 响应式处理
@media screen and (max-width: 1400px) {
  .site-addition {
    .main-content {
      min-height: 500px;

      .map-wrapper {
        min-height: 350px;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .site-addition {
    .main-content {
      min-height: 400px;

      .content-left {
        .map-wrapper {
          min-height: 300px;
        }
      }
    }
  }
}

</style>
