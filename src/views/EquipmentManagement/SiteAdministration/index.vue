<template>
  <div>
    <ContentWrap>
      <el-tabs v-model="activeName"  class="demo-tabs">
        <el-tab-pane label="站点列表" name="first">
          <SiteList />
        </el-tab-pane>
        <el-tab-pane label="站点地图" name="second">
          <SiteMap v-if="activeName === 'second'" />
        </el-tab-pane>
      </el-tabs>
    </ContentWrap>
  </div>
</template>
<script lang="ts" setup>
import SiteList from './components/SiteList.vue'
import SiteMap from './components/SiteMap.vue'
// import AlreadyScrapped from './components/AlreadyScrapped.vue';

defineOptions({ name: 'SiteAdministration' })

const activeName = ref('first') // 列表的总页数

/** 初始化 **/
onMounted(() => {})
</script>
<style scoped></style>
