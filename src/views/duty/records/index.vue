<script setup>
import { ref } from 'vue'

import YearMonthPicker from '../components/YearMonthPicker.vue'
import ShiftRecord from '../components/ShiftRecord.vue'
import dayjs from 'dayjs'
import { exportDutyRecord } from '@/api/system/duty/duty.data'
import download from '@/utils/download'
const selectedDate = ref({
  year: dayjs().year(),
  month: dayjs().month() + 1 // `month()` 返回的是 0-11，需要 +1
})
const message = useMessage()

/** 导出按钮操作  导出排班记录*/
const handleExportRecord = async () => {
  // 导出的二次确认
  await message.exportConfirm()
  // 发起导出
  const data = await exportDutyRecord(selectedDate.value)
  download.excel(data, '交接班列表.xls')
}
</script>

<template>
  <div class="container">
    <YearMonthPicker v-model="selectedDate">
      <el-button
        plain
        style="margin-left: 5px"
        @click="handleExportRecord"
        v-hasPermi="['system:duty-record:query']"
      >
        <Icon icon="ep:download" />

        导出
      </el-button>
    </YearMonthPicker>
    <ShiftRecord :selectedDate="selectedDate" />
  </div>
</template>

<style scoped>
.container {
  text-align: center;
  padding: 20px;
  background-color: #fff;
}
</style>
