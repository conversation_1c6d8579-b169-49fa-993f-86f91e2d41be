<template>
  <el-container
    class="h-[calc(100vh-var(--top-tool-height)-var(--tags-view-height)-var(--app-content-padding)-var(--app-content-padding)-10px)]"
  >
    <!-- 左侧固定宽度 -->
    <el-aside
      width="350px"
      style="display: flex; flex-direction: column; gap: 10px; padding: 10px; background: #f5f5f5"
    >
      <el-card shadow="hover" style="flex: 1; overflow: auto">
        <PeopleSetting @refresh-list="refreshList" />
      </el-card>
      <el-card shadow="hover" style="flex: 1; overflow: auto">
        <TimeSetting @refresh-list="refreshList" />
      </el-card>
    </el-aside>

    <!-- 右侧自适应 -->
    <el-main class="right-box">
      <el-card class="card" shadow="hover" style="flex: 1">
        <YearMonthPicker v-model="selectedDate">
          <el-button
            plain
            style="margin-left: 5px"
            @click="importClick"
            v-hasPermi="['system:duty-detail:create']"
          >
            <Icon icon="ep:upload" />
            导入
          </el-button>
          <el-button
            plain
            style="margin-left: 5px"
            @click="handleExport"
            v-hasPermi="['system:duty-detail:query']"
          >
            <Icon icon="ep:download" />

            导出
          </el-button>
        </YearMonthPicker>
        <DateSwiper ref="dataRef" :selectedDate="selectedDate" />
      </el-card>
    </el-main>
  </el-container>
  <ImportForm ref="importFormRef" />
</template>

<script setup lang="ts">
import YearMonthPicker from '../components/YearMonthPicker.vue'
import DateSwiper from '../components/DateSwiper.vue'
import PeopleSetting from '../components/PeopleSetting.vue'
import TimeSetting from '../components/TimeSetting.vue'
import ImportForm from '../components/ImportForm.vue'
import dayjs from 'dayjs'

import { exportDuty } from '@/api/system/duty/duty.data'
import download from '@/utils/download'

const selectedDate = ref({
  year: dayjs().year(),
  month: dayjs().month() + 1 // `month()` 返回的是 0-11，需要 +1
})

const dataRef = ref<InstanceType<typeof DateSwiper> | null>(null)

const refreshList = () => {
  if (dataRef.value) {
    dataRef.value.getData()
  }
}

const message = useMessage() // 消息弹窗

/** 导入按钮操作*/
const importFormRef = ref()
const showImport = ref(false)
const importClick = async () => {
  showImport.value = true
  importFormRef.value.open()
}

/** 导出按钮操作 导出排班设置*/
const handleExport = async () => {
  // 导出的二次确认
  await message.exportConfirm()
  // 发起导出
  const data = await exportDuty(selectedDate.value)
  download.excel(data, '交接班列表.xls')
}
</script>

<style scoped lang="scss">
.container {
  position: relative;
}
.right-box {
  padding: 10px;
  display: flex;
  flex-direction: column;
}
</style>
