<template>
  <div class="p-4">
    <vue-cal
      :events="events"
      :selected-date="formattedDate"
      locale="zh-cn"
      display-mode="month"
      :hide-view-selector="true"
      :disable-views="['years', 'year', 'week', 'day']"
      :hide-title-bar="true"
      class="custom-calendar"
      @cell-click="onDateClick"
    >
      <template #cell-content="day">
        <div
          class="day-cell"
          :class="day.cell.outOfScope ? 'opacity-50 bg-[#EAEDF1]' : 'opacity-100 bg-[#FFF]'"
        >
          <span :class="getDayStyle(getPeopleForDate(day))" class="day-number">{{
            parseInt(day.cell.formattedDate.slice(-2), 10)
          }}</span>
          <div class="tags-container">
            <div class="grid grid-cols-2 px-3 gap-1 max-h-16 overflow-hidden">
              <el-tag
                v-for="(person, index) in getPeopleForDate(day).slice(0, 6)"
                :key="index"
                :type="getStatusType(person.dutyStatus)"
                size="small"
                class="truncate text-sm"
              >
                {{ person.userName }}
                <img
                  v-if="person.dutyStatus > 0"
                  class="w-[12px] h-[12px]"
                  :src="person.dutyStatus === 1 ? blueIcon : orangeIcon"
                />
              </el-tag>
            </div>
          </div>
        </div>
      </template>
    </vue-cal>
  </div>
  <Dialog v-model="dialogVisible" title="交接班记录" width="800px">
    <div class="text-left">
      <div class="date-header mb-4">
        <strong>{{ choosedDate }}</strong>
        <strong class="ml-3">{{ getWeekday(choosedDate) }}</strong>
      </div>
      <div v-if="dialogData.length > 0">
        <div
          v-for="(record, index) in dialogData"
          :key="index"
          class="duty-record mb-1 border-b-4 border-gray-500"
        >
          <div class="record-header">
            <img
              v-if="record.dutyStatus > 0"
              class="w-[12px] h-[12px]"
              :src="record.dutyStatus === 1 ? blueIcon : orangeIcon"
            />
            {{ record.deptName }} - {{ record.dutyUserName }}

            <span class="time-range">【{{ record.startTime }} - {{ record.endTime }}】</span>
            <span class="ml-3">签到： {{ formattedToHHmm(record.checkInTime) }}</span>
            <span class="ml-3">签退： {{ formattedToHHmm(record.checkOutTime) }}</span>
            <el-button class="ml-3" v-if="record.id" @click="handleDetail(record.id)" size="small"
              >交班记录 ></el-button
            >
          </div>
        </div>
      </div>
      <div v-else class="empty-message">暂无交接记录</div>
    </div>
  </Dialog>
  <Dialog v-model="detailVisible" title="交接班记录" width="800px">
    <ExamForm :form="examForm" :disabled="true" />
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from 'vue'
import VueCal from 'vue-cal'
import 'vue-cal/dist/vuecal.css'
import dayjs from 'dayjs'
import { ElTag } from 'element-plus'
import blueIcon from '@/assets/imgs/icons/sign-blue.png'
import orangeIcon from '@/assets/imgs/icons/sign-orange.png'
import {
  getDutyRecordListSimple,
  getDutyRecordListDetail,
  getDutyRecordShift
} from '@/api/system/duty/duty.data'

interface SelectedDate {
  year: number
  month: number
}
interface DutyStaff {
  userName: string
  dutyStatus: number
  dutyDetailId: number
}

interface DutyRecord {
  date: string
  dutyStaffs: DutyStaff[]
}

interface PlatformState {
  code: number
  name: string
  value: number
}

interface ShiftDetail {
  platformState: PlatformState[]
  exceptDesc: string
  frontDesc: string
  carrierDesc: string
  handDesc: string
  otherDesc: string
  dealDesc: string
  dealResult: string
  hygienicConditions: string
}
const props = defineProps<{ selectedDate: SelectedDate }>()

const backendData = ref<DutyRecord[]>([])
onMounted(() => {
  getData()
})
watch(
  () => props.selectedDate,
  () => {
    getData()
  },
  { deep: true }
)

const getData = async () => {
  const data: DutyRecord[] = await getDutyRecordListSimple({
    year: props.selectedDate.year,
    month: props.selectedDate.month
  })
  backendData.value = data
}

const events = computed(() => {
  return backendData.value.flatMap((item) =>
    item.dutyStaffs.map((person) => ({
      start: item.date,
      end: item.date,
      content: `${person.userName} - ${person.dutyStatus}`
    }))
  )
})

const getDayStyle = (day) => {
  if (!Array.isArray(day) || day.length === 0) {
    return ''
  } else {
    const allDutyStatusIs1 = day.every((item) => item.dutyStatus === 1)
    const hasDutyStatus2 = day.some((item) => item.dutyStatus === 2)

    if (allDutyStatusIs1) {
      return 'text-[#0053DF]'
    } else if (hasDutyStatus2) {
      return 'text-[#F67C00]'
    } else {
      return 'text-[#3B3C3D]'
    }
  }
}

function getWeekday(dateStr: string) {
  const weekdayMap = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const dayIndex = dayjs(dateStr).day() // 0~6
  return weekdayMap[dayIndex]
}
function formattedToHHmm(dateStr: number) {
  if (dateStr) {
    return dayjs(dateStr).format('HH:mm')
  } else {
    return '--'
  }
}
const formattedDate = computed(() => {
  return dayjs(`${props.selectedDate.year}-${props.selectedDate.month}-01`).format('YYYY-MM-DD')
})

const getPeopleForDate = (obj: any) => {
  const date = obj.cell.formattedDate
  const event = backendData.value.find((item) => item.date === date)
  return event ? event.dutyStaffs : []
}

const getStatusType = (dutyStatus: number) => {
  switch (dutyStatus) {
    case 1:
      return 'primary'
    case 0:
      return 'info'
    case 2:
      return 'warning'
    default:
      return 'primary' // 其他状态默认蓝色
  }
}
const message = useMessage()

interface DialogData {
  id: number
  dutyDetailId: number
  dutyUserId: number
  dutyTimeId: number
  checkInTime: number
  checkOutTime: number
  createTime: number
  date: string
  deptName: string
  dutyStatus: number
  dutyUserName: string
  startTime: string
  endTime: string
  shiftRecord: string
}

const dialogVisible = ref<boolean>(false)
const dialogData = ref<DialogData[]>([])
const choosedDate = ref<string>('')

const onDateClick = async (event: any) => {
  const clickedDate = dayjs(event).format('YYYY-MM-DD')
  choosedDate.value = clickedDate
  const dateObj = backendData.value.find((item) => item.date === clickedDate)
  if (dateObj && dateObj.dutyStaffs && dateObj.dutyStaffs.length) {
    dialogData.value = await getDutyRecordListDetail(dateObj.dutyStaffs.map((d) => d.dutyDetailId))
    dialogVisible.value = true
  } else {
    message.error('暂无记录')
  }
}

const detailVisible = ref(false)
const examForm = ref<ShiftDetail>()
const handleDetail = async (dutyDetailId: number) => {
  const data = await getDutyRecordShift({ recordId: dutyDetailId })
  if (data) {
    examForm.value = data
    detailVisible.value = true
  }
}
</script>

<style lang="scss" scoped>
.custom-calendar {
  height: 600px;
}
.vuecal__cell {
  background-color: #ecedee;
}

.day-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 90%;
  width: 90%;
  margin: 0 auto;
  text-align: center;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
  border: 1px solid transparent;
}

.day-cell:hover {
  border: 1px solid #0053df;
}

.day-number {
  position: absolute;
  top: 4px;
  font-size: 25px;
  font-weight: bold;
}

.tags-container {
  position: absolute;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 14px;
  gap: 4px;
  bottom: 10px;
}

.record-header {
  font-weight: bold;
  position: relative;
}

/* 去除 vue-cal 月视图中每一格的边框 */
:deep(.vuecal__cell:before)  {
  border: none !important;
}

:deep(.vuecal__cell--selected) {
  background-color: transparent !important;
}
.vuecal__flex {
  background-color: #f4f6f8;
}
</style>
