<script setup lang="ts">
import { defineProps } from 'vue'

type StaffMember = {
  id: number
  userName: string
  deptName: string
}

type DutyTime = {
  startTime: string
  endTime: string
  dutyStaffs: StaffMember[]
  dutyTimeId: number
}

const props = withDefaults(
  defineProps<{ dutyTimes: DutyTime[]; sortIndex: number; isPastOrToday: boolean }>(),
  {}
)

const calculateTimePercentage = (dutyTimes: DutyTime[]) => {
  if (Array.isArray(dutyTimes) && dutyTimes.length) {
    const timeToPercentage = (time: string) => {
      const [hours, minutes, seconds] = time.split(':').map(Number)
      return ((hours * 3600 + minutes * 60 + seconds) / 86400) * 100
    }

    let startTimePoint = timeToPercentage(dutyTimes[0].startTime)
    return Math.ceil(startTimePoint)
  } else {
    return 0
  }
}

const emit = defineEmits(['handleAdd'])
const handleAdd = (duty: DutyTime) => {
  emit('handleAdd', duty)
}

const formatTime = (time: string) => {
  return time.slice(0, 5) // 去掉秒部分，保留 HH:MM
}

// 将时间转换为秒数
const toSeconds = (time: string) => {
  const [h, m, s] = time.split(':').map(Number)
  return h * 3600 + m * 60 + s
}

const isNextDay = (startTime: string, endTime: string) => {
  return toSeconds(startTime) >= toSeconds(endTime) ? '次日' : ''
}
</script>

<template>
  <div
    class="timeline"
    v-if="props.dutyTimes && props.dutyTimes[0]"
    :style="{ left: `${calculateTimePercentage(props.dutyTimes) - 20}%` }"
  >
    <div class="time-label">{{ props.dutyTimes[0].startTime }}</div>
    <!-- 顶部时间 -->
    <div class="line">
      <div class="dot" :class="sortIndex ? 'bg-[#19C692]' : 'bg-[#005FFF]'"></div>
      <!-- 顶端小圆点 -->
    </div>
  </div>
  <div
    v-if="props.dutyTimes && props.dutyTimes.length"
    class="w-[95%] z-2 mx-1 py-2 text-left absolute overflow-hidden rounded-md"
    :class="sortIndex ? 'bg-[#CFEBE2aa]' : 'bg-[#E4EBF6aa]'"
    :style="{
      left: `${calculateTimePercentage(props.dutyTimes)}%`,
      top: `${props.sortIndex * 150 + 20}px`
    }"
  >
    <div v-for="(duty, index) in props.dutyTimes" class="mb-2" :key="index">
      <div class="flex justify-between">
        <div
          class="bg-[#005FFF] rounded-r-xl pl-2 inline pr-4 text-white text-sm py-1"
          :class="sortIndex ? 'bg-[#19C692]' : 'bg-[#005FFF]'"
        >
          {{ formatTime(duty.startTime) }} -{{
            isNextDay(duty.startTime, duty.endTime) + formatTime(duty.endTime)
          }}
        </div>
        <div
          v-if="!props.isPastOrToday"
          @click="handleAdd(duty)"
          class="pointer bg-white mr-2 rounded-md size-[25px] text-center text-<25px>/<25px>"
          >+</div
        >
      </div>

      <div class="">
        <div
          v-for="staff in duty.dutyStaffs"
          :key="staff.id"
          class="flex justify-between bg-white m-2 p-2 rounded-md"
        >
          <span class="text-xs text-gray-700">{{ staff.deptName }} - {{ staff.userName }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
button:focus {
  outline: none;
}

.pointer {
  cursor: pointer;
  pointer-events: all;
  z-index: 2;
}

.timeline {
  position: absolute;
  left: -30px; // 调整虚线位置
  top: -32px;
  height: calc(100% + 32px);
  display: flex;
  flex-direction: column;
  align-items: center;

  .time-label {
    padding: 10px 8px 0;
    font-size: 12px;
    color: #333;
  }

  .line {
    width: 2px;
    height: 100%;
    border-left: 2px dashed #ddd; // 长虚线
    position: relative;
  }

  .dot {
    position: absolute;
    top: 0;
    left: -4px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
}
</style>
