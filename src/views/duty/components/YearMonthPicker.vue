<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'

// Props：默认选中的年份和月份
const props = withDefaults(
  defineProps<{
    modelValue: { year: number; month: number }
    isSetting?: boolean
  }>(),
  {
    isSetting: false
  }
)

// Emits：支持 v-model 绑定
const emit = defineEmits(['update:modelValue'])
const message = useMessage() // 消息弹窗

// 当前年份范围（可根据需求调整）
const currentYear = new Date().getFullYear()
const yearOptions = Array.from({ length: 10 }, (_, i) => currentYear - 5 + i)

// 选中的年份和月份（默认值从 props 里获取）
const selectedYear = ref(props.modelValue.year || currentYear)
const selectedMonth = ref(props.modelValue.month || new Date().getMonth() + 1)
const isSettingRef = ref(props.isSetting)

// 处理年份切换
const handleYearChange = (year: number) => {
  selectedYear.value = year
  emit('update:modelValue', { year, month: selectedMonth.value })
}

// 处理月份点击
const handleMonthSelect = (month: number) => {
  selectedMonth.value = month
  emit('update:modelValue', { year: selectedYear.value, month })
  // ElMessage.success(`选择了 ${selectedYear.value} 年 ${month} 月`)
}
</script>

<template>
  <div class="year-month-picker">
    <!-- 年份选择 -->
    <el-select v-model="selectedYear" class="year-select" @change="handleYearChange">
      <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
    </el-select>

    <!-- 月份选择 -->
    <div class="month-buttons">
      <el-button
        style="margin-left: 5px"
        v-for="month in 12"
        :key="month"
        :type="month === selectedMonth ? 'primary' : 'default'"
        @click="handleMonthSelect(month)"
      >
        {{ month }}月
      </el-button>
    </div>
    <slot></slot>
  </div>
</template>

<style scoped>
.year-month-picker {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  overflow-x: auto;
}

.year-select {
  width: 100px;
  flex-shrink: 0;
}

.month-buttons {
  display: flex;
  gap: 8px;
}

.el-button {
  min-width: 50px;
}
</style>
