<script setup lang="ts">
import { computed } from 'vue'
import dayjs from 'dayjs'
import {
  getDutyDeTailList,
  getDutyTimeList,
  getDutyDeTailUpdate,
  getDutyStaffList,
  getUpdateDutyTime
} from '@/api/system/duty/duty.data'
import DateBox from './DateBox.vue'
import { Clock } from '@element-plus/icons-vue'
import type { ElTable } from 'element-plus'

type DateProps = {
  selectedDate: { year: number; month: number }
}
interface Schedule {
  groupId: number
  periodName: string
  periodTime: TimeRange[]
}

// 定义时间段类型
interface TimeRange {
  startTime: string
  endTime: string
  id: number
}

const props = defineProps<DateProps>()

// 获取当前月份的天数
const daysInMonth = computed(() => {
  return new Date(props.selectedDate.year, props.selectedDate.month, 0).getDate()
})

// 生成当前月份的日期列表
const groupsBox = ref<number[]>([])

const days = computed(() => {
  return Array.from({ length: daysInMonth.value }, (_, i) => {
    const date = `${props.selectedDate.year}-${String(props.selectedDate.month).padStart(2, '0')}-${String(i + 1).padStart(2, '0')}`
    const dutyInfo = dutyData.value.find((d) => d.date === date) || {
      date,
      dutyTimes: []
    }
    let sortIndex = 0
    if (dutyInfo.dutyTimes.length) {
      if (!groupsBox.value.includes(dutyInfo.dutyTimes[0].groupId)) {
        groupsBox.value.push(dutyInfo.dutyTimes[0].groupId)
      }
      sortIndex = groupsBox.value.findIndex((d) => d === dutyInfo.dutyTimes[0].groupId) || 0
    }
    return { date, dutyInfo, sortIndex }
  })
})

type DutyDate = {
  date: string
  weekday: string
  dutyTimes: DutyTime[]
}
const dutyData = ref<DutyDate[]>([])

type DutyTime = {
  dutyTimeId: number
  startTime: string
  endTime: string
  groupId: number
  defaultPeriod: number
  dutyStaffs: {
    id: number
    userId: number
    userName: string
    deptName: string
    avatar: string | null
    createTime: string | null
  }[]
}
const defaultGroupId = ref(0)

const getData = async () => {
  const data = await getDutyTimeList()
  console.log(
    data.find((d) => d.defaultPeriod),
    'data.find((d) => d.isDefault).groupId'
  )
  if (data && Array.isArray(data)) {
    defaultGroupId.value = data.find((d) => d.defaultPeriod)?.groupId
  }
  getDayList()
}

const getDayList = async () => {
  groupsBox.value = [defaultGroupId.value]
  dutyData.value = await getDutyDeTailList(props.selectedDate)
}

const changeTimesVisible = ref(false)
interface Schedule {
  groupId: number
  periodName: string
  periodTime: TimeRange[]
}

const schedules = ref<Schedule[]>([])

const getTimes = async () => {
  const data = await getDutyTimeList()
  if (data && Array.isArray(data)) {
    schedules.value = data
  }
}
type Detail = {
  dutyUserId: number
  dutyTimeId: number
  groupId: number
}
type TimeForm = {
  dutyDate: string
  groupId: number
  details: Detail[]
}

const changeTimesForm = ref<TimeForm>({
  dutyDate: '',
  groupId: 0,
  details: []
})
const changeTimes = (day: any) => {
  const { date, dutyInfo } = day
  changeTimesForm.value.groupId = dutyInfo?.dutyTimes?.[0].groupId || 0
  getTimes()
  changeTimesForm.value.dutyDate = date
  changeTimesVisible.value = true
}

const isPastOrToday = (dateStr: string): boolean => {
  const inputDate = dayjs(dateStr).startOf('day') // 解析输入日期并设置为当天开始
  const today = dayjs().startOf('day') // 获取今天的日期，并设置为当天开始

  return !inputDate.isAfter(today) // 未来时间返回 false，今天或过去返回 true
}
const message = useMessage()

const confirmTimeChange = async () => {
  // 校验 schedules 是否存在
  if (!schedules.value || schedules.value.length === 0) {
    message.error('未找到排班信息，请稍后重试')
    return
  }

  // 查找对应的排班数据
  const selectedSchedule = schedules.value.find(
    (schedule: Schedule) => schedule.groupId === changeTimesForm.value.groupId
  )

  // 校验是否找到对应的排班信息
  if (!selectedSchedule || !selectedSchedule.periodTime) {
    message.error('未找到匹配的排班时间')
    return
  }

  // 更新表单详情
  changeTimesForm.value.details = selectedSchedule.periodTime.map((time: TimeRange) => ({
    dutyUserId: [],
    dutyTimeId: time.id,
    groupId: changeTimesForm.value.groupId
  }))

  try {
    // 发送更新请求
    const response = await getUpdateDutyTime(changeTimesForm.value)

    if (response) {
      changeTimesVisible.value = false // 关闭弹窗
      getData() // 重新获取数据
      message.success('替换成功')
    } else {
      message.error('该时间段已添加成员，删除后再替换')
    }
  } catch (error) {
    console.error('更新排班详情失败:', error)
    message.error('系统错误，请联系管理员')
  }
}
watch(
  () => props.selectedDate,
  () => {
    getData()
  },
  { deep: true, immediate: true }
)

const formatDay = (time: string) => {
  return time.slice(5, 10) // 去掉秒部分，保留 HH:MM
}

// 将时间转换为秒数
const toSeconds = (time: string) => {
  const [h, m, s] = time.split(':').map(Number)
  return h * 3600 + m * 60 + s
}
const computedTimeText = (time: TimeRange) => {
  return `${time.startTime.slice(0, 5)}-${toSeconds(time.startTime) - toSeconds(time.endTime) > 0 ? '次日' : ''}${time.endTime.slice(0, 5)}`
}

const addVisible = ref(false)
const choosedtime = ref('')
const choosedTimeData = ref<DutyTime | null>(null)
const defaultKeys = ref<string[]>([])
const handleAdd = async (date: string, duty: DutyTime) => {
  defaultKeys.value = [date]
  choosedtime.value = date + '-' + duty.dutyTimeId
  choosedTimeData.value = duty
  addVisible.value = true
  await getMember()

  selectedMembers.value = duty.dutyStaffs.map((d) => d.userId)
}
watch(
  () => addVisible.value,
  (v) => {
    if (!v) {
      getData()
    }
  }
)

const formatDate = (dateStr: string) => {
  const [year, month, day] = dateStr.split('-')
  return `${month}-${day}`
}

const defaultProps = {
  children: 'children',
  label: 'date'
}

const treeData = computed(() => {
  return days.value.map((day) => {
    return {
      key: day.date, // 为日期节点设置唯一 key
      date: day.date,
      dutyInfo: day.dutyInfo,
      sortIndex: day.sortIndex,
      children: day.dutyInfo.dutyTimes.map((dutyTime) => {
        return {
          key: day.date + '-' + dutyTime.dutyTimeId, // 为值班时间节点设置唯一 key
          date: day.date, // 添加 date 属性以匹配 node-key
          startTime: dutyTime.startTime,
          endTime: dutyTime.endTime,
          dutyStaffs: dutyTime.dutyStaffs,
          ...dutyTime // 包含所有值班时间属性
        }
      })
    }
  })
})

interface Member {
  userId: number
  id: number
  userName: string
  avatar: string
}
const members = ref<Member[]>([])

const getMember = async () => {
  const data = await getDutyStaffList()
  if (data && Array.isArray(data)) {
    members.value = data
  }
}
const selectedMembers = ref<number[]>([])
const handleSelectionChange = async (val: Member[]) => {
  if (!isSyncing.value) {
    const newVal = val.map((member) => member.userId)
    if (choosedTimeData.value) {
      const details = [
        {
          dutyUserId: newVal,
          dutyTimeId: choosedTimeData.value.dutyTimeId,
          groupId: choosedTimeData.value.groupId
        }
      ]
      const dateStr = choosedtime.value.slice(0, 10)
      const dutyTimeId = choosedTimeData.value.dutyTimeId
      const dutyDate = dutyData.value.find((d) => d.date === dateStr)
      if (dutyDate) {
        const dutyTime = dutyDate.dutyTimes.find((dt) => dt.dutyTimeId === dutyTimeId)
        if (dutyTime) {
          dutyTime.dutyStaffs = val // 更新本地数据
        }
      }
      const data = await getDutyDeTailUpdate({
        dutyDate: choosedtime.value.slice(0, 10),
        details: details
      })
      if (data) {
        message.success('更改成功')
        selectedMembers.value = newVal
      }
    }
  }
}
const selectable = () => {
  return !isPastOrToday(choosedtime.value.slice(0, 10))
}

const handleTime = (date: any) => {
  selectedMembers.value = date.dutyStaffs.map((staff: Member) => staff.userId)
  choosedtime.value = date.key
  choosedTimeData.value = date
}

const multipleTable = ref<InstanceType<typeof ElTable> | null>(null)

const isSyncing = ref(false)
watch(
  selectedMembers,
  (newVal) => {
    isSyncing.value = true
    nextTick(async () => {
      if (multipleTable.value) {
        multipleTable.value.clearSelection()
        newVal.forEach((userId) => {
          const row = members.value.find((member) => member.userId === userId)
          if (row) {
            if (multipleTable.value) {
              multipleTable.value.toggleRowSelection(row, true)
            }
          }
        })
      }
      isSyncing.value = false
    })
  },
  { deep: true }
)

// 选中项详情
const selectedSchedule = computed(() =>
  schedules.value.find((s) => s.groupId === changeTimesForm.value.groupId)
)

defineExpose({
  getData
})
</script>

<template>
  <div
    class="overflow-x-auto overflow-y-hidden always-scrollbar whitespace-nowrap w-full h-full border p-4"
  >
    <div class="flex">
      <div
        v-for="day in days"
        :key="day.date"
        class="flex flex-col items-center min-w-[180px] max-w-full bg-white"
      >
        <div class="font-bold w-full text-center pt-2 pb-4 box-border">{{
          formatDay(day.date)
        }}</div>
        <div
          class="relative w-full h-[calc(100vh-var(--top-tool-height)-var(--tags-view-height)-var(--app-content-padding)-var(--app-content-padding)-240px)] box-border border border-gray-600"
        >
          <DateBox
            :style="{ top: `${day.sortIndex * 150}px` }"
            :dutyTimes="day.dutyInfo.dutyTimes"
            :sortIndex="day.sortIndex"
            :isPastOrToday="isPastOrToday(day.date)"
            @handle-add="(duty) => handleAdd(day.date, duty)"
          />
        </div>
        <div class="box-border flex items-center w-full p-2 z-3">
          <el-button class="m-auto" :disabled="isPastOrToday(day.date)" @click="changeTimes(day)"
            >选择交接时段</el-button
          ></div
        >
      </div>
    </div>
  </div>
  <Dialog
    title="选择交接时段"
    v-model="changeTimesVisible"
    width="420px"
    :scroll="true"
    maxHeight="300px"
  >
    <div class="pt-4 space-y-4">
      <div>
        <!-- 下拉选择器 -->
        <el-select v-model="changeTimesForm.groupId" placeholder="时段名称" @change="handleSelect">
          <el-option
            v-for="(schedule, index) in schedules"
            :key="index"
            :label="schedule.periodName"
            :value="schedule.groupId"
          />
        </el-select>

        <!-- 选中项的时间段展示 -->
        <div v-if="selectedSchedule" class="mt-4 p-4 bg-gray-50 rounded">
          <div class="font-medium mb-2">{{ selectedSchedule.periodName }}</div>
          <div class="space-y-1">
            <div
              v-for="(time, i) in selectedSchedule.periodTime"
              :key="i"
              class="flex items-center text-sm"
            >
              <el-icon class="mr-1"><Clock /></el-icon>
              {{ computedTimeText(time) }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="flex justify-center w-full">
        <el-button type="primary" @click="confirmTimeChange">确认</el-button>
        <el-button @click="changeTimesVisible = false">取消</el-button>
      </div>
    </template>
  </Dialog>
  <Dialog title="添加人员" v-model="addVisible" width="920px" :scroll="true" maxHeight="550px">
    <div class="flex h-full">
      <!-- 左侧日期树 -->
      <div class="w-1/3 border-r overflow-y-auto max-h-[550px]">
        <el-tree
          :data="treeData"
          node-key="key"
          :props="defaultProps"
          :default-expanded-keys="defaultKeys"
        >
          <template #default="{ data }">
            <span class="flex items-center">
              <span v-if="data.dutyInfo">
                <!-- 日期节点 -->
                <span class="mr-2">
                  {{ formatDate(data.date) }} ({{ data.dutyInfo.weekday }})
                </span>
              </span>
              <span
                class="min-w-48"
                :class="choosedtime === data.key ? 'bg-blue-100 rounded p-x-1' : ''"
                @click="handleTime(data)"
                v-else
              >
                <!-- 值班时间节点 -->
                <span class="flex inline-block">
                  <span>{{ computedTimeText(data) }}</span>
                  <el-tag
                    v-for="staff in data.dutyStaffs"
                    :key="staff.id"
                    size="small"
                    class="inline-flex"
                  >
                    {{ staff.userName || '无人员' }}
                  </el-tag>
                </span>
              </span>
            </span>
          </template>
        </el-tree>
      </div>

      <!-- 右侧表格 -->
      <div class="w-2/3 p-4">
        <el-table
          :data="members"
          @selection-change="handleSelectionChange"
          ref="multipleTable"
          row-key="userId"
          stripe
          border
        >
          <el-table-column type="selection" :selectable="selectable" width="50" />
          <el-table-column prop="userName" label="人员名称" />
          <el-table-column prop="deptName" label="单位" />
        </el-table>
      </div>
    </div>
  </Dialog>
</template>

<style lang="scss" scoped>
.box-border {
  border: 1px solid #ecedee;
}

.el-radio-group {
  align-items: self-start;
  display: block;
}

/* src/assets/global.css 或 App.vue 中 <style> */
.always-scrollbar {
  scrollbar-width: auto; /* Firefox */
  -ms-overflow-style: auto; /* IE 10+ */
  overflow-y: hidden;
  overflow-x: scroll;
}

/* 对 Webkit 浏览器强制显示滚动条 */
.always-scrollbar::-webkit-scrollbar {
  height: 8px;
}

.always-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}
</style>
