<template>
  <div class="header">
    <div>交接班成员</div>
    <el-button @click="showAddDialog" plain class="m-t-8px">
      <Icon icon="ep:plus" />
    </el-button>
  </div>

  <div class="member-container">
    <div v-for="member in members" :key="member.id" class="member-card">
      <!-- 删除按钮 -->
      <el-button
        class="delete-btn"
        type="info"
        size="small"
        @click="removeMember(member.id, member.userName)"
        >×</el-button
      >

      <!-- 头像 -->
      <img :src="member.avatar || defaultAvater" alt="头像" class="avatar" />

      <!-- 名字 -->
      <div class="name">{{ member.userName }}</div>
    </div>
  </div>

  <Dialog title="添加交接班人员" v-model="dialogVisible" width="800px">
    <div class="dialog-header">
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
      >
        <el-form-item prop="name">
          <el-input v-model="queryParams.username" placeholder="输入名称" />
        </el-form-item>
        <el-form-item label="单位" prop="deptId">
          <el-tree-select
            clearable
            v-model="queryParams.deptId"
            :data="list"
            :props="defaultProps"
            check-strictly
            class="!w-240px"
            node-key="id"
            placeholder="请选择归属部门"
            @change="handleQuery()"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <Icon class="mr-5px" icon="ep:search" />
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="list mb-8">
      <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" :selectable="selectable" width="55" />

        <el-table-column label="用户账号" prop="username" :show-overflow-tooltip="true" />
        <el-table-column label="角色" prop="roleName" :show-overflow-tooltip="true" />
        <el-table-column label="单位" prop="deptName" :show-overflow-tooltip="true" />
      </el-table>

      <Pagination
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNo"
        :total="total"
        @pagination="handleQuery"
      />
    </div>
    <template #footer>
      <div class="flex justify-center w-full">
        <el-button type="primary" @click="handleAdd">添 加 </el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getDutyStaffList,
  getDutyStaffDelete,
  getDutyStaffCreate
} from '@/api/system/duty/duty.data'
import * as UserApi from '@/api/system/user'
import * as DeptApi from '@/api/system/dept'
import { defaultProps, handleTree } from '@/utils/tree'
import defaultAvater from '@/assets/imgs/default-avatar.png'
const message = useMessage() // 消息弹窗

interface Member {
  id: number
  userName: string
  avatar: string
}

const getData = async () => {
  const data = await getDutyStaffList()
  if (data && Array.isArray(data)) {
    members.value = data
  }
}
getData()
const dialogVisible = ref(false)
const members = ref<Member[]>([])

const removeMember = async (id: Number, userName: String) => {
  await message.confirm(`确定将【${userName}】从交接班中删除吗？`)
  const data = await getDutyStaffDelete(id)
  if (data) {
    ElMessage.success('删除成功')
    getData()
    emit('refreshList')
  }
}

const showAddDialog = () => {
  handleQuery()
  dialogVisible.value = true
}

watch(dialogVisible, () => {
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  queryParams.deptId = undefined
  queryParams.username = undefined
  queryParams.status = undefined
})

const userList = ref<UserApi.UserVO[]>([]) // 用户列表

/** 初始化 **/
onMounted(async () => {
  await getList()
  // 获取用户列表
  userList.value = await UserApi.getSimpleUserList()
})

const loading = ref(true) // 列表的加载中
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  deptId: undefined,
  username: undefined,
  status: undefined
})
/** 查询部门列表 */
const list = ref() // 列表的数据

const getList = async () => {
  loading.value = true
  try {
    const data = await DeptApi.getDeptPage(queryParams)
    list.value = handleTree(data)
  } finally {
    loading.value = false
  }
}
const total = ref(0) // 列表的总页数

const handleQuery = async () => {
  loading.value = true
  try {
    const data = await UserApi.getUserPage(queryParams)
    userList.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const selectedIds = ref<any>([]) // 表格的选中 ID 数组

const handleSelectionChange = (rows: UserApi.UserVO[]) => {
  selectedIds.value = rows.map((row) => {
    return { userId: row.id, deptName: row.deptName, userName: row.username }
  })
}

const selectable = (row) => {
  return !members.value.map((d) => d.userId).includes(row.id)
}
const emit = defineEmits(['refreshList'])

const handleAdd = async () => {
  const data = await getDutyStaffCreate(selectedIds.value)
  if (data) {
    ElMessage.success('添加成功')
    dialogVisible.value = false
    getData()
    emit('refreshList')
  }
}
</script>

<style lang="scss" scoped>
.header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.member-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 10px;
}

.member-card {
  position: relative;
  width: 58px;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  text-align: center;
  padding: 5px;
  position: relative;
}

.avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.name {
  font-size: 12px;
  margin-top: 5px;
  white-space: nowrap;
}

.delete-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 18px;
  height: 18px;
  font-size: 12px;
  line-height: 12px;
  padding: 0;
  border-radius: 50%;
  background-color: #919aaa;
  color: white;
  border: none;
  cursor: pointer;
}
</style>
