<template>
  <div class="header">
    <div>交接时段设置</div>
    <el-button @click="handleAdd" plain class="m-t-8px">
      <Icon icon="ep:plus" />
    </el-button>
  </div>
  <div class="pt-4 space-y-4">
    <div
      v-for="(schedule, index) in schedules"
      :key="index"
      :class="['rounded-lg  flex flex-col space-y-2 relative']"
    >
      <!-- 标题区域 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <span v-if="schedule.isDefault" class="bg-blue-500 size-3 inline-block rounded-1"></span>
          <span>
            <el-tooltip :content="schedule.periodName" placement="top" v-if="schedule.periodName">
              <span class="font-semibold text-gray-800">
                {{ truncateString(schedule.periodName) }}
              </span>
            </el-tooltip>
            <span v-else class="font-semibold text-gray-800">暂无</span>
          </span>

          <span v-if="schedule.isDefault" class="text-blue-500 text-sm font-medium">默认</span>
        </div>
      </div>

      <!-- 排班时间 -->
      <div class="space-y-1">
        <div v-for="(time, i) in schedule.periodTime" :key="i" class="flex items-center text-sm">
          <el-icon class="mr-1"><Clock /></el-icon>
          {{ computedTimeText(time) }}
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="absolute top--2 right-0 flex space-x-2">
        <el-button v-if="!schedule.isDefault" size="small" @click="setDefault(schedule.groupId)">
          <el-icon><Position /></el-icon>
        </el-button>
        <el-button size="small">
          <el-icon><Edit @click="handleUpdate(schedule)" /></el-icon>
        </el-button>
        <el-button
          size="small"
          v-if="!schedule.isDefault"
          @click="deleteSchedule(schedule.groupId, schedule.periodName)"
        >
          <el-icon><Delete /></el-icon>
        </el-button>
      </div>
    </div>
  </div>

  <Dialog v-model="dialogVisible" width="800px">
    <template #title>
      <span>
        <span>{{ form.groupId ? '编辑交接时段' : '添加交接时段' }}</span>
        <span class="text-gray-400 text-xs ml-2 font-light"
          >注：多个时间段之间时间区间不能重叠</span
        >
      </span>
    </template>

    <el-form ref="formRef" :rules="rules" :model="form" label-width="100px">
      <!-- 时段名称 -->
      <el-form-item label="名称" required>
        <el-input v-model="form.periodName" placeholder="请输入时段名称" />
      </el-form-item>

      <!-- 默认时段 -->
      <el-form-item label="默认时段">
        <el-switch v-model="form.defaultPeriod" :active-value="1" :inactive-value="0" />
      </el-form-item>

      <!-- 时间段列表 -->
      <el-form-item label="时间段" required>
        <div v-for="(time, index) in form.periodTime" :key="index" class="time-range mb-4">
          <el-form-item :prop="`periodTime.${index}.startTime`" :rules="timeRules">
            <el-time-picker
              v-model="time.startTime"
              placeholder="签到时间"
              format="HH:mm:ss"
              value-format="HH:mm:ss"
            />
            <span class="separator ml-1 mr-1">—</span>
            <el-time-picker
              v-model="time.endTime"
              placeholder="签退时间"
              format="HH:mm:ss"
              value-format="HH:mm:ss"
            />
            <el-button
              class="ml-1"
              plain
              @click="removeTime(index)"
              v-if="form.periodTime.length > 1"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </el-form-item>
        </div>
        <div class="w-full">
          <el-button class="w-full" plain @click="addTime">添加时间段</el-button>
        </div>
      </el-form-item>
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="flex justify-center w-full">
        <el-button type="primary" @click="savePeriod">{{
          form.groupId ? '编辑' : '添加'
        }}</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Delete, Edit, Position, Clock } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, FormItemRule } from 'element-plus'

import {
  getDutyTimeList,
  getDutyTimeCreate,
  getDutyTimeUpdate,
  getDutyTimeDelete,
  getDutyTimeUpdateDefault
} from '@/api/system/duty/duty.data'

const message = useMessage() // 消息弹窗

interface Schedule {
  groupId: number
  periodName: string
  periodTime: TimeRange[]
  isDefault: boolean
}

// 定义时间段类型
interface TimeRange {
  startTime: string
  endTime: string
}

// 定义表单数据类型
interface PeriodForm {
  groupId: number
  periodName: string
  defaultPeriod: number
  periodTime: TimeRange[]
}

const schedules = ref<Schedule[]>([])

// 设为默认排班

const emit = defineEmits(['refreshList'])
const setDefault = async (groupId: number) => {
  const data = await getDutyTimeUpdateDefault(groupId)
  if (data) {
    ElMessage.success('设置成功！')
    getList()
    emit('refreshList')
  }
}
const handleUpdate = (schedule: Schedule) => {
  resetForm()
  Object.assign(form, JSON.parse(JSON.stringify({ ...schedule }))) // 重新填充默认值
  form.defaultPeriod = schedule.isDefault ? 1 : 0
  dialogVisible.value = true
}
function truncateString(str: string) {
  if (!str) return ''
  return str.length > 6 ? str.slice(0, 6) + '......' : str
}

const handleAdd = () => {
  console.log(form)
  resetForm()
  form.groupId = 0
  form.periodName = ''
  form.defaultPeriod = 0
  form.periodTime = [{ startTime: '', endTime: '' }]

  dialogVisible.value = true
}

// 删除排班
const deleteSchedule = async (groupId: number, periodName: string) => {
  if (schedules.value.length <= 1) {
    return ElMessage.warning('至少保留一个时段！')
  }
  await message.delConfirm(`确认删除【${periodName}】时间段？`)

  const data = await getDutyTimeDelete(groupId)
  if (data) {
    ElMessage.success('删除成功！')
    getList()
  }
}

// 将时间转换为秒数
const toSeconds = (time: string) => {
  const [h, m, s] = time.split(':').map(Number)
  return h * 3600 + m * 60 + s
}
const computedTimeText = (time: TimeRange) => {
  return `${time.startTime}-${toSeconds(time.startTime) - toSeconds(time.endTime) > 0 ? '次日' : ''}${time.endTime}`
}
const getList = async () => {
  const data = await getDutyTimeList()
  if (data && Array.isArray(data)) {
    schedules.value = data.map((d: any) => {
      return {
        groupId: d.groupId,
        periodName: d.periodName,
        periodTime: d.periodTime,
        isDefault: !!d.defaultPeriod
      }
    })
  }
}
getList()

// 弹框控制
const dialogVisible = ref<boolean>(false)
const formRef = ref<FormInstance | null>(null)

// 表单数据
const form = reactive<PeriodForm>({
  groupId: 0,
  periodName: '',
  defaultPeriod: 0,
  periodTime: [{ startTime: '', endTime: '' }]
})

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields() // 使用 Element Plus 提供的 resetFields 方法
  }
}

// 表单校验规则
const rules: FormRules = {
  periodName: [{ required: true, message: '请输入时段名称', trigger: 'blur' }]
}

const timeRules: FormItemRule = { required: true, message: '请选择时间', trigger: 'change' }
// 添加时间段
const addTime = () => {
  form.periodTime.push({ startTime: '', endTime: '' })
}

// 删除时间段
const removeTime = (index: number) => {
  form.periodTime.splice(index, 1)
}

// 保存时段
const savePeriod = (): void => {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      console.log('保存的数据：', form)
      if (form.groupId) {
        const data = await getDutyTimeUpdate(form)
        if (data) {
          ElMessage.success('时段编辑成功！')
          dialogVisible.value = false
          emit('refreshList')
          getList()
        }
      } else {
        const data = await getDutyTimeCreate(form)
        if (data) {
          ElMessage.success('时段添加成功！')
          dialogVisible.value = false
          emit('refreshList')
          getList()
        }
      }
    }
  })
}
</script>

<style scoped>
.header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
