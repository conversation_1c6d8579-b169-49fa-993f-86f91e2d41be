<template>
  <Dialog width="80%" v-model="dialogVisible" :title="dialogTitle">
    <div style="display: flex;">
      <div style="width: 30%;border: #e9e9e9 solid 1px;border-radius: 15px; padding: 20px;">

        <el-form
          class="-mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="50px"
        >
          <el-form-item label="" prop="project">
            <el-input
              v-model="queryParams.project"
              placeholder="请输入项目编号/名称"
              clearable
              size="default"
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item>
<!--            <el-button size="default" type="primary" @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 查询</el-button>-->
            <XButton title="查询" preIcon="ep:search" gradient @click="handleQuery" />
          </el-form-item>
        </el-form>
        <el-table
          :data="tableData"
          style="width: 100%; margin-bottom: 20px;margin-top: 15px;"
          height="480"
          row-key="id"
          :row-class-name="tableRowClassName"
          @selection-change="tableDataSelection"
          default-expand-all
        >
          <el-table-column label="序号" type="selection" :selectable="checkSelectable" align="left" width="55" />
          <el-table-column prop="projectCode" label="项目编号" align="left"  />
          <el-table-column prop="projectName" label="项目名称" align="left"  />

        </el-table>

      </div>
      <div style="width: 70%;border: #e9e9e9 solid 1px;border-radius: 15px; padding: 20px;margin-left: 15px;">
        <el-form
          class="-mb-15px"
          :model="formData"
          ref="formRef"
          :inline="true"
          label-width="55px"
        >
          <el-form-item label="" prop="assetsName">
            <el-input
              v-model="formData.assetsName"
              placeholder="请输入设备编号/名称"
              clearable
              size="default"
              class="!w-180px"
            />
          </el-form-item>
          <el-form-item label="类型" prop="assetsTypeId">
            <el-cascader
              :options="copeDeviceType"
              v-model="assetsTypeId"
              @change="handleChange"
              max-collapse-tags="1"
              :props="props1"
              style="width: 100%;"
              collapse-tags
              clearable
            />
          </el-form-item>


          <el-form-item label="区域" prop="areaId">
            <Dictionary
              v-model="formData.areaId"
              type="cascader"
              dict-type="oamArea"
              :cascader-props="{
              multiple: true,
              label:'name',
              value: 'id',
             }"
              max-collapse-tags="1"
              placeholder="请选择区域"
            />
          </el-form-item>
          <el-form-item>
<!--            <el-button size="default" type="primary" @click="devicehandleQuery"><Icon icon="ep:search" class="mr-5px" /> 查询</el-button>-->
            <XButton title="查询" preIcon="ep:search" gradient @click="devicehandleQuery" />
          </el-form-item>
        </el-form>
        <el-table
          ref="tableRef"
          style="margin-top: 15px;"
          height="400"
          row-key="id"
          :header-cell-style='{ "background-color": "#e4eefc", "color": "#606266" }'
          v-loading="loading"
          :data="tabelist"
          @selection-change="handleSelectionChange">
          <el-table-column
            type="selection"
            :reserve-selection="true"
            width="55" />
          <el-table-column
            label="设备编号"
            align="left"
            prop="assetsCode"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="设备名称"
            align="left"
            prop="assetsName"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="所属项目" align="left" prop="projectName" />
          <el-table-column
            label="设备类型"
            align="left"
            prop="assetsTypeName"
          />
          <el-table-column label="区域" align="left" prop="area"  />
          <el-table-column label="地址" align="left" prop="address"  />
        </el-table>
        <Pagination
          style="margin-top: 30px;"
          :total="total"
          v-model:page="currentPage"
          v-model:limit="pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <template #footer>
      <div style="text-align: center;">
        <el-button :disabled="formLoading" class="gradient-btn" style="width: 300px;" type="primary" @click="submitForm">确 定</el-button>
        <el-button style="width: 300px;" @click="dialogVisible = false">取 消</el-button>
      </div>

    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import * as DeptApi from '@/api/system/UnitList/index'
import {ref, nextTick, computed, onMounted, onUnmounted, watch} from "vue";
import { ElMessage } from 'element-plus';
import {getPermissionList, getProjectList, TenantVO} from '@/api/system/UnitList/index'

interface TreeNode {
  id: number;
  parentId?: number;
  typeName: string;
  children?: TreeNode[];
}

interface TableRow {
  id: string | number;
  [key: string]: any;
}

const queryParams = ref({
  project: '',
}) // 消息弹窗
const tabelist = ref<TableRow[]>([]) // 表格数据
const loading = ref(false) // 弹窗的是否展示
const assetsTypeId = ref([]) // 设备类型储存值
const props1 = {
  value: 'id',
  label: 'name',
  children: 'children',
  multiple: true
}
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('')
const SelectedList = ref([])  //已勾选设备类型
const deviceId = ref([]) // 弹窗标题
const DeviceType = ref([]) // 设备类型选中合集
const copeDeviceType = ref([]) // 设备类型选中合集
const permissionDeptId = ref('')
const total = ref(0)
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const tableRef = ref() // 表格引用
const formRef = ref() // 表单 Ref
const tableData = ref<TableRow[]>([]) // 类型表格数据

interface FormData {
  permissionDeptId: string;
  areaId: string[];
  assetsTypeId: string[];
  pageNo: string;
  pageSize: string;
  assetsName: string;
}

const formData = ref<FormData>({
  permissionDeptId:'',
  areaId: [],
  assetsTypeId:[],
  pageNo: '1',
  pageSize: '10',
  assetsName: '',
})

const selectedRows = ref(new Set()) // 存储所有选中的行
const unselectedRows = ref(new Set()) // 存储取消选中的行

const apiParams = computed(() => ({
  pageNo: String(formData.value.pageNo),
  pageSize: String(formData.value.pageSize),
  assetsName: formData.value.assetsName,
  permissionDeptId: formData.value.permissionDeptId,
  areaId: formData.value.areaId,
  projectId: formData.value.projectId,
  assetsTypeId: formData.value.assetsTypeId
} as any))

const currentPage = computed({
  get: () => Number(formData.value.pageNo),
  set: (val: number) => {
    formData.value.pageNo = String(val)
  }
})

const pageSize = computed({
  get: () => Number(formData.value.pageSize),
  set: (val: number) => {
    formData.value.pageSize = String(val)
  }
})

// 用于标记是否在程序控制的选择过程中
const isProgrammaticSelect = ref(false)

// 初始化数据
const initData = async () => {
  formLoading.value = true
  try {
    // 先获取已选中的设备类型
    await getSelectedProperty()
    // 再获取类型列表
    let res = await DeptApi.getProjectList(queryParams.value)
    // tableData.value = buildTree(res.list)
    tableData.value = res
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  } finally {
    formLoading.value = false
  }
}

/** 打开弹窗 */
const open = async (id: string) => {
  formData.value.permissionDeptId = id
  permissionDeptId.value = id
  dialogTitle.value = '添加项目'
  dialogVisible.value = true
  resetForm()
  await initData()
}

const getList = async () => {
  if (formData.value.projectId.length>0){
    loading.value = true
    try {
      const res = await DeptApi.getPropertyPages(apiParams.value)
      tabelist.value = res.list
      total.value = res.total
      // 设置选中状态
      nextTick(() => {
        // 先清除当前页的选择状态
        isProgrammaticSelect.value = true
        tableRef.value?.clearSelection()

        // 遍历当前页的数据，根据unselectedRows来决定是否选中
        tabelist.value.forEach(row => {
          if (!Array.from(unselectedRows.value).some(unselectedRow => unselectedRow.id === row.id)) {
            tableRef.value?.toggleRowSelection(row, true)
          }
        })

        // 延迟一下再重置标记，确保所有选择操作都完成
        setTimeout(() => {
          isProgrammaticSelect.value = false
        }, 0)
      })
    } finally {
      loading.value = false
    }
  }else {
    ElMessage.warning('请先选择项目')
  }
}

const buildTree = (items: TreeNode[], parentId?: number): TreeNode[] => {
  const itemMap: Record<number, TreeNode> = {};
  const tree: TreeNode[] = [];

  // 首先将所有项存入映射表，用id作为键
  items.forEach(item => {
    itemMap[item.id] = { ...item, children: [] };
  });

  // 遍历所有项，将它们添加到它们的父项的children数组中
  items.forEach(item => {
    if (item.parentId) {
      const parent = itemMap[item.parentId];
      if (parent && parent.children) {
        parent.children.push(itemMap[item.id]);
      }
    } else {
      // 如果没有parentId，则认为是根节点
      tree.push(itemMap[item.id]);
    }
  });

  return tree;
}

const handleChange = (val) => {
  // 1. 展平数组
  const flatArray = val.flat()
  // 2. 去重
  const uniqueArray = [...new Set(flatArray)]
  // 3. 过滤掉无效值（如 null、undefined）
  const validArray = uniqueArray.filter(item => item != null)
  formData.value.assetsTypeId=validArray

}


/** 提交表单 */
const emit = defineEmits(['success'])
const submitForm = async () => {
  if (DeviceType.value.length>0){
    formLoading.value = true
    try {
      let params=[]
      // 只传递取消选中的值
      const unselectedIds = Array.from(unselectedRows.value)
      DeviceType.value.forEach(item => {
        if (unselectedIds.length>0 && unselectedIds.some(unselected => unselected.projectId === item.id)) {
          params.push(
            {
              projectId:item.id,
              permissionCode:item.projectCode,
              permissionName:item.projectName,
              deptId:permissionDeptId.value,
              permissionTypeCode:'1',
              permissionTypeName:'项目',
              excludeDeviceIds:unselectedIds.filter(unselected => unselected.projectId === item.id).map(unselected => unselected.id)
            }
          )
        }else {
          params.push(
            {
              projectId:item.id,
              permissionCode:item.projectCode,
              permissionName:item.projectName,
              deptId:permissionDeptId.value,
              permissionTypeCode:'1',
              permissionTypeName:'项目',
              excludeDeviceIds:[]
            }
          )
        }
      })

      // 批量删除取消选中的权限
      let res = await DeptApi.createPermissions(params)
      formData.value.assetsTypeId=[]
      tabelist.value=[]
      unselectedRows.value.clear()
      ElMessage.success('保存成功')
      dialogVisible.value = false
      emit('success', true)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      formLoading.value = false
    }
  }else {
    ElMessage.warning('请先选择项目')
  }

}
/** 搜索按钮操作 */
const handleQuery = () => {
  // gettypeList()
  initData()
}
/** 搜索按钮操作 */
const devicehandleQuery = () => {
  getList()

}
/** 设备类型筛选 */
const tableDataSelection = (val) => {
  DeviceType.value = val
  if (val.length>0){
    formData.value.projectId = val.map(item=>item.id)
    // 清空取消选中的记录
    // unselectedRows.value.clear()
    getList()
    deviceId.value = val.map(item=>item.id)
  }else {
    formData.value.projectId=[]
    tabelist.value=[]
    total.value='0'
    unselectedRows.value.clear()
  }
}

// const gettypeList = async () => {
//   let res = await DeptApi.getTypelist({
//     pageNo: '1',
//     pageSize: '10',
//     // typeCode: queryParams.value.typeCode,
//     assetsTypeId: '',
//     permissionTypeCode: '',
//     deviceTypeId: '',
//     projectId: '',
//     status: '',
//     permissionCode: '',
//     permissionName: '',
//     permissionTypeName: ''
//   } as TenantVO)
//   tableData.value = buildTree(res.list)
// }

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  // 如果是程序控制的选择，不处理
  if (isProgrammaticSelect.value) {
    return
  }

  // 获取当前页面所有行
  const currentPageRows = tabelist.value
  const selectedIds = new Set(selection.map(row => row))

  // 更新取消选中的行的ID
  currentPageRows.forEach(row => {
    if (!selectedIds.has(row)) {
      // 如果当前行未被选中，将其添加到取消选中集合
      unselectedRows.value.add(row)
    } else {
      // 如果当前行被选中，将其从取消选中集合中移除
      unselectedRows.value.delete(row)
    }
  })
}

const tableRowClassName = ({ row }) => {
  return SelectedList.value.includes(row.id) ? 'disabled-row' : ''
}

/** 获取已有设备类型 */
const getSelectedProperty = async () => {
  try {
    let res = await DeptApi.getPermissionList({
      permissionTypeCode: '1',
      deptId: permissionDeptId.value
    })
    SelectedList.value = res.list?.map(item => item.projectId) || []

  } catch (error) {
    console.error('获取已选设备类型失败:', error)
    SelectedList.value = []
  }
}

// 判断行是否可选
const checkSelectable = (row) => {
  return !SelectedList.value.includes(row.id)
}

onMounted(async () => {
  // 加载设备类型数据
  copeDeviceType.value = await DeptApi.getDeviceType()
})
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    areaId: [],
    projectId: [],
    pageNo: '1',
    pageSize: '10',
    permissionDeptId: permissionDeptId.value,
    assetsName: '',
  }
  formData.value.projectId = []
  tabelist.value = []
  DeviceType.value = []
  assetsTypeId.value=[]
  SelectedList.value = []
  formRef.value?.resetFields()
  // 清空取消选中的记录
  unselectedRows.value.clear()
  // 清空表格选择
  tableRef.value?.clearSelection()
}

// 组件卸载时清理
onUnmounted(() => {
  resetForm()
})

// 监听弹窗关闭
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})

// 暴露方法
defineExpose({ open })
</script>
<style>
.el-table .disabled-row {
  background-color: #d9d9d9;
  color: #999;
}
.el-table .disabled-row .el-checkbox__inner {
  background-color: #d9d9d9;
  border-color: #dcdfe6;
  cursor: not-allowed;
}
.gradient-btn {
  background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
  border: none !important;
  font-size: 14px !important;
  padding: 9px 16px !important;
  color: #ffffff !important;

  &:hover {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.8;
  }

  &:active {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.9;
  }

  // 覆盖 element-plus 的默认样式
  &.el-button--primary {
    --el-button-hover-bg-color: transparent;
    --el-button-active-bg-color: transparent;
  }
}
</style>
