<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="" prop="name" class="!mr-3">
        <el-input
          v-model="deptName"
          placeholder="输入单位名称"
          clearable
          class="!w-240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item label="状态" prop="status">-->
<!--        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">-->
<!--          <el-option-->
<!--            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="类型" prop="status">-->
<!--        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">-->
<!--          <el-option-->
<!--            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item class="!mr-3">
        <XButton title="查询" preIcon="ep:search" gradient @click="handleQuery" />
<!--        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 查询</el-button>-->
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column type="index" label="序号" width="55" align="left">
        <template #default="scope">
          <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column label="单位名称" align="left" prop="deptName" />
      <el-table-column label="单位类型" align="left" prop="typeName" />
      <el-table-column label="单位负责人" align="left" prop="omuAdminName" width="100" />
      <el-table-column label="一月"  width="80"  align="center" prop="jan">
        <template #default="scope">
          <el-icon v-if="scope.row.jan==true" :size="23" style="color: #5a9cf8"><Checked /></el-icon>
          <el-icon v-else :size="23" style="color: #8a909c"><Failed /></el-icon>
        </template>
      </el-table-column>
      <el-table-column label="二月" width="80"   align="left" prop="feb">
        <template #default="scope">
          <el-icon v-if="scope.row.feb==true" :size="23" style="color: #5a9cf8"><Checked /></el-icon>
          <el-icon v-else :size="23" style="color: #8a909c"><Failed /></el-icon>
        </template>
      </el-table-column>
      <el-table-column label="三月"  width="80"  align="left" prop="march">
        <template #default="scope">
          <el-icon v-if="scope.row.march==true" :size="23" style="color: #5a9cf8"><Checked /></el-icon>
          <el-icon v-else :size="23" style="color: #8a909c"><Failed /></el-icon>
        </template>
      </el-table-column>
      <el-table-column label="四月"  width="80"  align="left" prop="april">
        <template #default="scope">
          <el-icon v-if="scope.row.april==true" :size="23" style="color: #5a9cf8"><Checked /></el-icon>
          <el-icon v-else :size="23" style="color: #8a909c"><Failed /></el-icon>
        </template>
      </el-table-column>
      <el-table-column label="五月"  width="80"  align="left" prop="may">
        <template #default="scope">
          <el-icon v-if="scope.row.may==true" :size="23" style="color: #5a9cf8"><Checked /></el-icon>
          <el-icon v-else :size="23" style="color: #8a909c"><Failed /></el-icon>
        </template>
      </el-table-column>
      <el-table-column label="六月" width="80"   align="left" prop="june">
        <template #default="scope">
          <el-icon v-if="scope.row.june==true" :size="23" style="color: #5a9cf8"><Checked /></el-icon>
          <el-icon v-else :size="23" style="color: #8a909c"><Failed /></el-icon>
        </template>
      </el-table-column>
      <el-table-column label="七月"  width="80"  align="left" prop="july">
        <template #default="scope">
          <el-icon v-if="scope.row.july==true" :size="23" style="color: #5a9cf8"><Checked /></el-icon>
          <el-icon v-else :size="23" style="color: #8a909c"><Failed /></el-icon>
        </template>
      </el-table-column>
      <el-table-column label="八月" width="80"   align="left" prop="aug">
        <template #default="scope">
          <el-icon v-if="scope.row.aug==true" :size="23" style="color: #5a9cf8"><Checked /></el-icon>
          <el-icon v-else :size="23" style="color: #8a909c"><Failed /></el-icon>
        </template>
      </el-table-column>
      <el-table-column label="九月" width="80"   align="left" prop="sep">
        <template #default="scope">
          <el-icon v-if="scope.row.sep==true" :size="23" style="color: #5a9cf8"><Checked /></el-icon>
          <el-icon v-else :size="23" style="color: #8a909c"><Failed /></el-icon>
        </template>
      </el-table-column>
      <el-table-column label="十月"  width="80"  align="left" prop="oct">
        <template #default="scope">
          <el-icon v-if="scope.row.oct==true" :size="23" style="color: #5a9cf8"><Checked /></el-icon>
          <el-icon v-else :size="23" style="color: #8a909c"><Failed /></el-icon>
        </template>
      </el-table-column>
      <el-table-column label="十一月"   width="80" align="left" prop="novnov">
        <template #default="scope">
          <el-icon v-if="scope.row.nov==true" :size="23" style="color: #5a9cf8"><Checked /></el-icon>
          <el-icon v-else :size="23" style="color: #8a909c"><Failed /></el-icon>
        </template>
      </el-table-column>
      <el-table-column label="十二月"  width="80"  align="left" prop="dec">
        <template #default="scope">
          <el-icon v-if="scope.row.dec==true" :size="23" style="color: #5a9cf8"><Checked /></el-icon>
          <el-icon v-else :size="23" style="color: #8a909c"><Failed /></el-icon>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="left">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.deptId)"
          >
            排班
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
<!--    <Pagination-->
<!--      :total="total"-->
<!--      v-model:page="queryParams.pageNo"-->
<!--      v-model:limit="queryParams.pageSize"-->
<!--      @pagination="getList"-->
<!--    />-->
  </ContentWrap>

</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import {
  Checked,
  Failed,
} from '@element-plus/icons-vue'
import download from '@/utils/download'
import * as PostApi from '@/api/system/WorkforceManagement'
import router from "@/router";
// import PostForm from './PostForm.vue'


// defineOptions({ name: 'SystemPost' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const deptName = ref('') // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询岗位列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await PostApi.getList(deptName.value)
    // console.log('排班管理',data)
    list.value = data
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}


/** 重置按钮操作 */
const resetQuery = () => {
  deptName.value = ''
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 排班管理*/
const openForm = (type: string, id?: number) => {
  router.push({
    path: 'Scheduling',
    query: { id }
  })
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PostApi.deletePost(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await PostApi.exportPost(queryParams)
    download.excel(data, '岗位列表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
