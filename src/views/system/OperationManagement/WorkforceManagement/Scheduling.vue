<template>
  <ContentWrap>
    <div style="display: flex;align-items: center;justify-content: space-between">
      <div>
        <el-button type="primary" @click="router.go(-1)" size="small" plain>返回</el-button>
        <span style="margin-left: 10px;">排班管理-排班</span>
      </div>
      <div>
        <el-button @click="handleImport" plain v-hasPermi="['system:omu-schedule:import']" > <Icon icon="ep:upload" /> 导入 </el-button>
        <el-button plain @click="handleExport" v-hasPermi="['system:omu-schedule:export']" :loading="exportLoading">
          <Icon icon="ep:download" />导出
        </el-button>
      </div>
    </div>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <div>
      <el-select
        v-model="queryParams.year"
        placeholder="Select"
        @change="getList"
        size="large"
        style="width: 120px"
      >
        <el-option
          v-for="item in yearList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-radio-group style="margin-left: 40px; gap: 0;display: inline-flex; " @change="getList"
                      v-model="queryParams.month" size="large">
        <el-radio-button class="custom-radio-button" label="1月" value="1"/>
        <el-radio-button class="custom-radio-button" label="2月" value="2"/>
        <el-radio-button class="custom-radio-button" label="3月" value="3"/>
        <el-radio-button class="custom-radio-button" label="4月" value="4"/>
        <el-radio-button class="custom-radio-button" label="5月" value="5"/>
        <el-radio-button class="custom-radio-button" label="6月" value="6"/>
        <el-radio-button class="custom-radio-button" label="7月" value="7"/>
        <el-radio-button class="custom-radio-button" label="8月" value="8"/>
        <el-radio-button class="custom-radio-button" label="9月" value="9"/>
        <el-radio-button class="custom-radio-button" label="10月" value="10"/>
        <el-radio-button class="custom-radio-button" label="11月" value="11"/>
        <el-radio-button class="custom-radio-button" label="12月" value="12"/>
      </el-radio-group>

      <div style="margin-top: 20px;">
        <div class="schedule-grid">
          <div class="contentStyle" v-for="itm in contentList" :key="itm.id">
            <div class="date-section">
              <el-button size="small" type="primary">{{ itm.day + "号" }}</el-button>
              <el-button size="small"
                         :type="itm.weekend === '星期六' || itm.weekend === '星期日' ? 'warning' : 'primary'"
                         plain>{{ itm.weekend }}
              </el-button>
              <!--              <el-button size="small" type="warning" plain>{{ itm.weekend }}</el-button>-->
            </div>
            <div class="staff-section">
              <div class="staffTag" v-if="itm.staffs && itm.staffs.length > 0">
                <el-tag
                  v-for="tag in itm.staffs"
                  :key="tag.userId"
                  closable
                  type="success"
                  @close="handleClose(tag.userId, itm.day)"
                  class="staff-tag">
                  {{ tag.userName ? tag.userName : '' }}
                </el-tag>
              </div>
              <div v-else>
                <el-button size="small" type="info" plain>未安排人员</el-button>
              </div>
            </div>
            <el-button @click="Addschedule(itm)" class="add-btn" size="small" :icon="Plus" :disabled="isHistoryDay(itm.day)"/>
          </div>
        </div>
      </div>

    </div>
    <!-- 表单弹窗：添加/修改 -->
    <Dialog  width="1010" v-model="dialogVisible" title="添加排班人员">
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="">
          <el-input v-model="userParams.username" placeholder="输入名称" clearable />
        </el-form-item>
        <el-form-item label="角色">
          <el-select
            v-model="userParams.roleId"
            placeholder="请选择角色"
            style="width: 200px"
          >
            <el-option
              v-for="item in roleList"
              :key="item.id"
              :label="item.name"
              :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <XButton title="查询" preIcon="ep:search" gradient @click="getDepartment" />
<!--          <el-button type="primary" @click="getDepartment">查询</el-button>-->
          <el-button  @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div>
        <el-table ref="tableRef" v-loading="loading" @selection-change="tableSelection" :data="userList">
          <el-table-column type="selection"  align="left" width="50"/>
          <el-table-column label="昵称" align="left" prop="nickname" />
          <el-table-column label="角色" align="left" prop="roleName"  />
          <el-table-column label="单位" align="left" prop="deptName" />
        </el-table>
        <div class="schedule-selection">
          <div style="display: flex; align-items: center; margin-bottom: 16px;">
            <span style="margin-right: 10px;">排班日选择:</span>
            <el-select
              v-model="checkedDays"
              multiple
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="5"
              placeholder="请选择排班日期"
              style="width: 700px"
              @change="handleCheckedDaysChange"
            >
              <template #header>
                <div class="select-header">
                  <el-checkbox
                    v-model="checkAll"
                    :indeterminate="isIndeterminate"
                    @change="handleCheckAllChange"
                  >
                    {{queryParams.month +"月"}}
                  </el-checkbox>
                </div>
              </template>
              <el-option
                v-for="day in UserScheduling"
                :key="day.value"
                :label="`${day.label} ${day.weekend}`"
                :value="day.value"
              />
            </el-select>
          </div>
        </div>
      </div>
      <template #footer>
        <div style="text-align: center">
          <XButton title="确定" width="200px" gradient @click="addType" />
<!--          <el-button  type="primary" @click="addType">确 定</el-button>-->
          <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
        </div>
      </template>
    </Dialog>
    <ImportDictionary ref="importFormRef"  @success="getList"  />
  </ContentWrap>

</template>
<script lang="ts" setup>
import {ArrowDown} from '@element-plus/icons-vue'
import {DICT_TYPE, getIntDictOptions} from '@/utils/dict'
import {
  Plus

} from '@element-plus/icons-vue'
import download from '@/utils/download'
import * as PostApi from '@/api/system/WorkforceManagement'
import {useRoute, useRouter} from 'vue-router'
import {dateFormatter} from "@/utils/formatTime";
import {addScheduling, exportScheduling, getUserList,DictionaryImport} from "@/api/system/WorkforceManagement";
import {getRolePage} from '@/api/system/role'
defineOptions({name: 'SystemPost'})
const currentYear = new Date().getFullYear();
import ImportDictionary from './components/ImportDictionary.vue'

const message = useMessage() // 消息弹窗
const {t} = useI18n() // 国际化
const route = useRoute()
const router = useRouter()
const loading = ref(true) // 列表的加载中
const formLoading = ref(false) // 列表的加载中
const roleList = ref([]) // 角色列表
const SelectionList = ref([]) // 弹窗选中列表
const currentSelect = ref(currentYear) // 列表的总页数
const yearList = ref([]) // 年份列表
const contentList = ref([]) // 排班列表
const userList = ref([]) // 用户列表
const deptId = ref('') // 用户列表
const UserScheduling = ref([]) // 用户列表
const radio1 = ref('1')
const list = ref([]) // 列表的数据
const dialogVisible = ref(false) // 列表的数据
// 添加全选相关的响应式变量
const checkAll = ref(false)
const isIndeterminate = ref(false)
const checkedDays = ref<string[]>([])
const queryParams = ref({
  deptId: Number(route.query.id),
  year: currentYear,
  month: '1'
})
const userParams = ref({
  deptId: Number(route.query.id),
  orgAccessStatus: 1,
  pageNo: 1,
  pageSize: '1000',
  roleId: '',
  username: ''
})
const formInline = reactive({
  user: '',
  region: '',
  date: '',
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
/** 排班详情 */
const getList = async () => {
  loading.value = true
  try {
    const res = await PostApi.getDetails(queryParams.value)
    console.log('获取排班数据成功:', res)
    // 确保返回的数据被正确赋值给 contentList
    contentList.value = res.map(item => ({
      id: item.id,
      day: item.day,
      weekend: item.weekend,
      staffs: item.staffs || [] // 如果 staffs 不存在，默认为空数组
    }))
    UserScheduling.value=generateMonthDays(queryParams.value.year, Number(queryParams.value.month))
    // console.log('排班数据:', contentList.value)
  } catch (error) {
    console.error('获取排班数据失败:', error)
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}
let tableRef= ref()
/** 按钮添加操作 */
const Addschedule = (value:any) => {
  checkedDays.value = []
  checkedDays.value.push(value.day.toString())
  isIndeterminate.value = true
  console.log('添加排班人员', value)
  dialogVisible.value = true
  nextTick(() => {
    if (value.staffs && value.staffs.length > 0) {
      const selectedRows = userList.value.filter(user =>
        value.staffs.some(staff => staff.userId === user.id)
      )
      // 更新表格选中状态
      selectedRows.forEach(row => {
        tableRef.value?.toggleRowSelection(row, true)
      })
      SelectionList.value = selectedRows
    }
  })
}

/** 排班人员关闭按钮 */
const handleClose = async (userId: string, day: string) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PostApi.DeleteSchedule({userId, days:day, month: queryParams.value.month, year: queryParams.value.year})
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {
  }

}


/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PostApi.deletePost(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {
  }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await PostApi.exportScheduling(queryParams.value)
    download.excel(data, '排班列表.xls')
  } catch {

  } finally {
    exportLoading.value = false
  }
}

const getYear = () => {

  // 创建一个数组来存储年份
  const years = [];

// 循环从当前年份到前五年
  for (let i = 0; i <= 5; i++) {
    const year = currentYear - i;
    years.push(
       {
        label: `${year}年`,
        value: year
      }
    );
    // years.push(currentYear - i);
  }
  return years;
}

//获取部门人员
const getDepartment = async () => {
  try {
    const res = await PostApi.getUserList(userParams.value)
    userList.value = res.list
  } catch (error) {
    console.log(error)
  }
}

const onReset = ()=>{
  userParams.value = {
    deptId: Number(route.query.id),
    orgAccessStatus: 1,
    pageNo: 1,
    pageSize: '1000',
    roleId: '',
    username: ''
  }
  getDepartment()
}

//获取角色列表
const getroleList = async () => {
  try {
    const res = await getRolePage({ pageNo: 1, pageSize: 1000,  orgAccess: 1})
    roleList.value = res.list
  } catch (error) {
    console.log(error)
  }
}

//用户选中数据
const tableSelection = (val:any)=>{
  SelectionList.value = val

}

//添加排班人员按钮
const addType = async ()=> {

  let userIds = SelectionList.value.map(item => ({
    userId: item.id,
    userName: item.username
  }))
  let addparms = {
    "staffs": userIds,
    "day": checkedDays.value,
    "deptId": deptId.value,
    "year": queryParams.value.year,
    "month": queryParams.value.month,
  }

  if (addparms.day.length ===0){
    message.error('请选择日期')
    return
  }
  try {
    await PostApi.addScheduling(addparms)
    message.success('新增成功')
    dialogVisible.value = false
    SelectionList.value = []
    getList()

  }catch {
    message.error('添加失败')
  }

}

/** 生成指定年月的日期数据 */
const generateMonthDays = (year: number, month: number) => {
  const daysInMonth = new Date(year, month, 0).getDate();
  const days = [];

  const now = new Date();
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month - 1, day);
    const weekday = date.getDay();
    const weekdayMap = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    if (date>now){
      days.push({
        // value: day.toString().padStart(2, '0'),
        value: day.toString(),
        label: `${month}月${day}日`,
        day: day,
        weekend: weekdayMap[weekday],
        checked: false
      });
    }
   }
   console.log('生成的日期数据', days)
  return days;
}
// 处理全选变化
const handleCheckAllChange = (val: boolean) => {
  if (val) {
    checkedDays.value = UserScheduling.value.map(item => item.value)
  } else {
    checkedDays.value = []
  }
  isIndeterminate.value = false
}

// 处理单个选择变化
const handleCheckedDaysChange = (value: string[]) => {
  const checkedCount = value.length
  const totalCount = UserScheduling.value.length

  checkAll.value = checkedCount === totalCount
  isIndeterminate.value = checkedCount > 0 && checkedCount < totalCount
}

//判断是否是历史日期
const isHistoryDay =(day:number)=>{
  const date = queryParams.value.year+'-'+queryParams.value.month+'-'+day;
  const currentDate = new Date();
  const targetDate = new Date(date);
  return currentDate >= targetDate;
}

/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}

/** 初始化 **/
onMounted(() => {
  if (route.query.id) {
    deptId.value = Number(route.query.id)
    // let params = {
    //   deptId: Number(route.query.id),
    //   year: currentYear,
    //   month: '1'
    // }
    // console.log('qingqiu', params)
    getList()
    getDepartment()
    getroleList()

    console.log('UserScheduling', UserScheduling.value)

  }
  yearList.value = getYear()
})
</script>
<style scoped>
.select-header {
  padding: 8px 12px;
  //border-bottom: 1px solid #ebeef5;
}

/* 移除之前的 prefix 相关样式 */
:deep(.el-select .el-input__prefix) {
  display: none;
}

:deep(.el-select .el-input__wrapper) {
  padding-left: 12px;
}

.schedule-selection {
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
}

.month-checkbox {
  margin-bottom: 16px;
  padding: 8px 0;
  font-weight: 500;
}

.date-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.el-checkbox-group) {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 8px;
}

.date-item {
  margin: 0 !important;
  padding: 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: all 0.3s;
}

.date-item:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

:deep(.date-item .el-checkbox__input) {
  margin-right: 4px;
}

:deep(.date-item .el-checkbox__label) {
  font-size: 13px;
  color: #606266;
}

:deep(.date-item.is-checked) {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

:deep(.date-item.is-checked .el-checkbox__label) {
  color: var(--el-color-primary);
}

/* 周末样式特殊处理 */
.date-item[data-weekend="true"] :deep(.el-checkbox__label) {
  color: #f56c6c;
}


.schedule-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  gap: 15px;
  padding: 10px;
}

.contentStyle {
  background: #fff;
  border-radius: 4px;
  overflow: hidden; /* 确保内容不会溢出 */
}

.staff-section {

  .staffTag {
    display: flex;
    align-items: center;
    margin-top: 3px;
    width: 200px;
    height: 50px;
    overflow: auto;
  }
}

.schedule-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  gap: 12px;
}

.left-section {
  display: flex;
  gap: 8px;
  min-width: 140px;
}

.middle-section {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.right-section {
  display: flex;
  align-items: center;
}

.staff-tag {
  margin: 2px;
  display: flex;
  align-items: center;

  //overflow: hidden;
}

.day-btn {
  min-width: 60px;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 3px;
}

/* 鼠标悬停时滚动条的样式 */
::-webkit-scrollbar-thumb:hover {
  background: #909399;
}


.custom-radio-button {
  margin-right: 18px;
  margin-bottom: 8px;
  border-radius: 4px !important;
}

.contentStyle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 2px solid #ebeef5;
  width: 414px;
  height: 50px;
}

.custom-radio-button :deep(.el-radio-button__inner) {
  border-radius: 4px !important;
}

/* 移除 Element Plus 默认的左边框移除样式 */
.custom-radio-button:not(:first-child) :deep(.el-radio-button__inner) {
  border-left: 1px solid var(--el-border-color);
}

/* 移除 Element Plus 默认的圆角处理 */
.custom-radio-button:first-child :deep(.el-radio-button__inner),
.custom-radio-button:last-child :deep(.el-radio-button__inner) {
  border-radius: 4px !important;
}
</style>
