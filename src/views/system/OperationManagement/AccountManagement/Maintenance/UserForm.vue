<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="账户" prop="username">
            <el-input v-model="formData.username" placeholder="请输入用户账户" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="昵称" prop="nickname">
            <el-input v-model="formData.nickname" maxlength="11" placeholder="昵称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="formData.mobile" maxlength="50" placeholder="手机号" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="角色" prop="roleIds">
            <el-select v-model="formData.roleIds"  multiple placeholder="请选择">
              <el-option
                v-for="item in postList"
                :key="item.id"
                :label="item.name"
                :value="item.id!"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="组织" prop="deptId">
            <el-cascader style="width: 50%;" :show-all-levels="false" :options="unitList" :props="props1" v-model="formData.deptId" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :value=0 size="large">启用</el-radio>
              <el-radio :value=1 size="large">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div style="text-align: center;">
        <el-button :disabled="formLoading" class="gradient-btn" style="width: 200px;" type="primary" @click="submitForm">确 定</el-button>
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
      </div>

    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { CommonStatusEnum } from '@/utils/constants'
import { defaultProps, handleTree } from '@/utils/tree'
import * as PostApi from '@/api/system/post'
import * as DeptApi from '@/api/system/dept'
import * as UserApi from '@/api/system/user'
import { FormRules } from 'element-plus'
import {getRoleList} from "@/api/system/post";

defineOptions({ name: 'SystemUserForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const props1 = {
  checkStrictly: true,
  value: 'id',
  label: 'name',
  children: 'children',
  emitPath: false
}
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  nickname: '',
  deptId: '',
  mobile: '',
  email: '',
  id: undefined,
  username: '',
  password: '',
  sex: undefined,
  postIds: [],
  remark: '',
  status: CommonStatusEnum.ENABLE,
  roleIds: []
})
const formRules = reactive<FormRules>({
  username: [{ required: true, message: '用户账户不能为空', trigger: 'blur' }],
  nickname: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
  roleIds: [{ required: true, message: '用户角色不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  deptId: [{ required: true, message: '组织不能为空', trigger: 'blur' }],
  mobile: [
    {
      required: true,
      pattern: /^(?:(?:\+|00)86)?1(?:3[\d]|4[5-79]|5[0-35-9]|6[5-7]|7[0-8]|8[\d]|9[189])\d{8}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur'
    }
  ]
})
const formRef = ref() // 表单 Ref
const deptList = ref<Tree[]>([]) // 树形结构
const postList = ref([] as PostApi.PostVO[]) // 岗位列表

/** 打开弹窗 */
const open = async (type: string, id?: number,currentTypeId:number) => {
  // 加载角色列表
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await UserApi.getUser(id)
      // 找到对应的角色名称
      const role = postList.value.find(item => item.id === formData.value.orgAccess)
      if (role) {
        formData.value.orgAccess = role.id
      }
    } finally {
      formLoading.value = false
    }
  }
  queryParams.typeId = currentTypeId;
  const data = await DeptApi.copegetDeptPage(queryParams)
  unitList.value = handleTree(data)
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as UserApi.UserVO
    if (formType.value === 'create') {
      await UserApi.createUser(data)
      message.success(t('common.createSuccess'))
    } else {
      await UserApi.updateUser(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success',formType.value)
  } finally {
    formLoading.value = false
  }
}
const queryParams = reactive({
  pageNo: 1,
  pageSize: 100,
  name: undefined,
  status: undefined
})
let unitList = ref<Tree[]>([])
onMounted(async () => {

  postList.value = await PostApi.getRoleList({orgAccess: 1})
})

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    nickname: '',
    deptId: '',
    mobile: '',
    email: '',
    id: undefined,
    username: '',
    password: '',
    sex: undefined,
    postIds: [],
    remark: '',
    status: CommonStatusEnum.ENABLE,
    roleIds: []
  }
  formRef.value?.resetFields()
}
</script>
<style lang="scss" scoped >
.gradient-btn {
background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
border: none !important;
font-size: 14px !important;
padding: 9px 16px !important;
color: #ffffff !important;

&:hover {
background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
opacity: 0.8;
}

&:active {
background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
opacity: 0.9;
}

// 覆盖 element-plus 的默认样式
&.el-button--primary {
--el-button-hover-bg-color: transparent;
--el-button-active-bg-color: transparent;
}
}
</style>
