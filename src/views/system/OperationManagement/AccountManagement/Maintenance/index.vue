<template>
  <el-row :gutter="20">
    <!-- 左侧部门树 -->
    <el-col :span="4" :xs="24">
      <ContentWrap class="h-1/1" style="background: rgb(241 243 246)">
        <DeptTree
          :typeId="currentTypeId"
          :id="currentId"
          :activeTab="currentTab"
          @node-click="handleDeptNodeClick"
        />
      </ContentWrap>
    </el-col>
    <el-col :span="20" :xs="24">
      <!-- 搜索 -->
      <ContentWrap>
        <el-form
          class="flex flex-wrap items-start -mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
        >
          <el-form-item label="" prop="username" class="!mr-3">
            <el-input
              v-model="queryParams.username"
              placeholder="输入账户/昵称/手机号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="角色" prop="mobile" class="!mr-3">
            <el-select class="!w-240px" v-model="queryParams.roleId" placeholder="请选择">
              <el-option
                v-for="item in postList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status" class="!mr-3">
            <el-select
              v-model="queryParams.status"
              placeholder="用户状态"
              clearable
              class="!w-240px"
            >
              <el-option
                v-for="dict in statusList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
            <div>
              <XButton title="查询" preIcon="ep:search" gradient @click="handleQuery" />
              <el-button @click="resetQuery">
                <Icon icon="ep:refresh" />
                重置
              </el-button>
            </div>
            <div>
              <el-button plain @click="openForm('create')" v-hasPermi="['system:user:create']">
                <Icon icon="ep:plus" />
                新增
              </el-button>
              <el-button plain @click="handleImport" v-hasPermi="['system:user:import']">
                <Icon icon="ep:upload" />
                导入
              </el-button>
              <el-button
                plain
                @click="handleExport"
                :loading="exportLoading"
                v-hasPermi="['system:user:export']"
              >
                <Icon icon="ep:download" />
                导出
              </el-button>
              <el-button
                plain
                @click="DisableEnable('0')"
                :loading="exportLoading"
                v-hasPermi="['system:user:export']"
              >
                <Icon icon="ep:switch-button" />
                启用
              </el-button>
              <el-button
                plain
                @click="DisableEnable('1')"
                :loading="exportLoading"
                v-hasPermi="['system:user:export']"
              >
                <Icon icon="ep:circle-close" />
                禁用
              </el-button>
            </div>
          </div>
        </el-form>
      </ContentWrap>

      <ContentWrap>
        <el-table
          ref="tableRef"
          @selection-change="checkList"
          @select-all="selectAll"
          v-loading="loading"
          :data="list"
          @select="selectOne"
        >
          <el-table-column label="序号" type="selection" align="left" width="55" />

          <el-table-column label="序号" type="index" align="left" width="55" />
          <el-table-column
            label="账户"
            align="left"
            prop="username"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="昵称"
            align="left"
            prop="nickname"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="手机号码" align="left" prop="mobile" />
          <el-table-column
            label="角色"
            align="left"
            key="deptName"
            prop="roleName"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="单位" align="left" prop="deptName" width="120" />
          <el-table-column label="状态" key="status">
            <template #default="scope">
              <el-switch
                v-model="scope.row.status"
                :active-value="0"
                :inactive-value="1"
                inline-prompt
                active-text="已启用"
                inactive-text="已禁用"
                @change="handleStatusChange(scope.row)"
                :disabled="!checkPermi(['system:user:update'])"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="left" width="160">
            <template #default="scope">
              <div class="flex items-center justify-center">
                <el-button
                  type="primary"
                  link
                  @click="openForm('update', scope.row.id)"
                  v-hasPermi="['system:user:update']"
                >
                  <Icon icon="ep:edit" />
                  修改
                </el-button>
                <el-dropdown
                  @command="(command) => handleCommand(command, scope.row)"
                  v-hasPermi="[
                    'system:user:delete',
                    'system:user:update-password',
                    'system:permission:assign-user-role'
                  ]"
                >
                  <el-button type="primary" link>
                    <Icon icon="ep:d-arrow-right" />
                    更多
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        command="handleDelete"
                        v-if="checkPermi(['system:user:delete'])"
                      >
                        <Icon icon="ep:delete" />
                        删除
                      </el-dropdown-item>
                      <el-dropdown-item
                        command="handleResetPwd"
                        v-if="checkPermi(['system:user:update-password'])"
                      >
                        <Icon icon="ep:key" />
                        重置密码
                      </el-dropdown-item>
                      <el-dropdown-item
                        command="handleRole"
                        v-if="checkPermi(['system:permission:assign-user-role'])"
                      >
                        <Icon icon="ep:circle-check" />
                        分配角色
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>

    <Dialog v-model="dialogVisible" :title="dialogTitle">
      <div>用户添加成功，初始密码为"Wx123456!@#" 请尽快登录系统进行修改</div>

      <template #footer>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </template>
    </Dialog>
  </el-row>
  <!-- 添加或修改用户对话框 -->
  <UserForm ref="formRef" @success="openPop" />
  <!-- 用户导入对话框 -->
  <UserImportForm ref="importFormRef" @success="getList" />
  <!-- 分配角色 -->
  <UserAssignRoleForm ref="assignRoleFormRef" @success="getList" />
</template>
<script lang="ts" setup>
import { checkPermi } from '@/utils/permission'
import download from '@/utils/download'
import { CommonStatusEnum } from '@/utils/constants'
import { ref, reactive, onMounted, defineProps, nextTick } from 'vue'
import * as UserApi from '@/api/system/user'
import UserForm from './UserForm.vue'
import UserImportForm from './UserImportForm.vue'
import UserAssignRoleForm from './UserAssignRoleForm.vue'
import DeptTree from './DeptTree.vue'
import * as DeptApi from '@/api/system/dept'
import * as PostApi from '@/api/system/post'

defineOptions({ name: 'SystemUser' })
const props = defineProps({
  activeTab: {
    type: [String, Number],
    required: true
  },
  typeId: {
    type: [String, Number],
    required: true
  },
  id: {
    type: [String, Number],
    required: true
  }
})
// 添加加载状态标记，防止重复加载
const roleListLoaded = ref(false)
const currentTab = computed(() => props.activeTab)
const currentTypeId = computed(() => props.typeId)
const currentId = computed(() => props.id)
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
let dataUnit = ref([])
let selectionList = ref([])
let dialogTitle = ref('')
let dialogVisible = ref(false)
let postList = ref([])
let statusList = ref([
  {
    label: '启用',
    value: '0'
  },
  {
    label: '禁用',
    value: '1'
  }
])
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orgAccessStatus: '1',
  username: undefined,
  mobile: undefined,
  status: undefined,
  deptId: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
let selectIdList = []

/** 查询列表 */
const getList = async () => {
  if (!queryParams.deptId) {
    loading.value = false
    return
  }

  loading.value = true
  try {
    queryParams.userIdList = []
    const data = await UserApi.getUserPageShowClose(queryParams)
    list.value = data.list || []
    total.value = data.total || 0

    // 等待 DOM 更新后再处理选中状态
    await nextTick()
    if (tableRef.value) {
      // 遍历表格数据，找到匹配ID的行并选中
      list.value.forEach((row) => {
        if (selectIdList.includes(row.id)) {
          tableRef.value.toggleRowSelection(row, true)
        }
      })
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    message.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
/** 重置密码弹窗 */
const openPop = (value) => {
  if (value == 'create') {
    dialogVisible.value = true
    dialogTitle.value = '用户添加成功'
  }
  getList()
}

let tableRef = ref()
/** 启用禁用按钮 */
const DisableEnable = async (type: string) => {
  if (selectionList.value.length < 1) {
    message.warning('请选择要操作的数据')
    return
  }
  try {
    // 修改状态的二次确认
    const text = type == '0' ? '启用' : '停用'
    await message.confirm('确认要"' + text + '""' + '这' + selectionList.value.length + '个用户吗?')
    // 发起修改状态
    const ids = selectionList.value.map((item) => item.id)
    await UserApi.updateUserStatus([...ids], type)
    // 刷新列表
    await getList()
  } catch {
    tableRef.value?.clearSelection()
    // 取消后，进行恢复按钮
  }
}
/** 表格选中数据 */
const checkList = (value: any) => {
  selectionList.value = value || []
}

/** 重置按钮操作 */
const resetQuery = () => {
  selectIdList = []
  queryFormRef.value?.resetFields()
  queryParams.roleId = ''
  handleQuery()
}

/** 表格选中数据 */
const selectAll = (selection: any) => {
  // 先删除当前页所有
  for (let valueElement of list.value) {
    selectIdList = selectIdList.filter((item) => item !== valueElement.id)
  }
  // 添加选中的
  if (selection && selection.length > 0) {
    selection.forEach((temp) => {
      if (temp && temp.id) {
        selectIdList.push(temp.id)
      }
    })
  }
}

/** 处理部门被点击 */
const handleDeptNodeClick = async (row) => {
  if (row) {
    queryParams.deptId = row.id
    await getList()
  } else {
    loading.value = false
    list.value = []
    total.value = 0
  }
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id, currentTypeId.value)
}

/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}

/** 修改用户状态 */
const handleStatusChange = async (row: UserApi.UserVO) => {
  try {
    // 修改状态的二次确认
    const text = row.status === CommonStatusEnum.ENABLE ? '启用' : '停用'
    await message.confirm('确认要"' + text + '""' + row.username + '"用户吗?')
    // 发起修改状态
    await UserApi.updateUserStatus([row.id], row.status)
    // 刷新列表
    await getList()
  } catch {
    // 取消后，进行恢复按钮
    row.status =
      row.status === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE : CommonStatusEnum.ENABLE
  }
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    queryParams.userIdList = selectIdList
    const data = await UserApi.exportUserOam(queryParams)
    download.excel(data, '用户数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 操作分发 */
const handleCommand = (command: string, row: UserApi.UserVO) => {
  switch (command) {
    case 'handleDelete':
      handleDelete(row.id)
      break
    case 'handleResetPwd':
      handleResetPwd(row)
      break
    case 'handleRole':
      handleRole(row)
      break
    default:
      break
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await UserApi.deleteUser(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 重置密码 */
const handleResetPwd = async (row: UserApi.UserVO) => {
  try {
    // 重置的二次确认
    const result = await message.prompt(
      '请输入"' + row.username + '"的新密码',
      t('common.reminder')
    )
    const password = result.value
    //密码正则校验
    const regex = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[\W_]).{8,}$/
    if (!regex.test(password)) {
      ElMessage.error('密码必须包含字母、数字、特殊字符，且至少8位')
      return // 校验不通过，打断执行
    }
    //校验通过，继续请求接口
    // 发起重置
    await UserApi.resetUserPwd(row.id, password)
    message.success('修改成功，新密码是：' + password)
  } catch {}
}

/** 分配角色 */
const assignRoleFormRef = ref()
const handleRole = (row: UserApi.UserVO) => {
  assignRoleFormRef.value.open(row)
}

// 获取角色列表的方法
const getRoleList = async () => {
  if (roleListLoaded.value) return // 如果已经加载过，直接返回
  try {
    postList.value = await PostApi.getRoleList({ orgAccess: 1 })
    roleListLoaded.value = true
  } catch (error) {
    console.error('获取角色列表失败:', error)
    message.error('获取角色列表失败')
  }
}

// 表格选中数据
const selectOne = (selection: any[], row: any) => {
  if (!row || !row.id) return

  // 判断是取消勾选还是勾选
  let checked = false
  if (selection && selection.length > 0) {
    selection.forEach((r) => {
      if (r && r.id === row.id) {
        checked = true
      }
    })
  }

  if (checked) {
    selectIdList.push(row.id)
  } else {
    selectIdList = selectIdList.filter((item) => item !== row.id)
  }
}
/** 初始化 */
onMounted(async () => {
  try {
    // 获取部门数据
    dataUnit.value = await DeptApi.copegetDeptPage(queryParams)
    // 获取角色列表
    await getRoleList()
  } catch (error) {
    console.error('初始化数据失败:', error)
    message.error('初始化数据失败')
  }
})
</script>
