<template>

  <el-tabs  v-if="tabsList.length>0" v-model="activeName" class="demo-tabs" @tab-click="handleClick">
    <el-tab-pane :label="itm.typeName" :name="itm.id" :key="itm.id" v-for="itm in tabsList">
      <keep-alive :include="['Maintenance']">
        <Maintenance
          v-if="activeName === itm.id"
          :key="activeName"
          :active-tab="activeName"
          :typeId="Number(itm.id)"
          :id="id"
        />
      </keep-alive>
    </el-tab-pane>
  </el-tabs>

</template>
<script lang="ts" setup>
import { ref, onMounted,nextTick , } from 'vue'
import Maintenance from './Maintenance/index.vue';
import * as UserApi from "@/api/system/user";
import { useRoute } from 'vue-router';
import router from "@/router";
defineOptions({ name: 'SystemUser' })
let tabsList=ref([])
const route = useRoute();
const activeName = ref('1') // 列表的加载中
const typeId = ref('') // 列表的加载中
const id = ref('') // 列表的加载中
const handleClick = (tab: any, event: Event) => {
  nextTick(() => {
    console.log('当前选中的标签页:', typeof activeName.value)
  })
}
/** 初始化 */
// onMounted(async() => {
//   tabsList.value = await UserApi.getTypedropdown()
//   nextTick(() => {
//     if(route.query.typeId && route.query.id){
//       id.value = route.query.id
//       typeId.value = route.query.typeId
//       activeName.value = Number(route.query.typeId)
//       console.log('选中的值', typeof activeName.value)
//     }else{
//       activeName.value = tabsList.value[0].id
//     }
//   })
// })
onMounted(async() => {
  tabsList.value = await UserApi.getTypedropdown("1")
  nextTick(() => {
    if(route.query.typeId && route.query.id){
      id.value = route.query.id
      activeName.value = Number(route.query.typeId)
    }else{
      activeName.value = tabsList.value[0].id
    }
  })
})

</script>
