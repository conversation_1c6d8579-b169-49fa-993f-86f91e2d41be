<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="flex flex-wrap items-start -mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
    >
      <el-form-item label="类型名称" prop="typeName" class="!mr-3">
        <el-input
          v-model="queryParams.typeName"
          placeholder="请输入类型名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
        <div>
          <XButton title="查询" preIcon="ep:search" gradient @click="handleQuery" />
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        </div>
        <div>
          <el-button
            plain
            @click="openForm('create')"
            v-hasPermi="['system:type:create']"
          >
            <Icon icon="ep:plus" class="mr-5px" /> 新增
          </el-button>
        </div>
      </div>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="单位类型" align="left" prop="typeName" />
      <el-table-column label="单位数量" align="left" prop="count">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="jumpTo(scope.row.id)"
          >
            {{scope.row.count }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="left" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            :disabled="scope.row.count > 0"
            v-if="scope.row.count <= 0"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <Dialog  v-model="dialogVisible" :title="dialogTitle">
    <el-form
      class="-mb-15px"
      :model="Params"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="类型名称" prop="typeName">
        <el-input
          v-model="Params.typeName"
          placeholder="请输入类型名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="text-align: center">
        <el-button :disabled="formLoading" class="gradient-btn" style="width: 200px;" type="primary" @click="addType">确 定</el-button>
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
      </div>

    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
// import download from '@/utils/download'
import {addTypePage, getTypePage, updateTypePage,deleteType} from '@/api/system/type/index'
// import TypeForm from './TypeForm.vue'
import { ref, reactive, } from 'vue'
import { useRouter } from 'vue-router'
/** 单位类型 列表 */
defineOptions({ name: 'Type' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()
const loading = ref(true) // 列表的加载中
const formLoading = ref(false) // 弹窗的确定按钮加载中状态
const dialogVisible = ref(false) // 弹窗的显示状态
const dialogTitle = ref(null) // 弹窗的标题
const list = ref<TypeVO[]>([]) // 列表的数据
const total = ref(10) // 列表的总页数

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  typeName: undefined,
})
const Addtype = ref()


const Params = reactive({
  typeName: undefined,
  id: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await getTypePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 单位数量跳转 */
const jumpTo = async (id:number) => {

  router.push({ path: 'UnitList', query: { id } })
}

const addType = async () => {
  if (Addtype.value === 'create') {
    try {
      const data = await addTypePage(Params)
      message.success('操作成功')
      dialogVisible.value = false
      await getList()

    } finally {

    }
  }else{
    try {
      const data = await updateTypePage(Params)
      message.success(' 更新成功')
      dialogVisible.value = false
      await getList()

    } finally {

    }
  }

}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  // queryFormRef.value.resetFields()
  queryParams.typeName= undefined
  handleQuery()
}

/** 添加/修改操作 */
const openForm = (type: string, id?: any) => {
  Addtype.value = type
  if (type === 'create') {
    Params.typeName = undefined
    Params.id = undefined
  } else {
    list.value.forEach((item) => {
      if (item.id === id) {
        Params.typeName = item.typeName
        Params.id = item.id
      }
    })
  }
  dialogTitle.value = t('action.' + type)
  dialogVisible.value = true
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await deleteType(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}


/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
<style lang="scss" scoped >
.gradient-btn {
  background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
  border: none !important;
  font-size: 14px !important;
  padding: 9px 16px !important;
  color: #ffffff !important;

  &:hover {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.8;
  }

  &:active {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.9;
  }

  // 覆盖 element-plus 的默认样式
  &.el-button--primary {
    --el-button-hover-bg-color: transparent;
    --el-button-active-bg-color: transparent;
  }
}
</style>
