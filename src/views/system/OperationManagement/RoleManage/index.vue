<template>
  <doc-alert title="功能权限" url="https://doc.iocoder.cn/resource-permission" />
  <doc-alert title="数据权限" url="https://doc.iocoder.cn/data-permission" />

  <ContentWrap>
    <!-- 搜索工作栏 -->

    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="flex flex-wrap items-start -mb-15px"
    >
      <el-form-item label="" prop="name" class="!mr-3">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="请输入角色名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status" class="!mr-3">
        <el-select v-model="queryParams.status" class="!w-240px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
        <div>
          <XButton title="查询" preIcon="ep:search" gradient @click="handleQuery" />
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />
            重置
          </el-button>
        </div>
        <div>
          <el-button
            v-hasPermi="['system:role:create']"
            plain
            @click="openForm('create')"
          > <Icon icon="ep:plus" />
            新增
          </el-button>
          <el-button
            v-hasPermi="['system:role:create']"
            plain
            @click="onState('Enable')"
          >
            <Icon icon="ep:switch-button" />
            启用
          </el-button>
          <el-button
            v-hasPermi="['system:role:create']"
            plain
            @click="onState('Disable')"
          >
            <Icon icon="ep:circle-close" />
            禁用
          </el-button>
          <!--          :icon="Delete"-->
          <el-button
            v-hasPermi="['system:role:create']"
            plain
            @click="handleDelete('create')"
          >
            删除
          </el-button>
        </div>
      </div>
    </el-form>

  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table ref="myTable" v-loading="loading" @selection-change="selectionChange" :data="list">
      <el-table-column align="left" type="selection" width="55" />
      <el-table-column type="index" label="序号" width="55" align="left">
        <template #default="scope">
          <span>{{ (queryParams.pageNo - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column align="left" label="角色编码" prop="code" />
      <el-table-column align="left" label="角色名称" prop="name" />
      <el-table-column align="left" label="状态" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :width="300" align="left" label="操作">
        <template #default="scope">
          <el-button
            v-hasPermi="['system:role:update']"
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            编辑
          </el-button>
          <el-button
            v-hasPermi="['system:permission:assign-role-menu']"
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openAssignMenuForm(scope.row)"
          >
            功能权限
          </el-button>

          <el-button
            v-if="scope.row.status === 0"
            v-hasPermi="['system:role:delete']"
            @click="SingleState('Disable', scope.row)"
            link
            type="primary"
          >
            禁用
          </el-button>
          <el-button
            v-if="scope.row.status === 1"
            v-hasPermi="['system:role:delete']"
            link
            @click="SingleState('ccDisable', scope.row)"
            type="primary"
          >
            启用
          </el-button>
          <!--          <el-button-->
          <!--            v-hasPermi="['system:role:delete']"-->
          <!--            link-->
          <!--            type="danger"-->
          <!--            @click="handleDelete(scope.row.id)"-->
          <!--          >-->
          <!--            删除-->
          <!--          </el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <RoleForm ref="formRef" @success="getList" />
  <!-- 表单弹窗：菜单权限 -->
  <RoleAssignMenuForm ref="assignMenuFormRef" @success="getList" />
  <!-- 表单弹窗：数据权限 -->
  <RoleDataPermissionForm ref="dataPermissionFormRef" @success="getList" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import * as RoleApi from '@/api/system/role'
import RoleForm from './RoleForm.vue'
import RoleAssignMenuForm from './RoleAssignMenuForm.vue'
import RoleDataPermissionForm from './RoleDataPermissionForm.vue'
import { modifyStatus } from '@/api/system/role'
import {Delete} from "@element-plus/icons-vue";

defineOptions({ name: 'SystemRole' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const selectionList = ref([]) // 列表选中的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined,
  createTime: [],
  orgAccess: 1
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询角色列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await RoleApi.getRolePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 表格选中数据 */
const selectionChange = (value) => {
  selectionList.value = value
  console.log('选中数据', selectionList.value)
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 数据权限操作 */
const dataPermissionFormRef = ref()
const openDataPermissionForm = async (row: RoleApi.RoleVO) => {
  dataPermissionFormRef.value.open(row)
}

/** 菜单权限操作 */
const assignMenuFormRef = ref()
const openAssignMenuForm = async (row: RoleApi.RoleVO) => {
  assignMenuFormRef.value.open(row)
}

const myTable = ref()
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  if (selectionList.value.length < 1) {
    message.warning('请选择要删除的角色！')
  } else {
    const ids = selectionList.value.map((item) => item.id).join(',')
    console.log('sssss', ids)
    try {
      // 删除的二次确认
      await message.delConfirm()
      // 发起删除
      await RoleApi.deleteRole(ids)
      message.success(t('common.delSuccess'))
      // 刷新列表
      await getList()
    } catch {
      myTable.value.clearSelection()
    }
  }
}
//批量修改操作
const onState = async (state: string) => {
  if (selectionList.value.length < 1) {
    message.warning('请选择要操作的角色！')
  } else {
    // const ids = selectionList.value.map((item) => item.id).join(',')
    let array = []
    selectionList.value.forEach((item) => {
      array.push({
        id: item.id,
        code: item.code,
        status: state == 'Enable' ? '0' : '1',
        sort: '0',
        orgAccess: item.orgAccess
      })
    })
    try {
      // 操作的二次确认
      await message.confirm('你确定要操作所选角色状态吗？')

      // 发起删除
      await RoleApi.modifyStatus(array)
      message.success('操作成功！')
      // 刷新列表
      await getList()
    } catch {
      myTable.value.clearSelection()
    }
  }
}

//单个状态修改
const SingleState = async (type: string, row: any) => {
  console.log('sssss', row)
  let parms = { ...row, status: type == 'Disable' ? '1' : '0' }
  try {
    // 操作的二次确认
    await message.confirm('你确定要操作该角色状态吗？')
    // 发起删除
    await modifyStatus([parms])
    message.success('操作成功！')
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await RoleApi.exportRole(queryParams)
    download.excel(data, '角色列表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
