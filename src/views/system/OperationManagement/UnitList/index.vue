<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      class="flex flex-wrap items-start -mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
    >
      <el-form-item label="" prop="name" class="!mr-3">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入单位名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="类型" prop="typeId" class="!mr-3">
        <el-select
          v-model="queryParams.typeId"
          placeholder="请选择类型"
          clearable
          @change="getList"
          class="!w-240px"
        >
          <el-option
            v-for="dict in OptionsList"
            :key="dict.id"
            :label="dict.typeName"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
        <div>
          <XButton title="查询" preIcon="ep:search" gradient @click="handleQuery" />
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        </div>
      </div>
      <div>
        <el-button
          plain
          @click="openForm('create')"
          v-hasPermi="['system:dept:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button  plain @click="toggleExpandAll">
          <Icon icon="ep:sort" class="mr-5px" /> 展开/折叠
        </el-button>
      </div>

    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      row-key="id"
      :default-expand-all="isExpandAll"
      v-if="refreshTable"
    >
      <el-table-column prop="name" label="单位名称" />
      <el-table-column prop="level" align="left" label="级别">
        <template #default="scope">
          {{ numberToChinese(scope.row.level) + '级' }}
        </template>
      </el-table-column>
      <el-table-column prop="typeName" align="left" label="类型" />
      <el-table-column prop="staffCount" align="left" label="成员数量">
        <template #default="scope">
          <el-button link @click="JumptoAccount(scope.row.typeId, scope.row.id)" type="primary">
            {{ scope.row.staffCount }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="left" width="330">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['system:dept:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('addUpdate', scope.row.id)"
            v-hasPermi="['system:dept:update']"
          >
            添加下级
          </el-button>
          <el-button
            link
            type="primary"
            @click="openPermissions('permissions', scope.row.id)"
            v-hasPermi="['system:dept:update']"
          >
            数据权限
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['system:dept:delete']"
            v-if="scope.row.staffCount == 0"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DeptForm ref="formRef" @success="getList" />
  <Configuration ref="ConformRef" @success="getList" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import DeptForm from './DeptForm.vue'
import Configuration from './Configuration.vue'
import * as UserApi from '@/api/system/user'
import router from '@/router'
import { useRoute } from 'vue-router'
import { getTypedropdown } from '@/api/system/user'

defineOptions({ name: 'SystemDept' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const Route = useRoute() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref() // 列表的数据
const OptionsList = ref([]) // 类型下拉的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 100,
  name: undefined,
  typeId: undefined,
  status: undefined
})
const queryFormRef = ref() // 搜索的表单
const isExpandAll = ref(true) // 是否展开，默认全部展开
const refreshTable = ref(true) // 重新渲染表格状态
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

/** 查询部门列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DeptApi.copegetDeptPage(queryParams)
    list.value = handleTree(data)
    console.log('树形数据', list.value)
  } finally {
    loading.value = false
  }
}

/** 展开/折叠操作 */
const toggleExpandAll = () => {
  refreshTable.value = false
  isExpandAll.value = !isExpandAll.value
  nextTick(() => {
    refreshTable.value = true
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.pageNo = 1
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  // 如果是添加下级操作，需要获取顶层ID
  if (type === 'addUpdate') {
    // 在树形数据中查找当前节点的顶层节点
    const findTopParent = (data: any[], targetId: number): number => {
      for (const item of data) {
        if (item.id === targetId) {
          return item.parentId === 0 ? item.id : findTopParent(list.value, item.parentId)
        }
        if (item.children) {
          const result = findTopParent(item.children, targetId)
          if (result) return result
        }
      }
      return 0
    }
    const topId = findTopParent(list.value, id!)
    formRef.value.open(type, id, topId)
  } else {
    formRef.value.open(type, id)
  }
  // formRef.value.open(type, id)
}

const openPermissions = (type: string, id?: number) => {
  router.push({
    path: 'Datapermissions',
    query: {
      id,
      name:'单位列表'
    }
  })
}

/** 成员数量跳转操作 */
const JumptoAccount = (typeId?: number, id?: number) => {
  router.push({
    path: 'AccountManagement',
    query: { id, typeId }
  })
}

/** 配置成员操作 */
const ConformRef = ref()
const openConform = (type: string, id?: number) => {
  ConformRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DeptApi.deleteDept(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(async () => {
  if (Route.query.id) {
    queryParams.typeId = Number(Route.query.id)
  }
  await getList()
  // 获取用户列表
  // userList.value = await UserApi.getSimpleUserList()
  OptionsList.value = await UserApi.getTypedropdown('1')
})

// 数字转中文
const numberToChinese = (num: number): string => {
  const chineseNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
  if (num <= 10) {
    return chineseNums[num]
  }
  if (num > 10 && num < 20) {
    return '十' + (num > 10 ? chineseNums[num - 10] : '')
  }
  const digit = Math.floor(num / 10)
  const remainder = num % 10
  return chineseNums[digit] + '十' + (remainder > 0 ? chineseNums[remainder] : '')
}
</script>
