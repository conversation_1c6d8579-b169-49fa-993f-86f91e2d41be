<template>
  <Dialog  width="60%" v-model="dialogVisible"  :title="dialogTitle">
   <div style="display:flex;justify-content: space-between;">
     <div style="display: flex; align-items: center; justify-content: center;">
       <el-input
         v-model="queryParams.name"
         placeholder="输入账号/昵称"
         clearable
         @keyup.enter="handleQuery"
       />
       <el-button style="margin-left: 10px;" type="primary">查询</el-button>
     </div>
     <div><el-checkbox v-model="queryParams.checked" label="仅显示无组织用户" size="large" /></div>
   </div>
    <el-table v-loading="loading" :data="list">
      <el-table-column
        label="账户"
        align="left"
        prop="username"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="昵称"
        align="left"
        prop="nickname"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="手机号码" align="left" prop="mobile" />
      <el-table-column
        label="角色"
        align="left"
        key="deptName"
        prop="deptName"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="当前组织" align="left" prop="mobile"  />
      <el-table-column label="序号" type="selection" align="left" width="55" />
    </el-table>
    <template #footer>
      <el-button type="primary" @click="submitForm">确认添加</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { defaultProps, handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import * as UserApi from '@/api/system/user'
import { CommonStatusEnum } from '@/utils/constants'
import { FormRules } from 'element-plus'
import {checkPermi} from "@/utils/permission";

defineOptions({ name: 'SystemDeptForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const queryParams = reactive({
  name: undefined
})
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const list = ref([{username:'1',nickname:'wuyanzu',}]) // 表格数据
const loading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  title: '',
  parentId: undefined,
  name: undefined,
  sort: undefined,
  leaderUserId: undefined,
  phone: undefined,
  email: undefined,
  status: CommonStatusEnum.ENABLE
})
const formRef = ref() // 表单 Ref
const deptTree = ref() // 树形结构
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id && type == 'addUpdate') {
    formLoading.value = true
    try {
      formData.value = await DeptApi.getDept(id)
      formData.value={
        ...formData.value,
        name:undefined
      }
    } finally {
      formLoading.value = false
    }
  } else if (id) {
    formLoading.value = true
    try {
      formData.value = await DeptApi.getDept(id)
    } finally {
      formLoading.value = false
    }
  }
  // 获得用户列表
  userList.value = await UserApi.getSimpleUserList()
  // 获得部门树
  await getTree()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DeptApi.DeptVO
    if (formType.value === 'create') {
       let  parms={
         parentId:'0',
         name:data.name,
         sort:'0',
         status:'0'
       }
      const  parmsList= parms as unknown as DeptApi.DeptVO
      await DeptApi.createDept(data)
      message.success(t('common.createSuccess'))
    } else {
      await DeptApi.updateDept(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    title: '',
    parentId: undefined,
    name: undefined,
    sort: undefined,
    leaderUserId: undefined,
    phone: undefined,
    email: undefined,
    status: CommonStatusEnum.ENABLE
  }
  formRef.value?.resetFields()
}

/** 获得部门树 */
const getTree = async () => {
  deptTree.value = []
  const data = await DeptApi.getSimpleDeptList()
  let dept: Tree = { id: 0, name: '顶级部门', children: [] }
  dept.children = handleTree(data)
  deptTree.value.push(dept)
}
const handleQuery = async () => {
  deptTree.value = []
  const data = await DeptApi.getSimpleDeptList()
  let dept: Tree = { id: 0, name: '顶级部门', children: [] }
  dept.children = handleTree(data)
  deptTree.value.push(dept)
}
</script>
