<template>
  <div class="head-container">
<!--    <el-input v-model="deptName" class="mb-20px" clearable placeholder="搜索组织">-->
<!--      <template #prefix>-->
<!--        <Icon icon="ep:search" />-->
<!--      </template>-->
<!--    </el-input>-->
  </div>
  <div class="head-container">
    <el-tree
      ref="treeRef"
      style="background: rgb(241 243 246)"
      :data="deptList"
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
      :props="defaultProps"
      default-expand-all
      highlight-current
      :current-node-key="defaultSelectedKey"
      node-key="id"
      @node-click="handleNodeClick"
    >
      <template #default="{ node, }">
        <span class="custom-tree-node">
          <Icon :icon="node.expanded ? 'ep:folder-opened' : 'ep:folder'" class="mr-4px" />
          <span>{{ node.label }}</span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script lang="ts" setup>
import { ElTree } from 'element-plus'
import * as DeptApi from '@/api/system/UnitList/index'
import { defaultProps, handleTree } from '@/utils/tree'
import {useRouter} from "vue-router";

defineOptions({ name: 'SystemUserDeptTree' })
const defaultSelectedKey = ref('')
const router = useRouter();
const props = defineProps({
  paramsId: {
    type: String,
    default: '',
    required: true
  },


})
const deptName = ref('')
const deptList = ref<Tree[]>([]) // 树形结构
const treeRef = ref<InstanceType<typeof ElTree>>()

/** 获得部门树 */
const getTree = async () => {
  const res = await DeptApi.getBranch({parentId: props.paramsId || '' })
  deptList.value = []
  deptList.value.push(...handleTree(res))
  // 设置默认选中第一个节点
  if (deptList.value.length > 0) {
    defaultSelectedKey.value = deptList.value[0].id
    // 触发第一个节点的点击事件
    handleNodeClick(deptList.value[0])
  }
}

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}

/** 处理部门被点击 */
const handleNodeClick = async (row: { [key: string]: any }) => {
  emits('node-click', row)
}
const emits = defineEmits(['node-click'])




/** 监听deptName */
watch(deptName, (val) => {
  treeRef.value!.filter(val)
})

/** 初始化 */
onMounted(async () => {
  await getTree()
})
</script>
