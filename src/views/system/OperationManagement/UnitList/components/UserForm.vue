<template>
  <Dialog width="70%" v-model="dialogVisible" :title="Title">
      <el-form
        class="-mb-15px"
        :model="formData"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="" prop="assetsName">
          <el-input
            v-model="formData.assetsName"
            placeholder="请输入设备编号/名称"
            clearable
            size="default"
            @keyup.enter="handleQuery"
            class="!w-200px"
          />
        </el-form-item>
        <el-form-item label="类型" prop="assetsTypeId">
          <el-cascader
            :options="DeviceType"
            v-model="assetsTypeId"
            @change="handleChange"
            max-collapse-tags="1"
            :props="props1"
            style="width: 100%;"
            collapse-tags
            clearable
          />
        </el-form-item>
        <el-form-item label="区域" prop="areaId">
          <Dictionary
            v-model="formData.areaId"
            type="cascader"
            dict-type="oamArea"
            :cascader-props="{
              multiple: true,
              label:'name',
              value: 'id',
             }"
            :max-collapse-tags="1"
            placeholder="请选择区域"
          />
        </el-form-item>
        <el-form-item>
<!--          <el-button size="default" type="primary" @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 查询</el-button>-->
          <XButton title="查询" preIcon="ep:search" gradient @click="handleQuery" />
          <el-button size="default" @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        </el-form-item>
      </el-form>
      <el-table
        ref="tableRef"
        style="margin-top: 25px;"
        height="440"
        row-key="id"
        @selection-change="handleSelectionChange"
        :row-class-name="tableRowClassName"
        :header-cell-style='{ "background-color": "#e4eefc", "color": "#606266" }'
        v-loading="loading" :data="tabelist">
        <el-table-column
          :selectable="checkSelectable"
          :reserve-selection="true"
          type="selection"  align="left" width="55" />
        <el-table-column
          label="设备编号"
          align="left"
          prop="assetsCode"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="设备名称"
          align="left"
          prop="assetsName"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="所属项目" align="left" prop="projectName" />
        <el-table-column
          label="设备类型"
          align="left"
          key="deptName"
          prop="assetsTypeName"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="区域" align="left" prop="area"  />
        <el-table-column label="地址" align="left" prop="address"  />
      </el-table>
    <Pagination
      style="margin-top: 30px;"
      :total="total"
      v-model:page="formData.pageNo"
      v-model:limit="formData.pageSize"
      @pagination="getList"
    />
    <template #footer>
      <div style="margin-top: 60px; text-align: center">
        <el-button :disabled="formLoading" class="gradient-btn" type="primary" style="width: 200px;" @click="submitForm">确 定</el-button>
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button></div>

    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import * as DeptApi from '@/api/system/UnitList/index'
import {ref} from "vue";
import {getPropertyDetails, getPropertyss, updatePermission} from "@/api/system/UnitList/index";
const tableRef = ref() // 表格引用
const tabelist = ref([]) // 表格数据
const dialogVisible = ref(false) // 弹窗的是否展示
const loading = ref(false) // 弹窗的是否展示
const typeName = ref('') // 弹窗的是否展示
const DeviceType = ref([])  //设备类型
const permissionDeptId = ref('')  //组织id
const Title = ref('添加设备')  //组织id
const permissionId = ref('')  //设备id
const assetsTypeId = ref([]) // 设备类型接收值
const isProgrammaticSelect = ref(false)
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const SelectionList =ref([])
const SelectedList =ref([])  //已勾选的设备
const unselectedRows = ref(new Set()) // 存储选中的行
const formData = ref({
  assetsTypeId: [],
  assetsName: '',
  areaId: [],
  permissionDeptId: '',
  permissionId: '',
  pageNo: '1',
  pageSize: '10',
})
const props1 = {
  value: 'id',
  label: 'name',
  children: 'children',
  multiple: true
}
const total = ref(0) // 总条数
const queryFormRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: string) => {
  dialogVisible.value = true
  resetForm()
   typeName.value = type
  if (type === 'SelectAssets') {
    formData.value.permissionId = id
    permissionId.value=id
    Title.value='选择设备'
    getList()
  // }
    // else if (type === 'SelectDept') {
  //   formData.value.permissionDeptId = id
  //   getList()
  }else {
    permissionDeptId.value = id
    formData.value.permissionDeptId = id
    getList()
    getSelectedProperty()
  }

}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 提交请求
  formLoading.value = true
  try {
    let parmas=[]
    if (typeName.value == 'SelectAssets'){
       let excludeDeviceIds= Array.from(unselectedRows.value)?.map(item=>item.deviceId)
       parmas={ id:permissionId.value,excludeDeviceIds}
    }else {
      SelectionList.value.forEach((item) => {
        parmas.push(
          {
            deviceId: item.id,
            permissionCode:item.assetsCode,
            permissionName:item.assetsName,
            permissionTypeCode:3,
            deptId:permissionDeptId.value,
            permissionTypeName:'设备',
          }
        )
      })
    }

    const res =  typeName.value=='SelectAssets'? await DeptApi.updatePermission(parmas)   :  await DeptApi.createPermissions(parmas)
    ElMessage.success('操作成功')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}
/** 获取已勾选资产 */
const getSelectedProperty = async () => {
 let res =await DeptApi.getPropertyss({permissionTypeCode:'3'})
  SelectedList.value = res.list?.map(item =>  item.deviceId)
  console.log('sds',SelectedList.value)
}
/** 重置按钮操作 */
const resetQuery = () => {
  resetForm()
  getList()
}

const getList = async () => {
  loading.value = true
  try {
    const res = typeName.value == 'SelectAssets' ? await DeptApi.getPropertyDetails(formData.value) : await DeptApi.getPropertyPages(formData.value)
    // tabelist.value = typeName.value == 'SelectAssets' ? res : res.list
    tabelist.value = res.list
    total.value = res.total
    if (typeName.value == 'SelectAssets') {
      isProgrammaticSelect.value = true
      tableRef.value?.clearSelection()
      tabelist.value.forEach(row => {
        if (!Array.from(unselectedRows.value).some(unselectedRow => unselectedRow.deviceId === row.deviceId)) {
          tableRef.value?.toggleRowSelection(row, true)
        }
      })
      setTimeout(() => {
        isProgrammaticSelect.value = false
      }, 0)
    }
  }finally {
    loading.value = false
  }
}




const handleSelectionChange = (val) => {
  if (isProgrammaticSelect.value) {
    return
  }
  SelectionList.value = val
  // 获取当前页面所有行
  const currentPageRows = tabelist.value
  const selectedIds = new Set(val.map(row => row))

  // 更新选中的行的ID
  currentPageRows.forEach(row => {
    if (!selectedIds.has(row)) {
      // 如果当前行被选中，将其添加到选中集合
      unselectedRows.value.add(row)
    } else {
      // 如果当前行被选中，将其从取消选中集合中移除
      unselectedRows.value.delete(row)
    }
  })

}


const handleChange = (val) => {
  // 1. 展平数组
  const flatArray = val.flat()
  // 2. 去重
  const uniqueArray = [...new Set(flatArray)]
  // 3. 过滤掉无效值（如 null、undefined）
  const validArray = uniqueArray.filter(item => item != null)
  formData.value.assetsTypeId=validArray

}


onMounted(async () => {
  // 加载设备类型数据
  DeviceType.value = await DeptApi.getDeviceType()
})


/** 重置表单 */
const resetForm = () => {
  if (typeName.value == 'SelectAssets') {
    formData.value = {
      assetsTypeId:  [],
      assetsName: '',
      areaId: [],
      permissionDeptId: '',
      permissionId: permissionId.value,
      pageNo: '1',
      pageSize: '10',
    }
  }else {
    formData.value = {
      assetsTypeId:  [],
      assetsName: '',
      areaId: [],
      permissionDeptId: permissionDeptId.value,
      pageNo: '1',
      pageSize: '10',
    }
  }

  // typeName.value = ''
  unselectedRows.value = new Set();
  assetsTypeId.value = []
  tabelist.value = []
  queryFormRef.value?.resetFields()
}

const tableRowClassName = ({ row, rowIndex }) => {
  if (typeName.value === 'SelectAssets'){
    return '';
  }
  // 根据条件来判断是否置灰和禁用多选框
  if (SelectedList.value.includes(row.id)) {
    return 'disabled-row';
  }
  return '';
};

// 判断行是否可选
const checkSelectable =(row, index)=> {
  if (typeName.value === 'SelectAssets'){
    return true;
  }

  return !SelectedList.value.includes(row.id);
}

</script>

<style>
.el-table .disabled-row {
  background-color: #d9d9d9;
  color: #999;
}
.el-table .disabled-row .el-checkbox__inner {
  background-color: #d9d9d9;
  border-color: #dcdfe6;
  cursor: not-allowed;
}
.gradient-btn {
  background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
  border: none !important;
  font-size: 14px !important;
  padding: 9px 16px !important;
  color: #ffffff !important;

  &:hover {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.8;
  }

  &:active {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.9;
  }

  &.el-button--primary {
    --el-button-hover-bg-color: transparent;
    --el-button-active-bg-color: transparent;
  }
}
</style>
