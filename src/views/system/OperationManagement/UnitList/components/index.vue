<template>
  <ContentWrap class="h-1/1">
    <div style="display:flex; align-items: center;">
      <el-button type="info" :icon="ArrowLeft" @click="goback()" plain>返回</el-button>
      <div style="margin-left:15px;">单位列表-数据权限</div>
    </div>
  </ContentWrap>
  <el-row :gutter="20">
    <!-- 左侧部门树 -->
    <el-col :span="4" :xs="24">
      <ContentWrap class="h-1/1" style="background: rgb(241 243 246)">
        <DeptTree v-if="paramsId"  @node-click="handleDeptNodeClick"  :paramsId="paramsId" />
      </ContentWrap>
    </el-col>

    <el-col :span="20" :xs="24">
      <!-- 搜索 -->
      <ContentWrap>
        <div style="display: flex; justify-content: space-between">
          <el-radio-group v-model="activeName">
            <el-radio-button label="设备权限" value="NewYork" />
            <el-radio-button label="区域权限" value="Washington" />
          </el-radio-group>

          <el-form
            v-if="activeName === 'NewYork'"
            class="-mb-15px"
            :model="queryParams"
            ref="queryFormRef"
            :inline="true"
            label-width="68px"
          >
            <el-form-item>
              <el-button
                plain
                @click="handleRole('addType')"
                v-hasPermi="['system:user:create']"
                v-if="parentId=='0'"
              >
                <Icon icon="ep:plus" />添加类型
              </el-button>
              <el-button
                plain
                @click="openProject('addProject')"
                v-hasPermi="['system:user:create']"
                v-if="parentId=='0'"
              >
                <Icon icon="ep:plus" />添加项目
              </el-button>
              <el-button
                plain
                @click="openForm('create',queryParams.deptId)"
                v-hasPermi="['system:user:create']"
                v-if="parentId=='0'"
              >
                <Icon icon="ep:plus" /> 添加设备
              </el-button>
              <el-button v-if="parentId!='0'" @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
            </el-form-item>
          </el-form>
          <el-button v-if="activeName == 'Washington'" @click="regionSave" ><Icon icon="ep:refresh" />保存</el-button>
        </div>
      </ContentWrap>
      <ContentWrap v-if="activeName === 'NewYork'">
        <el-table v-loading="loading"  :data="list">
          <el-table-column
            label="编号"
            align="left"
            prop="permissionCode"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="名称"
            align="left"
            prop="permissionName"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="类别" align="left" prop="permissionTypeName" />
          <el-table-column label="数量" align="left" key="deviceCount">
            <template #default="scope">
<!--              <el-button @click="openForm('SelectAssets', scope.row.id)" link type="primary">{{ scope.row.deviceCount }}</el-button>-->
              <span>{{ scope.row.deviceCount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="left" width="160">
            <template #default="scope">
              <div class="flex items-center justify-center">
                <el-button
                  type="primary"
                  link
                  v-if="scope.row.permissionTypeCode !== 3"
                  @click="openForm('SelectAssets', scope.row.id)"
                  v-hasPermi="['system:user:update']"
                >
                  <!--                      <Icon icon="ep:edit" />-->
                  选择设备
                </el-button>
                <el-button
                  type="primary"
                  link
                  @click="handleDelete(scope.row.id)"
                  v-hasPermi="['system:user:update']"
                >
                  <!--                      <Icon icon="ep:edit" />-->
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>

      <ContentWrap v-if="activeName === 'Washington'">
        <el-table 
          :data="tableData"  
          ref="WashingtonRef"   
          height="700" 
          style="width: 100%"  
          @selection-change="handleSelectionChange" 
          @click="handleTableClick"
          row-key="id" 
          default-expand-all
        >
          <el-table-column prop="name" label="区域名称" align="left" />
          <el-table-column label="序号" type="selection" align="left" width="55" />
        </el-table>
      </ContentWrap>
    </el-col>
  </el-row>

  <!-- 选择资产弹窗 -->
  <UserForm ref="formRef" @success="getList" />

  <!-- 添加类型-->
  <UserAssignRoleForm ref="assignRoleFormRef" @success="getList" />

  <!-- 添加项目 -->
  <ProjectPopup ref="ProjectFormRef" @success="getList" />

</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, defineProps, nextTick } from 'vue'
import * as DeptApi from '@/api/system/UnitList/index'
import UserForm from './UserForm.vue'
import UserAssignRoleForm from './UserAssignRoleForm.vue'
import {useRoute, useRouter} from "vue-router"
import ProjectPopup from './ProjectPopup.vue'
import DeptTree from './DeptTree.vue'
import {ArrowLeft} from "@element-plus/icons-vue";
import {resetPermission, updatePermissions} from "@/api/system/UnitList/index";
const props = defineProps({
  activeTab: {
    type: String,
    required: true
  }
})
const router = useRoute()
const routers = useRouter();
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const regionloading = ref(true) // 列表的加载中
const paramsId = ref('') // 部门树选择的id
const deptId = ref('') // 部门树选择的id
const WashingtonRef = ref() // 部门树选择的id
const total = ref(0) // 列表的总页数
const activeName = ref('NewYork') // 表头切换页
const list = ref([]) // 列表的数
const SelectedList=ref([])
const regionSelect=ref([])
const SeleectdataList=ref([])
const parentId=ref('')
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  deptId: undefined,

})
const queryFormRef = ref() // 搜索的表单

const tableData = ref([])
const isManualSelection = ref(false) // 添加标志位

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DeptApi.getPermissionPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 查询区域 */
const getregionList = async () => {
  regionloading.value = true
  try {
    const data = await DeptApi.getDicByDictType('oamArea')
    if (activeName.value!='NewYork' && parentId.value!='0') {
      tableData.value = [];
      await getSelectedlist();
      if (data.length > 0) {
        const selectedSet = new Set(SeleectdataList.value);
        // 递归遍历数据，仅保留完全匹配的节点（不保留父级）
        const filterOnlyMatched = (items) => {
          let matchedItems = [];
          items.forEach(item => {
            // 检查当前项是否匹配
            if (selectedSet.has(item.id)) {
              matchedItems.push({ ...item, children: [] }); // 只保留自身，清空子级
            }
            // 递归检查子级
            if (item.children && item.children.length > 0) {
              const matchedChildren = filterOnlyMatched(item.children);
              matchedItems.push(...matchedChildren); // 直接添加匹配的子项
            }
          });
          return matchedItems;
        };
        tableData.value = filterOnlyMatched(data);
      }
    } else {
      tableData.value = data
    }
  } finally {
    regionloading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery =async () => {
  try {
    await DeptApi.resetPermission(queryParams.deptId)
    message.success('操作成功')
    // 刷新列表
    await getList()
  } catch {}

}

/** 处理部门被点击 */
const handleDeptNodeClick = async (row) => {
    // isManualSelection.value = row.parentId=='0'? false : true;
  queryParams.deptId = row.id
  parentId.value=row.parentId
  deptId.value=row.id
  if (activeName.value=='NewYork'){
    await getList()
    // await getregionList();
  }else {
    await getregionList();
    await getSelectedProperty();
    nextTick(() => {
      // setTimeout(()=>{
       FeedbackPermissions();
      // },200)

    });
  }

  // await getregionList()

}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?:string ) => {

  formRef.value.open(type, id)
}

/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}

/** 区域权限选择 */
const handleSelectionChange = async (val) => {
  regionSelect.value = val;
  // 只有在手动选择时才执行API调用
  // if (!isManualSelection.value) return;

  // if (val.length > 0) {
  //   let params = val.map(item => ({
  //     deptId: queryParams.deptId,
  //     areaId: item.id,
  //     permissionCode: item.name,
  //     permissionName: item.name,
  //     permissionTypeCode: '4',
  //   }));
  //   console.log('发送给后端的参数:', params);
  //   const res = await DeptApi.updatePermissions(params)
  //   ElMessage.success('操作成功')
  // }else {
  //   console.log('发送给后端的参数2:', {  deptId: queryParams.deptId,permissionTypeCode: '4',});
  //   const res = await DeptApi.updatePermissions([{  deptId: queryParams.deptId,permissionTypeCode: '4',}])
  //   ElMessage.success('操作成功')
  // }
};

//区域保存按钮
const  regionSave=async()=>{
  if (regionSelect.value.length>0){
      let params = regionSelect.value.map(item => ({
        deptId: queryParams.deptId,
        areaId: item.id,
        permissionCode: item.name,
        permissionName: item.name,
        permissionTypeCode: '4',
      }));
      const res = await DeptApi.updatePermissions(params)
      ElMessage.success('操作成功')
    }else {
      const res = await DeptApi.updatePermissions([{  deptId: queryParams.deptId,permissionTypeCode: '4',}])
      ElMessage.success('操作成功')
    }

}

const getSelectedProperty = async () => {
  let res =await DeptApi.getPermissionList({deptId: queryParams.deptId ,permissionTypeCode:'4'})
  SelectedList.value = res.list?.map(item => {return item.areaId})
}
const getSelectedlist = async () => {
  let res =await DeptApi.getPermissionList({deptId: parentId.value=='0'? queryParams.deptId :parentId.value ,permissionTypeCode:'4'})
  SeleectdataList.value = res.list?.map(item => {return item.areaId})
}
/** 回显区域权限 */
const FeedbackPermissions = () => {
  // 确保表格数据已加载
  if (!tableData.value || !WashingtonRef.value) return;

  // 临时禁用手动选择标志
  isManualSelection.value = false;

  // 清除所有选择
  WashingtonRef.value.clearSelection();

  // 递归设置选择状态
  const setSelection = (data) => {
    if (!data) return;

    data.forEach(item => {
      if (SelectedList.value.includes(item.id)) {
        WashingtonRef.value.toggleRowSelection(item, true);
      }
      if (item.children && item.children.length > 0) {
        setSelection(item.children);
      }
    });
  };

  // 设置选择状态
  setSelection(tableData.value);

  // 重新启用手动选择标志
  nextTick(() => {
    isManualSelection.value = true;
  });
}

// 添加点击事件处理
const handleTableClick = () => {
  isManualSelection.value = true;
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DeptApi.deletePermission(id)
    message.success('操作成功')
    // 刷新列表
    await getList()
  } catch {}
}

/** 添加类型 */
const assignRoleFormRef = ref()
const handleRole = () => {
  assignRoleFormRef.value.open(queryParams.deptId)
}

/** 添加项目 */
const ProjectFormRef = ref()
const openProject = (type: string) => {
  ProjectFormRef.value.open(queryParams.deptId)
}

//返回
const goback = () =>{
  routers.go(-1)
}

/** 初始化 */
onMounted(() => {
  if (router.query.id) {
    paramsId.value = router.query.id
  }

})
/** 监听标签页变化并更新列表 */
watch(
  () => activeName.value,
  async (newVal) => {
    if (newVal === 'NewYork') {
      await getList();
    } else {
      console.log('sssssss',activeName.value)
      nextTick(()=>{
        activeName.value=newVal
      })

      await getregionList();
      await getSelectedProperty();
      nextTick(() => {
        FeedbackPermissions();
      });
    }
  }
)
</script>
