/**
 * Exhibition 模块类型定义
 * 统一管理展示模块相关的类型，避免重复定义
 */

// 重新导出通用类型
export type {
  SearchFormData,
  TableDataItem,
  BaseOption,
  PaginationParams
} from '@/config/constants/types/common'

export type {
  LabelFieldConfig,
  FieldGroup,
  FormRenderItem
} from '@/config/constants/types/field'

export type {
  ExhibitionOperate,
  BusinessType
} from '@/config/constants/enums/business'

export type {
  QueryResItem
} from '@/api/system/data/query-conf'

export type {
  StatisticItem
} from '@/api/system/data/count-conf'

export type {
  BusinessDataListRequest,
  SearchCondition,
  BusinessData
} from '@/api/system/business-data'

export type {
  ExhibitionFieldConfig
} from '@/config/business'

// ==================== Exhibition 模块特有类型 ====================

/**
 * 搜索字段配置接口
 * 扩展查询配置项，添加选项字段
 */
export interface SearchFieldConfig extends QueryResItem {
  options?: BaseOption[]
}

/**
 * 标签选择项接口
 */
export interface TagSelection {
  id: string
  name: string
}


/**
 * 数据转换元数据项接口
 */
export interface TransformMetadataItem {
  fieldCodes?: string
  queryType: number
}

/**
 * 搜索项接口（复用 API 类型）
 */
export type SearchItem = SearchCondition
// ==================== 组件 Props 类型 ====================

/**
 * DataTable 组件 Props
 */
export interface DataTableProps {
  columns: LabelFieldConfig[]
  data: TableDataItem[]
  rowKey: string
  loading?: boolean
}

/**
 * SearchForm 组件 Props
 */
export interface SearchFormProps {
  fields: SearchFieldConfig[]
  operateConfigList: ExhibitionOperate[]
  selectedRows: TableDataItem[]
  selectedOptions: Map<string, any[]>
}

/**
 * StatisticCards 组件 Props
 */
export interface StatisticCardsProps {
  config: StatisticItem[]
}

/**
 * CreateForm 组件 Props
 */
export interface CreateFormProps {
  // CreateForm 组件暂无 props
}

/**
 * TagMultiForm 组件 Props
 */
export interface TagMultiFormProps {
  // TagMultiForm 组件暂无 props
}

// ==================== 事件类型 ====================

/**
 * SearchForm 组件事件
 */
export interface SearchFormEmits {
  search: [params: SearchFormData]
  delete: [selection: TableDataItem[]]
}

/**
 * DataTable 组件事件
 */
export interface DataTableEmits {
  selectionChange: [selection: TableDataItem[]]
}
/**
 * TagMultiForm 组件事件
 */
export interface TagMultiFormEmits {
  ok: []
}

// ==================== 工具函数类型 ====================

/**
 * 数据转换函数类型
 */
export type TransformToSearchListFn = (
  filterData: Record<string, string>,
  metadataList: TransformMetadataItem[],
  separator?: string
) => SearchItem[]

/**
 * 组件属性获取函数类型
 */
export type GetComponentPropsFn = (field: SearchFieldConfig) => Record<string, any>

/**
 * 字段键获取函数类型
 */
export type GetFieldKeyFn = (field: SearchFieldConfig) => string

// ==================== 常量类型 ====================

/**
 * 查询类型映射
 */
export const QUERY_TYPE_MAP = {
  SEARCH: 0,      // 搜索
  SINGLE: 1,      // 单选
  MULTIPLE: 2,    // 多选
  DATE_RANGE: 3,  // 日期区间
  DATE: 4         // 日期
} as const

/**
 * 查询类型
 */
export type QueryType = typeof QUERY_TYPE_MAP[keyof typeof QUERY_TYPE_MAP]

// ==================== 导入重新导出 ====================

// 重新导出常用的 API 类型，方便使用
import type { QueryResItem } from '@/api/system/data/query-conf'
import type { StatisticItem } from '@/api/system/data/count-conf'
import type { SearchCondition } from '@/api/system/business-data'
import type { LabelFieldConfig } from '@/config/constants/types/field'
import type { SearchFormData, TableDataItem, BaseOption } from '@/config/constants/types/common'
import type { ExhibitionOperate } from '@/config/constants/enums/business'
