<template>
  <ContentWrap class="my-component">
    <header>告警标签配置</header>
    <main>
      <div class="main-top">
        <el-button
          type="primary"
          size="small"
          @click="addWarnFun"
          v-hasPermi="['infra:warn-label:create']"
          >新增</el-button
        >
      </div>
      <el-table
        :data="tableData"
        height="530"
        v-loading="loading"
        style="width: 100%; margin-bottom: 10px"
        :header-cell-style="{
          'background-color': '#F4F6F8',
          'font-size': '14px',
          color: '#3B3C3D'
        }"
      >
        <el-table-column type="index" :index="tableIndex" label="序号" width="80" />
        <el-table-column prop="labelName" label="标签名称" />
        <el-table-column prop="warnGrade" label="告警等级"
          ><template #default="scope">
            <div>
              <button :class="UTILS.accessStatusClass(scope.row.warnGrade)">
                {{ scope.row.warnGrade }}</button
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="warnDetail" label="告警详情">
          <template #default="scope">
            <span class="label-style">
              {{ scope.row.warnDetail }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="操作" fixed="right">
          <template #default="scope">
            <el-button
              link
              type="primary"
              size="small"
              @click="editWarnFun(scope.row, scope.row.id)"
              v-hasPermi="['infra:warn-label:update']"
              >编辑</el-button
            >
            <el-button
              link
              type="primary"
              size="small"
              @click="goDetailFun(scope.row)"
              v-hasPermi="['infra:warn-source-param:query']"
              >详情</el-button
            >
            <el-button
              link
              type="primary"
              size="small"
              @click="removeWarnFun(scope.row.id)"
              v-hasPermi="['infra:warn-label:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="demo-pagination-block">
        <el-pagination
          v-model:current-page="form.pageNo"
          v-model:page-size="form.pageSize"
          :page-sizes="[10, 20, 30, 50, 100]"
          :size="size"
          :disabled="disabled"
          :background="background"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </main>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElButton } from 'element-plus'
import { useRouter } from 'vue-router'
import * as UTILS from './utils'
import * as LabelApi from '@/api/alarm/rules/label'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()

onMounted(() => {
  getWarnListFun()
})

// 分页相关
const loading = ref(true)
const total = ref(0)
const size = ref('default')
const background = ref(true)
const disabled = ref(false)

const form = reactive({
  pageNo: 1,
  pageSize: 10
})

const tableData = ref([])

const getWarnListFun = async () => {
  loading.value = true
  try {
    const data = await LabelApi.getWarnList(form)
    // console.log('打印列表', data)
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
// 新增
const addWarnFun = () => {
  router.push({
    path: '/alarm/rules/label/labelAdd'
  })
}
// 编辑
const editWarnFun = async (obj, id) => {
  //存储当前对象 用于回显顶部表格
  sessionStorage.setItem('alarmLabelObj', JSON.stringify(obj))
  //存储当前行id用于判断是否是 编辑 没有id是 新增 同时用于在编辑页面查询底部数据展示表格
  sessionStorage.setItem('alarmLabelEditId', JSON.stringify(id))
  router.push({
    path: '/alarm/rules/label/labelAdd'
  })
}

// 详情
const goDetailFun = (obj) => {
  // console.log('打印当前行数据', obj)
  sessionStorage.setItem('alarmLabelObj', JSON.stringify(obj))
  router.push({
    path: '/alarm/rules/label/labelDetail'
  })
}
// 删除
const removeWarnFun = async (id) => {
  // 执行删除操作
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await LabelApi.deleteWarnLabel({ id })
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getWarnListFun()
  } catch {}
}

// 序号自增
const tableIndex = (index: number) => {
  return (form.pageNo - 1) * form.pageSize + index + 1
}

// 分页事件
const handleSizeChange = (val: number) => {
  form.pageSize = val
  getWarnListFun()
}
const handleCurrentChange = (val: number) => {
  form.pageNo = val
  getWarnListFun()
}
</script>

<style lang="scss" scoped>
.my-component {
  // width: 100% !important;
  // height: 100% !important;
  background-color: white;
  display: flex;
  flex-direction: column;
  header {
    width: 100%;
    height: 60px;
    font-size: 18px;
    font-weight: bold;
    padding-left: 20px;
    display: flex;
    align-items: center;
  }
  main {
    flex: 1;
    width: 100%;
    // padding: 0 30px 0 20px;
    .main-top {
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
}

// 状态按钮样式
.low-class {
  background-color: #ffd910; /* 蓝色背景 */
  color: #000; /* 文字 */
  min-width: 50px;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.middle-class {
  background-color: #ff9900; /* 蓝色背景 */
  color: #fff; /* 白色文字 */
  min-width: 50px;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.high-class {
  background-color: #ff5e0b; /* 蓝色背景 */
  color: #fff; /* 文字 */
  min-width: 50px;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.emergency-class {
  background-color: #f5002a; /* 蓝色背景 */
  color: #fff; /* 文字 */
  min-width: 50px;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
// 分页样式
.demo-pagination-block {
  display: flex;
  justify-content: flex-end;
}
// 标签样式
.label-style {
  padding: 3px 8px;
  border-radius: 5px;
  background-color: #f1f4f9;
}
</style>
