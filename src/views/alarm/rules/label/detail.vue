<template>
  <ContentWrap class="my-component">
    <header>
      <el-button class="ml-5 mr-5" type="primary" plain @click="goBack">返回</el-button>
      <p>告警标签配置-详情</p>
    </header>
    <main>
      <div class="main-top">
        <div class="header-title">标签信息</div>
        <div class="top-content">
          <div class="label-contenr">
            <div class="label-left">标签名称</div>
            <div class="label-right">{{ newalarmLabelObj?.labelName }}</div>
          </div>
          <div class="label-contenr">
            <div class="label-left">告警等级</div>
            <div class="label-right">
              <button :class="UTILS.accessStatusClass(newalarmLabelObj?.warnGrade)">
                {{ newalarmLabelObj?.warnGrade }}</button
              ></div
            >
          </div>
          <div class="label-contenr">
            <div class="label-left">告警详情</div>
            <div class="label-right">{{ newalarmLabelObj?.warnDetail }}</div>
          </div>
        </div>
      </div>
      <div class="main-bottom">
        <div class="header-title">告警参数</div>
        <div class="bottom-content">
          <el-table
            :data="tableList"
            style="width: 100%; margin-bottom: 10px"
            :header-cell-style="{
              'background-color': '#F4F6F8',
              'font-size': '14px',
              color: '#3B3C3D'
            }"
          >
            <el-table-column prop="sourceName" label="告警来源" />
            <el-table-column prop="returnParam" label="返回参数" />
            <el-table-column prop="note" label="注释" />
          </el-table>
        </div>
      </div>
    </main>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, onBeforeRouteLeave } from 'vue-router'
import * as LabelApi from '@/api/alarm/rules/label'
import * as UTILS from './utils/index' //引入筛选的工具函数
const route = useRoute()
const newalarmLabelObj = ref({})
const params = reactive({ labelId: '' })
onMounted(async () => {
  const alarmLabelObj = sessionStorage.getItem('alarmLabelObj')
  newalarmLabelObj.value = JSON.parse(alarmLabelObj)
  // console.log('传递过来的数据', newalarmLabelObj.value)
  if (alarmLabelObj) {
    params.labelId = newalarmLabelObj.value?.id
    getDetailBottomFun()
  } else {
    ElMessage.warning('已离开页面,请重新选择项目')
  }
})

// 使用 beforeRouteLeave 导航守卫
onBeforeRouteLeave((to, from) => {
  // 清除 sessionStorage 中的 equipmentDetailId
  // sessionStorage.removeItem('equipmentDetailId')
})

// 返回上一页
const goBack = () => {
  window.history.back()
}
const tableList = ref([])
const getDetailBottomFun = async () => {
  const data = await LabelApi.getDetailBottom(params)
  tableList.value = data
  // console.log('详情底部数据', data)
}
</script>

<style lang="scss" scoped>
.my-component {
  // width: 100%;
  // height: 100%;
  // background-color: #f6f7f9;
  background-color: white;
  header {
    width: 100%;
    height: 50px;
    background-color: white;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  main {
    width: 100%;
    height: 650px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .main-top {
      width: 100%;
      height: 250px;
      margin-bottom: 40px;
      // background-color: yellowgreen;
      .header-title {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        position: relative;
        font-family: Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #3b3c3d;
        text-align: left;
        font-style: normal;
        text-transform: none;
        line-height: 24px;
        margin: 3px 0;
        padding-left: 28px;

        &::before {
          margin: 10px 0 0 10px;
          content: '';
          position: absolute;
          left: 0;
          top: 6px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #005fff;
          border: 2px solid #d4e4ff;
        }
      }
      .top-content {
        padding: 0 15px;
        width: 100%;
        height: 200px;
        .label-contenr {
          width: 100%;
          height: 40px;
          display: flex;
          align-items: center;
          .label-left {
            width: 120px;
            padding: 0 20px;
          }
          .label-right {
            flex: 1;
          }
        }
      }
    }
    .main-bottom {
      width: 100%;
      height: 400px;
      // background-color: rgba(255, 192, 203, 0.628);
      .header-title {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        position: relative;
        font-family: Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #3b3c3d;
        text-align: left;
        font-style: normal;
        text-transform: none;
        line-height: 24px;
        margin: 3px 0;
        padding-left: 28px;

        &::before {
          margin: 10px 0 0 10px;
          content: '';
          position: absolute;
          left: 0;
          top: 6px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #005fff;
          border: 2px solid #d4e4ff;
        }
      }
      .bottom-content {
        padding: 0 15px;
      }
    }
  }
}

// 状态按钮样式
:deep(.stop-class) {
  background-color: #eff3f8;
  color: #919aaa;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.normal-class) {
  background-color: #005fff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.paused-class) {
  background-color: #eff3f8;
  color: #000;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// 状态按钮样式
.low-class {
  background-color: #ffd910; /* 蓝色背景 */
  color: #000; /* 文字 */
  min-width: 50px;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.middle-class {
  background-color: #ff9900; /* 蓝色背景 */
  color: white; /* 白色文字 */
  min-width: 50px;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.high-class {
  background-color: #ff5e0b; /* 蓝色背景 */
  color: #fff; /* 文字 */
  min-width: 50px;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.emergency-class {
  background-color: #f5002a; /* 蓝色背景 */
  color: #fff; /* 文字 */
  min-width: 50px;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
