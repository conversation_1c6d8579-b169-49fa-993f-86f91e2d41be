<template>
  <div class="my-component">
    <header>
      <el-button class="ml-5 mr-5" type="primary" plain @click="goBack">返回</el-button>
      <p>{{ isEditMode ? '告警标签配置 - 编辑' : '告警标签配置 - 新增' }}</p>
    </header>
    <main>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-suffix=":"
        size="default"
      >
        <div class="main-top">
          <div class="header-title">标签信息</div>
          <div class="top-content">
            <el-form-item label="标签名称" prop="labelName">
              <el-input v-model="form.labelName" placeholder="请输入" clearable />
            </el-form-item>

            <el-form-item label="告警等级" prop="warnGrade">
              <el-select v-model="form.warnGrade" placeholder="请选择" clearable>
                <el-option label="紧急" value="紧急" />
                <el-option label="高" value="高" />
                <el-option label="中" value="中" />
                <el-option label="低" value="低" />
              </el-select>
            </el-form-item>

            <el-form-item label="告警详情" prop="warnDetail" class="full-width">
              <el-input
                v-model="form.warnDetail"
                type="textarea"
                placeholder="请输入"
                :rows="3"
                clearable
              />
            </el-form-item>
          </div>
        </div>

        <div class="main-bottom">
          <div class="header-title">告警参数</div>
          <div class="bottom-content">
            <div class="btn-content">
              <el-button class="btn-class" type="primary" @click="openAddParamDialog"
                >新增</el-button
              >
            </div>

            <el-table
              :data="tableList"
              style="width: 100%"
              :header-cell-style="{
                'background-color': '#F4F6F8',
                'font-size': '14px',
                color: '#3B3C3D'
              }"
            >
              <el-table-column prop="sourceName" label="告警来源" />
              <el-table-column prop="returnParam" label="返回参数" />
              <el-table-column prop="note" label="注释" />
              <el-table-column label="操作" fixed="right">
                <template #default="scope">
                  <el-button link type="primary" size="small" @click="removeWarnFun(scope.row.id)"
                    >移出</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-form>

      <div style="margin-top: 20px; text-align: center">
        <el-button class="w-50" type="primary" @click="submitForm">确认</el-button>
        <el-button class="w-50" @click="goBack">取消</el-button>
      </div>
    </main>

    <!-- 新增参数弹窗 -->
    <el-dialog v-model="dialogVisible" title="新增" width="800px">
      <div class="dialog-content">
        <div class="search-form">
          <el-form :inline="true" :model="searchForm">
            <el-form-item label="告警来源">
              <el-select
                v-model="searchForm.sourceId"
                placeholder="请选择"
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="item in selectList"
                  :key="item.id"
                  :label="item.sourceName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchParams">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
          <div class="remind">
            <img src="@/assets/imgs/icons/okEnable.png" alt="" />
            <div class="text-style">告警参数被其他标签选择后,无法再次选择</div>
          </div>
        </div>

        <el-table
          ref="dialogTable"
          :data="dialogTableList"
          @selection-change="handleSelectionChange"
          style="width: 100%"
          :header-cell-style="{
            'background-color': '#F4F6F8',
            'font-size': '14px',
            color: '#3B3C3D'
          }"
          :row-style="(row) => (row.row.labelId !== null ? { cursor: 'not-allowed' } : {})"
          :cell-class-name="
            (params) => {
              if (params.column.type === 'selection') {
                return params.row.labelId !== null ? 'disabled-selection' : ''
              }
              return ''
            }
          "
        >
          <el-table-column type="selection" width="55" :selectable="isRowSelectable" />
          <el-table-column prop="sourceName" label="告警来源" />
          <el-table-column prop="returnParam" label="返回参数" />
          <el-table-column prop="note" label="注释" />
        </el-table>
      </div>

      <template #footer>
        <span
          class="dialog-footer"
          style="margin-top: 20px; display: flex; justify-content: center; gap: 20px"
        >
          <el-button class="w-50" type="primary" @click="confirmAddParams">确认</el-button>
          <el-button class="w-50" @click="dialogVisible = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import * as LabelApi from '@/api/alarm/rules/label'

const router = useRouter()
const formRef = ref()
const dialogTable = ref() // 弹窗表格的 ref
const isEditMode = ref(false)

const form = reactive({
  labelId: '',
  labelName: '',
  warnGrade: '',
  warnDetail: ''
})

const rules = reactive({
  labelName: [{ required: true, message: '请输入标签名称', trigger: 'blur' }],
  warnGrade: [{ required: true, message: '请选择告警等级', trigger: 'change' }],
  warnDetail: [{ required: true, message: '请输入告警详情', trigger: 'blur' }]
})

const tableList = ref([])

const selectList = ref([])
const getSelectFun = async () => {
  try {
    const data = await LabelApi.getSelect()
    selectList.value = data
    // console.log('打印下拉数据', data)
  } catch (error) {
    ElMessage.error('获取告警来源失败')
  }
}

onMounted(async () => {
  const alarmLabelObj = sessionStorage.getItem('alarmLabelObj')
  const alarmLabelEditId = sessionStorage.getItem('alarmLabelEditId')
  getSelectFun()
  if (alarmLabelObj) {
    const parsedData = JSON.parse(alarmLabelObj)
    isEditMode.value = true
    Object.assign(form, parsedData)
    if (alarmLabelEditId) {
      form.labelId = JSON.parse(alarmLabelEditId)
      await fetchWarnParams()
    }
  } else {
    resetForm()
  }
})

const fetchWarnParams = async () => {
  try {
    const data = await LabelApi.getDetailBottom({ labelId: form.labelId })
    tableList.value = data
  } catch (error) {
    ElMessage.error('获取告警参数失败')
  }
}

const resetForm = () => {
  form.labelId = ''
  form.labelName = ''
  form.warnGrade = ''
  form.warnDetail = ''
  tableList.value = []
}

const goBack = () => {
  sessionStorage.removeItem('alarmLabelObj')
  sessionStorage.removeItem('alarmLabelEditId')
  router.go(-1)
}

// 新增参数弹窗相关
const dialogVisible = ref(false)
const dialogTableList = ref([])

const selectedParams = ref([])
// 下拉表单
const searchForm = reactive({
  sourceId: ''
})

const openAddParamDialog = async () => {
  dialogVisible.value = true
  await fetchDialogParams()
  await nextTick()
  setSelectedRows()
}

const fetchDialogParams = async () => {
  try {
    const data = await LabelApi.getSearchData(searchForm)
    dialogTableList.value = data.list
    // console.log('打印弹窗表格数据', data)
  } catch (error) {
    ElMessage.error('获取参数列表失败')
  }
}

const setSelectedRows = () => {
  const existingIds = new Set(tableList.value.map((item) => item.id))
  dialogTableList.value.forEach((row) => {
    if (existingIds.has(row.id)) {
      dialogTable.value?.toggleRowSelection(row, true)
    }
  })
}

const searchParams = async () => {
  await fetchDialogParams()
  await nextTick()
  setSelectedRows()
}

const resetSearch = () => {
  searchForm.sourceId = ''
  searchParams()
}

const handleSelectionChange = (val: any) => {
  selectedParams.value = val.filter((item: any) => item.labelId === null)
}
// 判断某一行是否可选（labelId 为 null 才能选）
const isRowSelectable = (row, index) => {
  return row.labelId === null || row.labelId === undefined
}
const confirmAddParams = () => {
  if (selectedParams.value.length === 0) {
    ElMessage.warning('请至少选择一个参数')
    return
  }

  const newParams = []
  const existingIds = new Set(tableList.value.map((item) => item.id))

  selectedParams.value.forEach((param) => {
    if (!existingIds.has(param.id)) {
      newParams.push(param)
    } else {
      ElMessage.warning(`参数 "${param.sourceName}" 已经存在，跳过添加`)
    }
  })

  if (newParams.length > 0) {
    tableList.value = [...tableList.value, ...newParams]
    ElMessage.success(`成功添加 ${newParams.length} 个参数`)
  } else {
    ElMessage.warning('所有选中参数已存在，未添加新参数')
  }

  dialogVisible.value = false
}

const removeWarnFun = (id: string) => {
  tableList.value = tableList.value.filter((item) => item.id !== id)
}

const submitForm = async () => {
  const isValid = await formRef.value.validate()
  if (!isValid) return

  try {
    const payload = {
      ...form,
      warnSourceParamList: tableList.value // 直接使用页面上的 tableList 数据
    }

    if (isEditMode.value) {
      await LabelApi.updateWarnLabel(payload)
      ElMessage.success('更新成功')
    } else {
      await LabelApi.createWarnLabel(payload)
      ElMessage.success('创建成功')
    }
    goBack()
  } catch (error) {
    ElMessage.error(isEditMode.value ? '更新失败' : '创建失败')
  }
}
</script>

<style lang="scss" scoped>
.my-component {
  width: 100%;
  height: 100%;
  background-color: white;

  header {
    width: 100%;
    height: 50px;
    background-color: white;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 20px;
    box-sizing: border-box;
  }

  main {
    width: 100%;
    height: calc(100% - 50px);
    padding: 0 20px;
    box-sizing: border-box;

    .main-top,
    .main-bottom {
      margin-bottom: 30px;
    }
    .main-bottom {
      .bottom-content {
        .btn-content {
          width: 100%;
          height: 50px;
          display: flex;
          .btn-class {
            margin-left: auto;
          }
        }
      }
    }
    .header-title {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      position: relative;
      font-family: Microsoft YaHei;
      font-weight: bold;
      font-size: 16px;
      color: #3b3c3d;
      text-align: left;
      font-style: normal;
      text-transform: none;
      line-height: 24px;
      margin: 10px 0;
      padding-left: 28px;

      &::before {
        margin: 10px 0 0 10px;
        content: '';
        position: absolute;
        left: 0;
        top: 6px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #005fff;
        border: 2px solid #d4e4ff;
      }
    }

    .top-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      width: 100%;
      margin-bottom: 20px;
      .full-width {
        grid-column: span 2 / span 2; // 占据两列宽度（即 100%）
      }
    }
  }

  .dialog-content {
    .search-form {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .remind {
        margin-top: -10px;
        display: flex;
        align-items: center;
        img {
          width: 20px;
        }
        .text-style {
        }
      }
    }

    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
