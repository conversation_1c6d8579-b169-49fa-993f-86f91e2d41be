export function accessStatus(value) {
  switch (value) {
    case -1:
      return '停止'
    case 0:
      return '正常'
    case 1:
      return '暂停'
    default:
      return ''
  }
}

export function accessStatusClass(value) {
  switch (value) {
    case '低':
      return 'low-class'
    case '中':
      return 'middle-class'
    case '高':
      return 'high-class'
    case '紧急':
      return 'emergency-class'
    default:
      return ''
  }
}

//时间戳转化 保留时分秒
export function timestampToDateString(timestamp: number): string {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}
//时间戳转化 不保留时分秒
export function timestampToDateOnlyString(timestamp: number): string {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从0开始
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}/${month}/${day}`
}
