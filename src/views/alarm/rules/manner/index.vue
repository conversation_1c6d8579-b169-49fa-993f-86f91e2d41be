<template>
  <ContentWrap class="my-component">
    <header>告警方式配置</header>
    <main>
      <div class="main-top">
        <el-button
          type="primary"
          size="small"
          @click="addWarnFun"
          v-hasPermi="['infra:warn-type-dict:create']"
          >新增</el-button
        >
      </div>
      <el-table
        :data="tableData"
        height="530"
        v-loading="loading"
        style="width: 100%; margin-bottom: 10px"
        :header-cell-style="{
          'background-color': '#F4F6F8',
          'font-size': '14px',
          color: '#3B3C3D'
        }"
      >
        <el-table-column type="index" :index="tableIndex" label="序号" width="80" />
        <el-table-column prop="warnTypeName" label="告警方式名称" />
        <el-table-column prop="connectionMethod" label="具体形式">
          <template #default="scope">
            <span>{{ getDisplayText(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="操作" fixed="right">
          <template #default="scope">
            <el-button
              link
              type="primary"
              size="small"
              @click="editWarnFun(scope.row.id)"
              v-hasPermi="['infra:warn-type-dict:update']"
              >编辑</el-button
            >
            <el-button
              link
              type="primary"
              size="small"
              @click="removeWarnFun(scope.row.id)"
              v-hasPermi="['infra:warn-type-dict:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="demo-pagination-block">
        <el-pagination
          v-model:current-page="form.pageNo"
          v-model:page-size="form.pageSize"
          :page-sizes="[10, 20, 30, 50, 100]"
          :size="size"
          :disabled="disabled"
          :background="background"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </main>
    <!-- 新增 / 编辑弹窗 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px" @close="resetForm">
      <el-form ref="formRef" :model="completeForm" :rules="rules" label-width="120px">
        <el-form-item label="告警方式名称" prop="warnTypeName">
          <el-input v-model="completeForm.warnTypeName" placeholder="请输入告警方式名称" />
        </el-form-item>
        <el-form-item label="具体形式选择">
          <el-checkbox v-model="completeForm.popFlag" label="弹窗" />
          <el-checkbox v-model="completeForm.topFlag" label="置顶" />
          <el-checkbox v-model="completeForm.pushFlag" label="推送" />
          <el-checkbox v-model="completeForm.msgFlag" label="短信" />
          <el-checkbox v-model="completeForm.alarmFlag" label="警报" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import {
  ElMessage,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElCheckbox,
  ElButton
} from 'element-plus'
import * as MannerApi from '@/api/alarm/rules/manner'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
// 数据展示相关
const getDisplayText = (row) => {
  const texts = []
  if (row?.popFlag) texts.push('弹窗')
  if (row?.topFlag) texts.push('置顶')
  if (row?.pushFlag) texts.push('推送')
  if (row?.msgFlag) texts.push('短信')
  if (row?.alarmFlag) texts.push('警报')
  return texts.length > 0 ? texts.join('、') : '无'
}

onMounted(() => {
  getWarnListFun()
})

// 分页相关
const loading = ref(true)
const total = ref(0)
const size = ref('default')
const background = ref(true)
const disabled = ref(false)

const form = reactive({
  pageNo: 1,
  pageSize: 10
})

const tableData = ref([])

const getWarnListFun = async () => {
  loading.value = true
  try {
    const data = await MannerApi.getWarnList(form)
    // console.log('打印告警方式配置列表', data)
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 定义一个完整的表单数据模型
const completeForm = reactive({
  warnTypeName: '',
  popFlag: false,
  topFlag: false,
  pushFlag: false,
  msgFlag: false,
  alarmFlag: false
})

// 表单相关
const dialogVisible = ref(false)
const dialogTitle = ref('新增告警方式')
const currentId = ref(null)

const rules = reactive({
  warnTypeName: [{ required: true, message: '告警方式名称不能为空', trigger: 'blur' }]
})

const formRef = ref(null)

const addWarnFun = () => {
  dialogTitle.value = '新增告警方式'
  currentId.value = null
  // 重置表单数据
  completeForm.warnTypeName = ''
  completeForm.popFlag = false
  completeForm.topFlag = false
  completeForm.pushFlag = false
  completeForm.msgFlag = false
  completeForm.alarmFlag = false
  dialogVisible.value = true
}

const editWarnFun = async (id) => {
  try {
    const data = await MannerApi.getWarnDetail({ id })
    // 将获取的数据回填到 completeForm 中
    completeForm.warnTypeName = data.warnTypeName
    completeForm.popFlag = data.popFlag
    completeForm.topFlag = data.topFlag
    completeForm.pushFlag = data.pushFlag
    completeForm.msgFlag = data.msgFlag
    completeForm.alarmFlag = data.alarmFlag
    currentId.value = id
    dialogTitle.value = '编辑告警方式'
    dialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取告警方式详情失败')
  }
}
// 删除
const removeWarnFun = async (id) => {
  // 执行删除操作
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await MannerApi.deleteWarn({ id })
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getWarnListFun()
  } catch {}
}

const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (currentId.value) {
          await MannerApi.updateWarn({
            id: currentId.value,
            ...completeForm
          })
          ElMessage.success('编辑成功')
        } else {
          // console.log('打印新增数据', completeForm)
          await MannerApi.createWarn(completeForm)
          ElMessage.success('新增成功')
        }
        dialogVisible.value = false
        getWarnListFun()
      } catch (error) {
        // ElMessage.error('操作失败')
      }
    }
  })
}

const resetForm = () => {
  completeForm.warnTypeName = ''
  completeForm.popFlag = false
  completeForm.topFlag = false
  completeForm.pushFlag = false
  completeForm.msgFlag = false
  completeForm.alarmFlag = false
}

// 序号自增
const tableIndex = (index: number) => {
  return (form.pageNo - 1) * form.pageSize + index + 1
}

// 分页事件
const handleSizeChange = (val: number) => {
  form.pageSize = val
  getWarnListFun()
}
const handleCurrentChange = (val: number) => {
  form.pageNo = val
  getWarnListFun()
}
</script>

<style lang="scss" scoped>
.my-component {
  // width: 100% !important;
  // height: 100% !important;
  background-color: white;
  display: flex;
  flex-direction: column;
  header {
    width: 100%;
    height: 60px;
    font-size: 18px;
    font-weight: bold;
    padding-left: 20px;
    display: flex;
    align-items: center;
  }
  main {
    flex: 1;
    width: 100%;
    // padding: 0 30px 0 20px;
    .main-top {
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
}
// 分页样式
.demo-pagination-block {
  display: flex;
  justify-content: flex-end;
}
</style>
