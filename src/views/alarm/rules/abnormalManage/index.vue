<template>
  <ContentWrap class="my-component">
    <header>异常告警配置</header>
    <main>
      <div class="main-top">
        <el-button
          type="primary"
          size="small"
          @click="openDialog"
          v-hasPermi="['infra:warn-abnormal-config:create']"
          >新增</el-button
        >
      </div>
      <el-table
        :data="tableData"
        height="530"
        v-loading="loading"
        style="width: 100%; margin-bottom: 10px"
        :header-cell-style="{
          'background-color': '#F4F6F8',
          'font-size': '14px',
          color: '#3B3C3D'
        }"
      >
        <el-table-column type="index" :index="tableIndex" label="序号" width="80" />
        <el-table-column prop="labelName" label="告警规则">
          <template #default="scope">
            <div> {{ scope.row.warnMonCycle }}天内 {{ scope.row.warnCount }}次告警 </div>
          </template>
        </el-table-column>
        <el-table-column prop="warnGrade" label="状态"
          ><template #default="scope">
            <div>
              <button :class="UTILS.accessStatusClass(scope.row.openFlag)">
                {{ scope.row.openFlag ? '已启用' : '未启用' }}</button
              >
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="date" label="操作" fixed="right">
          <template #default="scope">
            <el-button
              v-if="scope.row.openFlag"
              link
              type="primary"
              size="small"
              @click="closeWarnFun(scope.row.id)"
              v-hasPermi="['infra:warn-abnormal-config:update']"
              >关闭</el-button
            >
            <el-button
              v-else
              link
              type="primary"
              size="small"
              @click="openFun(scope.row.id)"
              v-hasPermi="['infra:warn-abnormal-config:update']"
              >启用</el-button
            >
            <el-button
              link
              type="primary"
              size="small"
              @click="removeWarnFun(scope.row.id)"
              v-hasPermi="['infra:warn-abnormal-config:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="demo-pagination-block">
        <el-pagination
          v-model:current-page="form.pageNo"
          v-model:page-size="form.pageSize"
          :page-sizes="[10, 20, 30, 50, 100]"
          :size="size"
          :disabled="disabled"
          :background="background"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </main>

    <!-- 新增规则弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="新增规则"
      width="600px"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="180px" status-icon>
        <el-form-item label="填写异常设备监测周期:" prop="warnMonCycle">
          <el-input v-model.number="ruleForm.warnMonCycle" placeholder="请输入" />
          <div class="append-icon-text">天</div>
        </el-form-item>
        <el-form-item label="填写异常设备告警阈值:" prop="warnCount">
          <el-input v-model.number="ruleForm.warnCount" placeholder="请输入" />
          <div class="append-icon-text">次</div>
        </el-form-item>
      </el-form>
      <div class="rules-box" style="display: flex">
        <div class="rules-left">规则预览:</div>
        <!-- <div class="rules-right"
          >{{ warnRuleList?.warnMonCycle }}天内产生{{
            warnRuleList?.warnCount
          }}次以上告警的设备</div
        > -->
        <div class="rules-right">
          <span v-if="rulePreviewCycle">{{ rulePreviewCycle }}</span>
          <span v-if="rulePreviewCount">{{ rulePreviewCount }}</span>
        </div>
      </div>
      <template #footer>
        <div
          class="dialog-footer"
          style="margin-top: 20px; display: flex; justify-content: center; gap: 20px"
        >
          <el-button class="w-30" type="primary" @click="handleSubmit">确认添加</el-button>
          <el-button class="w-30" @click="handleClose">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 启用确认对话框 -->
    <el-dialog v-model="confirmOpenDialog" title="提示" width="400px" :close-on-click-modal="false">
      <div style="text-align: center; padding: 20px 0">
        <div><img src="@/assets/imgs/icons/okEnable.png" alt="" /></div>
        <div style="font-size: 18px; font-weight: bold; margin-bottom: 20px">确认启用？</div>
        <div style="font-size: 14px; color: #606266">
          启用新规则后，会清除之前异常设备，
          <br />
          并且根据新规则重新生成异常设备
        </div>
      </div>
      <template #footer>
        <div style="display: flex; justify-content: center; gap: 20px">
          <el-button type="primary" @click="confirmEnable">确认启用</el-button>
          <el-button @click="cancelEnable">暂不启用</el-button>
        </div>
      </template>
    </el-dialog>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElButton, FormInstance, FormRules } from 'element-plus'
import * as UTILS from './utils'
import * as AbnormalManageApi from '@/api/alarm/rules/abnormalManage'
import * as AbnormalApi from '@/api/alarm/abnormal' //引入api
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

onMounted(() => {
  getWarnListFun()
  getWarnRuleFun()
})

// 计算规则预览文本
// 计算“天”部分
const rulePreviewCycle = computed(() => {
  const cycle = ruleForm.warnMonCycle
  if (cycle !== null && cycle !== undefined) {
    return `${cycle}天内`
  }
  return ''
})

// 计算“次数”部分
const rulePreviewCount = computed(() => {
  const count = ruleForm.warnCount
  if (count !== null && count !== undefined) {
    return `产生${count}次以上告警的设备`
  }
  return ''
})

// 分页相关
const loading = ref(true)
const total = ref(0)
const size = ref('default')
const background = ref(true)
const disabled = ref(false)

const form = reactive({
  pageNo: 1,
  pageSize: 10
})

const tableData = ref([])

const getWarnListFun = async () => {
  loading.value = true
  try {
    const data = await AbnormalManageApi.getWarnList(form)
    // console.log('打印列表', data)
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
// 新增
const dialogVisible = ref(false)
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  warnMonCycle: null,
  warnCount: null
})

const rules = reactive<FormRules>({
  warnMonCycle: [
    {
      required: true,
      message: '请输入异常设备监测周期',
      trigger: 'blur'
    },
    {
      type: 'number',
      message: '周期必须为数字',
      trigger: 'blur'
    }
  ],
  warnCount: [
    {
      required: true,
      message: '请输入异常设备告警阈值',
      trigger: 'blur'
    },
    {
      type: 'number',
      message: '阈值必须为数字',
      trigger: 'blur'
    }
  ]
})

// 打开弹窗
const openDialog = () => {
  dialogVisible.value = true
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  ruleFormRef.value?.resetFields()
}

// 提交表单
const handleSubmit = () => {
  ruleFormRef.value?.validate((valid) => {
    if (valid) {
      // 提交数据到后端
      AbnormalManageApi.postAdd(ruleForm)
        .then(() => {
          ElMessage.success('添加成功')
          dialogVisible.value = false
          // 刷新列表
          getWarnListFun()
        })
        .catch((error) => {
          ElMessage.error('添加失败:' + error)
        })
    }
  })
}

// 关闭
const closeWarnFun = async (id) => {
  try {
    // 关闭的二次确认
    await message.confirm('确认关闭该告警吗？')
    // 发起关闭
    await AbnormalManageApi.getClose({ id })
    ElMessage.success('关闭成功')
    // 刷新列表
    await getWarnListFun()
  } catch {}
}

// 启用对话框相关
const confirmOpenDialog = ref(false)
const currentEnableId = ref(null)

// 打开启用对话框
const openFun = (id) => {
  currentEnableId.value = id
  confirmOpenDialog.value = true
}

// 确认启用
const confirmEnable = async () => {
  try {
    await AbnormalManageApi.getOpen({ id: currentEnableId.value })
    ElMessage.success('启用成功')
    await getWarnListFun()
    confirmOpenDialog.value = false
  } catch (error) {
    ElMessage.error('启用失败')
  }
}

// 取消启用
const cancelEnable = () => {
  confirmOpenDialog.value = false
}

// 删除
const removeWarnFun = async (id) => {
  // 执行删除操作
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AbnormalManageApi.deleteWarnConfiguration({ id })
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getWarnListFun()
  } catch {}
}

// 序号自增
const tableIndex = (index: number) => {
  return (form.pageNo - 1) * form.pageSize + index + 1
}

// 分页事件
const handleSizeChange = (val: number) => {
  form.pageSize = val
  getWarnListFun()
}
const handleCurrentChange = (val: number) => {
  form.pageNo = val
  getWarnListFun()
}
// 告警规则
const warnRuleList = ref({
  createTime: null,
  id: null,
  openFlag: null,
  warnCount: 0,
  warnDetail: '',
  warnMonCycle: 0
})
const getWarnRuleFun = async () => {
  const data = await AbnormalApi.getWarnRule()
  // console.log('打印告警规则', data)
  warnRuleList.value = data
}
</script>

<style lang="scss" scoped>
.my-component {
  background-color: white;
  display: flex;
  flex-direction: column;
  header {
    width: 100%;
    height: 60px;
    font-size: 18px;
    font-weight: bold;
    padding-left: 20px;
    display: flex;
    align-items: center;
  }
  main {
    flex: 1;
    width: 100%;

    .main-top {
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
}

// 状态按钮样式
.blue-class {
  background-color: #005fff; /* 蓝色背景 */
  color: #fff; /* 文字 */
  min-width: 50px;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.gray-class {
  background-color: #eff3f8; /* 蓝色背景 */
  color: #404a5b; /* 白色文字 */
  min-width: 50px;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// 弹窗样式
:deep(.el-dialog__header) {
  // border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
}

:deep(.el-dialog__footer) {
  // border-top: 1px solid #ebeef5;
  padding-top: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.dialog-footer .el-button {
  margin-left: 10px;
}
// 弹窗表单后文字样式
.append-icon-text {
  position: absolute;
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 100%;
  background-color: #dbdee5;
  text-align: center;
}
// 规则预览样式
.rules-box {
  width: 100%;
  height: 40px;
  padding-left: 25px; // 与表单项的 label 宽度对齐
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.demo-pagination-block {
  display: flex;
  justify-content: flex-end;
}
</style>
