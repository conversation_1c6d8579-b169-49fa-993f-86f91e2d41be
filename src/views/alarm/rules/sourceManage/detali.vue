<template>
  <div class="my-component">
    <header>
      <el-button class="ml-5 mr-5" type="primary" plain @click="goBack">返回</el-button>
      <p>告警源管理-详情</p>
    </header>
    <main>
      <div class="main-top">
        <div class="header-title">接入源详情</div>
        <div class="top-content">
          <el-descriptions class="descriptions-style" :column="1" border label-width="200">
            <el-descriptions-item
              label-class-name="my-label"
              v-for="(item, index) in descriptionsItems"
              :key="index"
            >
              <template #label>
                <div>{{ item.label }}</div>
              </template>
              <div class="description-value" v-html="item.value"></div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <div class="main-bottom">
        <div class="header-title">接入参数</div>
        <div class="bottom-content">
          <el-table
            :data="tableList"
            style="width: 100%; margin-bottom: 10px"
            :header-cell-style="{
              'background-color': '#F4F6F8',
              'font-size': '14px',
              color: '#3B3C3D'
            }"
          >
            <el-table-column prop="returnParam" label="返回参数" />
            <el-table-column prop="note" label="注释" />
          </el-table>
        </div>
      </div>
    </main>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, onBeforeRouteLeave } from 'vue-router'
import * as SourceManageApi from '@/api/alarm/rules/sourceManage' //引入api
import * as UTILS from './utils/index' //引入筛选的工具函数
const route = useRoute()

const descriptionsItems = ref([])
const params = reactive({ sourceId: '' })
onMounted(async () => {
  const sourceManageObj = sessionStorage.getItem('sourceManageObj')
  const newsourceManageObj = JSON.parse(sourceManageObj)
  // console.log('传递过来的数据', newsourceManageObj)
  if (sourceManageObj) {
    descriptionsItems.value = [
      { label: '接入告警源:', value: newsourceManageObj?.sourceName },
      { label: '对接方式:', value: newsourceManageObj?.connectionMethod },
      {
        label: '接入时间:',
        value: UTILS.timestampToDateOnlyString(newsourceManageObj?.accessTime)
      },
      { label: '接入状态:', value: generateStatusButton(newsourceManageObj?.accessStatus) },
      { label: '告警次数:', value: newsourceManageObj?.warnCount }
    ]
    params.sourceId = newsourceManageObj?.id
    getDetailBottomFun()
  } else {
    ElMessage.warning('已离开页面,请重新选择项目')
  }
})

function generateStatusButton(status) {
  const className = UTILS.accessStatusClass(status)
  const text = UTILS.accessStatus(status)
  return `<button class="${className}">${text}</button>`
}

// 使用 beforeRouteLeave 导航守卫
onBeforeRouteLeave((to, from) => {
  // 清除 sessionStorage 中的 equipmentDetailId
  // sessionStorage.removeItem('equipmentDetailId')
})

// 返回上一页
const goBack = () => {
  window.history.back()
}
const tableList = ref([])
const getDetailBottomFun = async () => {
  const data = await SourceManageApi.getDetailBottom(params)
  tableList.value = data
  // console.log('详情底部数据', data)
}
</script>

<style lang="scss" scoped>
.my-component {
  width: 100%;
  height: 100%;
  // background-color: #f6f7f9;
  background-color: white;
  header {
    width: 100%;
    height: 50px;
    background-color: white;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  main {
    width: 100%;
    height: 650px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .main-top {
      width: 100%;
      height: 250px;
      margin-bottom: 40px;
      // background-color: yellowgreen;
      .header-title {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        position: relative;
        font-family: Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #3b3c3d;
        text-align: left;
        font-style: normal;
        text-transform: none;
        line-height: 24px;
        margin: 3px 0;
        padding-left: 28px;

        &::before {
          margin: 10px 0 0 10px;
          content: '';
          position: absolute;
          left: 0;
          top: 6px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #005fff;
          border: 2px solid #d4e4ff;
        }
      }
      .top-content {
        padding: 0 15px;
        width: 100%;
        height: 200px;
      }
    }
    .main-bottom {
      width: 100%;
      height: 400px;
      // background-color: rgba(255, 192, 203, 0.628);
      .header-title {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        position: relative;
        font-family: Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #3b3c3d;
        text-align: left;
        font-style: normal;
        text-transform: none;
        line-height: 24px;
        margin: 3px 0;
        padding-left: 28px;

        &::before {
          margin: 10px 0 0 10px;
          content: '';
          position: absolute;
          left: 0;
          top: 6px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #005fff;
          border: 2px solid #d4e4ff;
        }
      }
      .bottom-content {
        padding: 0 15px;
      }
    }
  }
}

// 状态按钮样式
:deep(.stop-class) {
  background-color: #eff3f8;
  color: #919aaa;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.normal-class) {
  background-color: #005fff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.paused-class) {
  background-color: #eff3f8;
  color: #000;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
