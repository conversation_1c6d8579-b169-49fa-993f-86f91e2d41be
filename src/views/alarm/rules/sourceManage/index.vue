<template>
  <ContentWrap class="my-component">
    <header>告警源管理</header>
    <main>
      <el-table
        :data="tableData"
        height="530"
        v-loading="loading"
        style="width: 100%; margin-bottom: 10px"
        @selection-change="handleSelectionChange"
        :header-cell-style="{
          'background-color': '#F4F6F8',
          'font-size': '14px',
          color: '#3B3C3D'
        }"
      >
        <!-- 多选框列 -->
        <el-table-column type="selection" :selectable="selectable" width="55" />
        <!-- 其他列 -->
        <el-table-column type="index" :index="tableIndex" label="序号" width="80" />
        <el-table-column prop="sourceName" label="接入告警源" />
        <el-table-column prop="connectionMethod" label="对接方式" />
        <el-table-column prop="accessTime" label="接入时间">
          <template #default="scope">
            {{ UTILS.timestampToDateOnlyString(scope.row.accessTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="accessStatus" label="接入状态">
          <template #default="scope">
            <div>
              <button :class="UTILS.accessStatusClass(scope.row.accessStatus)">
                {{ UTILS.accessStatus(scope.row.accessStatus) }}</button
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="warnCount" label="告警次数" />

        <el-table-column prop="date" label="操作" fixed="right">
          <template #default="scope">
            <el-button
              v-if="scope.row.accessStatus === 0"
              link
              type="primary"
              size="small"
              @click="pausedFun(scope.row.id)"
              v-hasPermi="['infra:warn-source:update']"
              >暂停</el-button
            >
            <el-button
              v-if="scope.row.accessStatus === 1"
              link
              type="primary"
              size="small"
              @click="restoreFun(scope.row.id)"
              v-hasPermi="['infra:warn-source:update']"
              >恢复</el-button
            >
            <el-button
              v-if="scope.row.accessStatus === -1"
              link
              type="primary"
              size="small"
              @click="removeFun(scope.row.id)"
              v-hasPermi="['infra:warn-source:delete']"
              >删除</el-button
            >
            <el-button
              link
              type="primary"
              size="small"
              @click="goDetailFun(scope.row)"
              v-hasPermi="['infra:warn-source-param:query']"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页部分 -->
      <div class="demo-pagination-block">
        <el-pagination
          v-model:current-page="form.pageNo"
          v-model:page-size="form.pageSize"
          :page-sizes="[10, 20, 30, 50, 100]"
          :size="size"
          :disabled="disabled"
          :background="background"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </main>
  </ContentWrap>
</template>

<script lang="ts" setup>
import * as UTILS from './utils'
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import * as SourceManageApi from '@/api/alarm/rules/sourceManage' //引入api
const router = useRouter()
onMounted(() => {
  getSourceListFun()
})
// 分页相关
const loading = ref(true)
const total = ref(0)
const size = ref('default')
const background = ref(true)
const disabled = ref(false)

const form = reactive({
  pageNo: 1,
  pageSize: 10
})

// 其他事件
const handleSizeChange = (val: number) => {
  form.pageSize = val
  getSourceListFun() //重新获取
}
const handleCurrentChange = (val: number) => {
  form.pageNo = val
  getSourceListFun() //重新获取
}

const tableData = ref([])
const getSourceListFun = async () => {
  loading.value = true
  try {
    const data = await SourceManageApi.getSourceList(form)
    // console.log('打印告警源', data)
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
// 暂停
const pausedFun = async (id) => {
  try {
    await SourceManageApi.paused({ id })
    ElMessage.success('操作成功')
    getSourceListFun()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}
// 恢复
const restoreFun = async (id) => {
  try {
    await SourceManageApi.restore({ id })
    ElMessage.success('操作成功')
    getSourceListFun()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}
// 删除
const removeFun = async (id) => {
  try {
    await SourceManageApi.remove({ id })
    ElMessage.success('操作成功')
    getSourceListFun()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}
// 详情
const goDetailFun = (obj) => {
  // console.log('打印当前行数据', obj)
  sessionStorage.setItem('sourceManageObj', JSON.stringify(obj))
  router.push({
    path: '/alarm/rules/sourceManage/sourceManageDetail'
  })
}

// 多选框相关
const selectedRows = ref([]) // 选中的行数据
// 处理选中行变化
const handleSelectionChange = (rows) => {
  selectedRows.value = rows
  // 有选中的进行赋值用于导出使用
  // if (selectedRows.value.length) {
  //   formSearch.ids = rows.map((item) => item.id)
  // } else {
  //   formSearch.ids = [] //没有选中的置空
  // }
  // console.log('选中的行数据：', selectedRows.value)
}

// 控制哪些行可以被选择
const selectable = (row, index) => {
  // 例如：禁用第一行的选择
  return index !== -1
}
//序号自增
const tableIndex = (index: number) => {
  return (form.pageNo - 1) * form.pageSize + index + 1
}
</script>

<style lang="scss" scoped>
.my-component {
  background-color: white;
  display: flex;
  flex-direction: column;
  header {
    width: 100%;
    height: 60px;
    font-size: 18px;
    font-weight: bold;
    padding-left: 20px;
    display: flex;
    align-items: center;
  }
  main {
    flex: 1;
    width: 100%;

    // background-color: yellowgreen;
  }
}
// 状态按钮样式
.stop-class {
  background-color: #eff3f8; /* 蓝色背景 */
  color: #919aaa; /* 文字 */
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.normal-class {
  background-color: #005fff; /* 蓝色背景 */
  color: white; /* 白色文字 */
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.paused-class {
  background-color: #eff3f8; /* 蓝色背景 */
  color: #000; /* 文字 */
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
// 分页样式
.demo-pagination-block {
  display: flex;
  justify-content: flex-end;
}
</style>
