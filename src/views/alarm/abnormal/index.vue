<template>
  <div class="mr-8">
    <ContentWrap class="my-component">
      <div class="top-title">
        <div>异常告警设备列表</div>
        <div class="remind-box">
          <img src="@/assets/imgs/icons/sign-orange.png" alt="" />
          <div class="text-style">
            告警规则:{{ warnRuleList?.warnMonCycle }}天内产生{{
              warnRuleList?.warnCount
            }}次以上告警的设备
          </div>
        </div>
      </div>

      <div class="main">
        <el-row :gutter="20">
          <el-col :span="4" :xs="24">
            <div class="left-year">
              <DeptTree ref="treeRef" @node-click="handleDeptNodeClick" />
            </div>
          </el-col>
          <el-col :span="20" :xs="24">
            <!-- 右侧盒子 -->
            <div class="right-main">
              <!-- 表单部分 -->
              <ContentWrap class="form-box">
                <el-form
                  ref="queryFormRef"
                  :inline="true"
                  :model="formSearch"
                  class="form-search flex flex-wrap items-start -mb-15px"
                >
                  <el-form-item label="" class="!mr-3">
                    <el-input
                      v-model="formSearch.assets"
                      class="!w-240px"
                      clearable
                      placeholder="输入设备编号/名称"
                    />
                  </el-form-item>
                  <el-form-item label="区域" prop="areaId" class="!mr-3">
                    <Dictionary
                      v-model="formSearch.areaId"
                      type="cascader"
                      width="180px"
                      dict-type="oamArea"
                      :cascader-props="{
                        multiple: true,
                        checkStrictly: true,
                        label: 'name',
                        value: 'id'
                      }"
                      :max-collapse-tags="2"
                      placeholder="请选择区域"
                    />
                  </el-form-item>
                  <el-form-item label="所属项目:" prop="projectId" class="!mr-3">
                    <el-select
                      v-model="formSearch.projectId"
                      placeholder="选择所属项目"
                      multiple
                      collapse-tags
                      class="!w-140px"
                    >
                      <el-option
                        v-for="item in belongsProjectList"
                        :key="item.id"
                        :label="item.projectName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="重点设备" class="!mr-3">
                    <el-select
                      v-model="formSearch.isKey"
                      class="!w-140px"
                      clearable
                      placeholder="是否重点"
                    >
                      <el-option value="1" key="1" label="重点" />
                      <el-option value="0" key="0" label="非重点" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="告警来源:" prop="assetsSource" class="!mr-3">
                    <Dictionary
                      width="180px"
                      v-model="formSearch.assetsSource"
                      type="select"
                      dict-type="deviceSource"
                      placeholder="请选择告警来源"
                    />
                  </el-form-item>
                  <el-form-item label="生成时间:" prop="createTime" class="!mr-3">
                    <el-date-picker
                      v-model="formSearch.createTime"
                      format="YYYY/MM/DD HH:mm:ss"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    />
                  </el-form-item>

                  <div class="min-w-[850px] flex-1 flex justify-between mb-[18px] pl-2">
                    <div>
                      <XButton
                        class="text-button"
                        preIcon="ep:search"
                        title="查询"
                        width="75px"
                        gradient
                        @click="onSubmit"
                        v-hasPermi="['infra:warn-abnormal-assets:query']"
                      />
                      <el-button @click="resetForm"><Icon icon="ep:refresh" />重置</el-button>
                    </div>
                    <div>
                      <el-button
                        plain
                        @click="handleExport"
                        :loading="exportLoading"
                        v-hasPermi="['infra:warn-abnormal-assets:export']"
                      >
                        <Icon icon="ep:download" />导出
                      </el-button>
                      <el-button
                        plain
                        @click="allDeleteClick"
                        v-hasPermi="['infra:warn-abnormal-assets:delete']"
                      >
                        <Icon icon="ep:delete" />
                        移出
                      </el-button>
                    </div>
                  </div>
                </el-form>
              </ContentWrap>
              <!-- 表格部分 -->
              <div class="right-table">
                <el-table
                  :data="tableData"
                  height="530"
                  v-loading="loading"
                  style="width: 100%; margin-bottom: 10px"
                  @selection-change="handleSelectionChange"
                  @sort-change="handleSortChange"
                  :header-cell-style="{
                    'background-color': '#F4F6F8',
                    'font-size': '14px',
                    color: '#3B3C3D'
                  }"
                >
                  <!-- 多选框列 -->
                  <el-table-column type="selection" :selectable="selectable" min-width="55" />
                  <!-- 其他列 -->
                  <el-table-column type="index" :index="tableIndex" label="序号" min-width="60" />
                  <el-table-column
                    prop="assetsCode"
                    label="设备编号"
                    min-width="150"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <div
                        @click="goEquipmentDetail(scope.row.assetsCode)"
                        style="display: flex; align-items: center; color: blue; cursor: pointer"
                      >
                        <el-tooltip
                          content="重点设备"
                          placement="top"
                          v-if="scope?.row?.isKey === 1"
                        >
                          <span class="pt-1">
                            <img class="w5 h5" src="@/assets/imgs/icons/keynote.png" alt="" />
                          </span>
                        </el-tooltip>

                        {{ scope.row.assetsCode }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="assetsName"
                    label="设备名称"
                    min-width="150"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="projectName"
                    label="所属项目"
                    min-width="100"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="assetsTypeName"
                    label="设备类型"
                    min-width="100"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="area"
                    label="所属区域"
                    min-width="100"
                    show-overflow-tooltip
                  />

                  <el-table-column
                    prop="operatingUnitName"
                    label="运营商"
                    min-width="120"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="maintenanceUnit"
                    label="维保单位"
                    min-width="120"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="createTime"
                    label="生成时间"
                    min-width="180"
                    show-overflow-tooltip
                    sortable="custom"
                  >
                    <template #default="scope">
                      <div>{{ UTILS.timestampToDateString(scope.row.createTime) }}</div>
                    </template>
                  </el-table-column>

                  <el-table-column
                    prop="warnCount"
                    label="告警次数"
                    min-width="110"
                    show-overflow-tooltip
                    sortable="custom"
                  />

                  <el-table-column
                    prop="assetsSourceName"
                    label="告警来源"
                    min-width="120"
                    show-overflow-tooltip
                  />

                  <el-table-column prop="date" label="操作" min-width="100" fixed="right">
                    <template #default="scope">
                      <el-button link type="primary" size="small" @click="removeClick(scope.row.id)"
                        >移出</el-button
                      >
                      <el-button link type="primary" size="small" @click="handleClick(scope.row)"
                        >详情</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 分页部分 -->
                <div class="demo-pagination-block">
                  <el-pagination
                    v-model:current-page="form.pageNo"
                    v-model:page-size="form.pageSize"
                    :page-sizes="[10, 20, 30, 50, 100]"
                    :size="size"
                    :disabled="disabled"
                    :background="background"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  />
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </ContentWrap>
  </div>
</template>

<script lang="ts" setup>
import * as UTILS from './utils/index' //引入筛选的工具函数
import DeptTree from './components/DeptTree.vue'
import { hasPermission } from '@/directives/permission/hasPermi'
import * as WorkApi from '@/api/work'
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from 'vue'
import * as ListApi from '@/api/alarm/list' //引入api
import * as AbnormalApi from '@/api/alarm/abnormal' //引入api
import * as EquipmentApi from '@/api/operations/equipment'
import * as TicketsPushApi from '@/api/operations/ticketsPush'
import { getDictList } from '@/api/infra/deviceDict' //设备来源
import * as DynamicFormApi from '@/api/DynamicForm'
import { useRouter } from 'vue-router'
// import DeptTree from './components/DeptTree.vue'
import download from '@/utils/download'
// import * as UTILS from './utils'

import { ElMessage } from 'element-plus'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()

//序号自增
const tableIndex = (index: number) => {
  return (form.pageNo - 1) * form.pageSize + index + 1
}

onMounted(() => {
  getWarnRuleFun() //获得右上角 告警规则
  getType()
  getBelongsProjectFun() //获得所属项目
  // postListNoPageFun() //获得不分页的接口数据 如果数据存在调用第二个分页接口
  getWarnListFun()
  window.addEventListener('keydown', handleGlobalKeyDown)
})
onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleGlobalKeyDown)
})
const handleGlobalKeyDown = (event) => {
  if (event.key === 'Enter') {
    onSubmit()
  }
}
// 获得右上角 告警规则
const warnRuleList = ref({
  createTime: null,
  id: null,
  openFlag: null,
  warnCount: 0,
  warnDetail: '',
  warnMonCycle: 0
})
const getWarnRuleFun = async () => {
  const data = await AbnormalApi.getWarnRule()
  // console.log('打印告警规则', data)
  warnRuleList.value = data
}
//顶部两个下拉
const cascaderProps = reactive({
  value: 'id',
  label: 'name',
  children: 'children',
  multiple: true,
  checkStrictly: true,
  emitPath: false
})

const typeList = ref([])
const getType = async () => {
  const data = await TicketsPushApi.getType()
  // console.log('设备类型typeList', data)
  typeList.value = data
}
// 所属项目下拉
const belongsProjectList = ref([])
const getBelongsProjectFun = async () => {
  const data = await ListApi.getBelongsProject()
  belongsProjectList.value = data
  // console.log('打印所属项目', belongsProjectList.value)
}

// const postListNoPageFun = async () => {
//   const data = await AbnormalApi.postListNoPage(form)
//   // console.log('打印不分页接口', data)
//   if (data.length) {
//     // 存在数据调用下一个接口
//     formSearch.assetsCode = data.map((item) => item.assetsCode)
//     // console.log('打印拼接后设备编号', formSearch.assetsCode)
//     // const data = await EquipmentApi.postListNoPageFun(form)
//     getWarnListFun()
//   } else {
//     tableData.value = [] //查询不到手动置空
//     total.value = 0 //查询不到手动置空
//     //无数据清空展示并打断执行
//     return
//   }
// }

const getWarnListFun = async () => {
  loading.value = true
  try {
    const data = await AbnormalApi.getWarnList(formSearch)
    // console.log('打印分页数据', data.list)
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 表格数据
const tableData = ref([])

// 搜索表单数据
const form = reactive({
  pageNo: 1,
  pageSize: 10,
  assets: null, //设备编号/名称
  areaId: [], //所属区域
  projectId: [], //所属项目++
  isKey: null, //重点设备 是否重点
  assetsSource: null, //设备来源 告警来源
  assetsTypeId: [], //设备类型id 点击左侧列表存储
  // ====
  assetsName: null, //设备名称++
  operatingUnit: null, //运营商
  maintenanceUnit: null, //推送单位/维保单位
  warnId: null, //告警方式
  syncStatus: null //同步状态(0 未同步，1 已同步)
})

const formSearch = reactive({
  // 合并第一个表单数据
  assets: null, //设备编号/名称
  areaId: [], //所属区域
  projectId: [], //所属项目++
  isKey: null, //重点设备 是否重点
  assetsSource: null, //设备来源 告警来源
  assetsTypeId: [], //设备类型id 点击左侧列表存储
  // ====
  assetsName: null, //设备名称++
  operatingUnit: null, //运营商
  maintenanceUnit: null, //推送单位/维保单位
  warnId: null, //告警方式
  syncStatus: null, //同步状态(0 未同步，1 已同步)
  // 下面是第二个表单数据
  pageNo: 1,
  pageSize: 10,
  assetsCode: [], //设备编码
  createTime: [], //生成时间
  ids2: [], //多选框选中的id 批量导出使用
  orderParam: null, // 排序列名：如 "createTime", "warnCount"
  asc: null // 是否升序：true / false
})

// 分页相关
const loading = ref(true)
const total = ref(0)
const size = ref('default')
const background = ref(true)
const disabled = ref(false)

// 多选框相关
const selectedRows = ref([]) // 选中的行数据

// 处理选中行变化
const handleSelectionChange = (rows) => {
  selectedRows.value = rows
  // 有选中的进行赋值用于导出使用
  if (selectedRows.value.length) {
    formSearch.ids2 = rows.map((item) => item.id)
  } else {
    formSearch.ids2 = [] //没有选中的置空
  }
  // console.log('选中的行数据：', selectedRows.value)
}

// 控制哪些行可以被选择
const selectable = (row, index) => {
  // 例如：禁用第一行的选择
  return index !== -1
}

// 其他事件
const handleSizeChange = (val: number) => {
  form.pageSize = val
  formSearch.pageSize = val
  // postListNoPageFun() //重新获取
  getWarnListFun()
}
const handleCurrentChange = (val: number) => {
  form.pageNo = val
  formSearch.pageNo = val
  // postListNoPageFun() //重新获取
  getWarnListFun()
}

const handleClick = (obj) => {
  // console.log('打印当前行数据', obj)
  sessionStorage.setItem('alarmAbnormalObj', JSON.stringify(obj))
  router.push({
    path: '/alarm/alarmAbnormalDetail'
    // query: { id: id.toString() } // 将 id 值传递到详情页
  })
}

const sortParams = ref({
  prop: null,
  order: null
})

const handleSortChange = (column) => {
  const { prop, order } = column
  sortParams.value = { prop, order }

  // 支持按 "createTime" 和 "warnCount" 排序
  if (prop === 'createTime' || prop === 'warnCount') {
    formSearch.orderParam = prop
    formSearch.asc = order === 'ascending'
    console.log('打印赋值', formSearch.orderParam, formSearch.asc)
    onSubmit()
  }
}

const onSubmit = () => {
  formSearch.ids2 = [] //选中的数据置为空
  // postListNoPageFun()
  getWarnListFun()
}

const resetForm = () => {
  formSearch.assets = null //设备编号/名称
  formSearch.areaId = [] // 所属区域
  formSearch.projectId = [] //所属项目
  formSearch.isKey = null // 重点设备 是否重点
  formSearch.assetsSource = null // 设备来源 告警来源
  formSearch.assetsTypeId = [] //设备类型id 点击左侧列表存储

  // formSearch.deptId = null // 部门ID
  formSearch.assetsName = null // 设备名称
  formSearch.operatingUnit = null // 运营商
  formSearch.maintenanceUnit = null // 推送单位/维保单位
  formSearch.warnId = null // 告警方式
  formSearch.syncStatus = null // 同步状态

  // 清理第二个表单数据
  formSearch.pageNo = 1
  formSearch.pageSize = 10
  formSearch.assetsCode = []
  formSearch.createTime = []
  formSearch.ids2 = [] //选中的数据
  formSearch.orderParam = null //排序字段
  formSearch.asc = null //是否升序boolean

  // postListNoPageFun() // 调用获取列表的函数
  getWarnListFun()
}

// 处理项目被点击
const handleDeptNodeClick = async (row: { [key: string]: any }) => {
  form.assetsTypeId = [] //先清空在添加
  // console.log('父组件接收到的参数', row)
  form.assetsTypeId.push(row.id)
  // postListNoPageFun() //调用第一个接口
  getWarnListFun()
}

// 点击移出
const removeClick = async (id: number) => {
  // 执行删除操作
  try {
    // 删除的二次确认
    await message.removeConfirm()
    // 发起移出
    await AbnormalApi.removeWarn({ ids: id })
    message.success(t('common.removeSuccess'))
    // 刷新列表
    // await postListNoPageFun()
    formSearch.ids2 = []
    await getWarnListFun()
  } catch {}
}

// 批量移出 选中群体移出
const allDeleteClick = async () => {
  if (formSearch.ids2.length) {
    // 执行删除操作
    try {
      // 删除的二次确认
      await message.removeConfirm()
      // 发起移出

      await AbnormalApi.removeWarn({ ids: formSearch.ids2.join(',') })
      // ElMessage.success('暂未开发')
      message.success(t('common.removeSuccess'))
      // 刷新列表
      formSearch.ids2 = []
      await getWarnListFun()
    } catch {}
  } else {
    ElMessage.warning('请选择要移出的设备')
  }
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const newData = await AbnormalApi.exportWarnList(formSearch)
    download.excel(newData, '异常告警设备列表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

// 跳转设备详情 传递assetsCode
const goEquipmentDetail = (assetsCode) => {
  sessionStorage.removeItem('equipmentDetailId') //传id清除code 传code清除id
  sessionStorage.setItem('equipmentDetailCode', assetsCode.toString())
  router.push({
    path: '/operations/equipmentDetail'
    // query: { id: id.toString() } // 将 id 值传递到详情页
  })
}
</script>

<style lang="scss" scoped>
.my-component {
  // width: 100% !important;
  // height: 100% !important;
  background-color: white;
  display: flex;
  flex-direction: column;
  .top-title {
    width: 100%;
    height: 60px;
    font-size: 18px;
    font-weight: bold;
    padding-left: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    div:nth-child(2) {
      height: 40px;
      line-height: 40px;
      padding: 0 15px;
      font-size: 14px;
      font-weight: normal;
      margin-right: 30px;
      background-color: #fff9f6;
    }
  }
  .remind-box {
    display: flex;
    align-items: center;
  }

  .main {
    // flex: 1;
    // display: flex;
    // padding-left: 20px;
    .left-year {
      // width: 200px;
      // height: 600px;
      height: 100%;
      background-color: #f1f3f6;
      overflow-y: auto !important;
      // 隐藏滚动条
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
      }
      // margin-right: 10px;
    }

    // .right-main {
    //   flex: 1;
    //   display: flex;
    //   flex-direction: column;
    //   // overflow-x: auto;
    //   min-width: 0; /* 允许内容被裁剪而不是挤压其他元素 */
    //   // 隐藏滚动条
    //   scrollbar-width: none; /* Firefox */
    //   -ms-overflow-style: none; /* IE 10+ */
    //   &::-webkit-scrollbar {
    //     display: none; /* Chrome, Safari, Edge */
    //   }
    //   .form-box {
    //     width: 100%;
    //     display: flex;
    //     flex-direction: row;
    //     align-items: center;
    //     margin-bottom: 10px;
    //     overflow-x: auto;
    //     .form-search {
    //       flex: 1;
    //       display: flex;
    //       flex-direction: row;
    //       align-items: center;

    //       .el-form-item {
    //         margin-right: 10px;
    //       }
    //     }
    //   }

    //   .right-table {
    //     width: 100%;
    //     flex: 1;
    //     // padding-right: 45px;
    //     max-height: 540px; //设置最大高度溢出滚动
    //     overflow-y: auto;
    //     // 隐藏滚动条
    //     scrollbar-width: none; /* Firefox */
    //     -ms-overflow-style: none; /* IE 10+ */
    //     &::-webkit-scrollbar {
    //       display: none; /* Chrome, Safari, Edge */
    //     }
    //     :deep(.el-table__header th) {
    //       text-align: left;
    //     }
    //     :deep(.el-table td) {
    //       text-align: left;
    //     }
    //     .demo-pagination-block {
    //       margin-top: 10px;
    //     }
    //   }
    // }
    .demo-pagination-block {
      display: flex;
      justify-content: flex-end;
    }
  }
}
// 查询按钮样式
.text-button {
  color: #fff;
}

// 状态文字颜色
.status-normal {
  color: green;
}

.status-reported {
  color: orange;
}

.status-fault {
  color: red;
}
</style>
