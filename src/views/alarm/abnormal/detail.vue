<template>
  <ContentWrap class="my-component">
    <header>
      <el-button class="ml-5 mr-5" type="primary" plain @click="goBack">返回</el-button>
      <p>异常告警设备列表-详情</p>
    </header>
    <main>
      <div class="main-left">
        <div class="title-box">设备详情</div>
        <div class="detail-box">
          <PrivateShow :assetsCode="paramsLeft.assetsCode" />
        </div>
      </div>

      <div class="main-right">
        <div class="right-top">
          <div class="title-box">历史告警</div>
          <el-table
            :data="detailRightTable"
            style="width: 95%; margin-bottom: 10px; margin-left: 25px; height: 85%"
            :header-cell-style="{
              'background-color': '#F4F6F8',
              'font-size': '14px',
              color: '#3B3C3D'
            }"
          >
            <!-- 其他列 -->
            <el-table-column type="index" :index="tableIndex" label="序号" width="60" />
            <el-table-column prop="labelName" label="告警标签" width="120" show-overflow-tooltip>
              <template #default="scope">
                <span class="label-style">
                  {{ scope.row.labelName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="warnGradeName"
              label="告警等级"
              width="100"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div>
                  <button :class="UTILS.accessStatusClass(scope.row.warnGradeName)">
                    {{ scope.row.warnGradeName }}</button
                  >
                </div>
              </template>
            </el-table-column>

            <el-table-column
              prop="warnUpdateTime"
              label="最新更新时间"
              width="160"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div>{{ UTILS.timestampToDateString(scope.row.warnUpdateTime) }}</div>
              </template>
            </el-table-column>

            <el-table-column
              prop="warnCreateTime"
              label="告警生成时间"
              width="160"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div>{{ UTILS.timestampToDateString(scope.row.warnCreateTime) }}</div>
              </template>
            </el-table-column>

            <el-table-column prop="dealStatus" label="处理状态" width="100" show-overflow-tooltip>
              <template #default="scope">
                <div class="status-dot-container">
                  <span
                    class="status-dot"
                    :style="{ backgroundColor: UTILS.getStatusColor(scope.row.dealStatus) }"
                  ></span>
                  {{ UTILS.getStatusText(scope.row.dealStatus) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="dealUserName" label="处理人" width="100" show-overflow-tooltip />
          </el-table>
        </div>

        <div class="right-bottom">
          <div class="title-box">历史工单</div>
          <el-table
            :data="[]"
            style="width: 95%; margin-bottom: 10px; margin-left: 25px"
            :header-cell-style="{
              'background-color': '#F4F6F8',
              'font-size': '14px',
              color: '#3B3C3D'
            }"
          >
            <!-- 其他列 -->
            <el-table-column type="index" :index="tableIndex" label="序号" width="60" />
            <el-table-column prop="labelName" label="工单编号" width="120" show-overflow-tooltip />
            <el-table-column
              prop="warnGradeName"
              label="工单名称"
              width="100"
              show-overflow-tooltip
            />

            <el-table-column
              prop="warnUpdateTime"
              label="生成时间"
              width="160"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div>{{ UTILS.timestampToDateString(scope.row.warnUpdateTime) }}</div>
              </template>
            </el-table-column>

            <el-table-column
              prop="warnCreateTime"
              label="完成时间"
              width="160"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div>{{ UTILS.timestampToDateString(scope.row.warnCreateTime) }}</div>
              </template>
            </el-table-column>

            <el-table-column prop="dealStatus" label="工单状态" width="100" show-overflow-tooltip>
              <template #default="scope">
                <div>{{ UTILS.getStatusText(scope.row.dealStatus) }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="dealUserName" label="处理人" width="100" show-overflow-tooltip />
          </el-table>
        </div>
      </div>
    </main>
  </ContentWrap>
</template>

<script lang="ts" setup>
// import PrivateShow from './components/PrivateShow.vue' //引入私有组件
import PrivateShow from '@/components/PrivateShow/index.vue' //引入详情组件
import { ref, reactive, onMounted } from 'vue'
import { useRoute, onBeforeRouteLeave } from 'vue-router'
import * as AbnormalApi from '@/api/alarm/abnormal' //引入api
import * as ListApi from '@/api/alarm/list' //引入api
// import * as UTILS from './utils/index' //引入筛选的工具函数
import * as UTILS from '@/views/alarm/list/utils/index' //引入筛选的工具函数

const route = useRoute()
const paramsLeft = reactive({ assetsCode: '' })

const paramsRightTop = reactive({ assetsCode: '' })

onMounted(async () => {
  const alarmAbnormalObj = sessionStorage.getItem('alarmAbnormalObj')
  const newalarmAbnormalObj = JSON.parse(alarmAbnormalObj)
  // console.log('传递过来的数据', newalarmAbnormalObj)
  if (alarmAbnormalObj) {
    paramsLeft.assetsCode = newalarmAbnormalObj.assetsCode
    // console.log('赋值assetsCode', paramsLeft.assetsCode)
    paramsRightTop.assetsCode = ''
    paramsRightTop.assetsCode = newalarmAbnormalObj.assetsCode
    await getDetailRightTopFun()
  } else {
    ElMessage.warning('已离开页面,请重新选择项目')
  }
})

// 使用 beforeRouteLeave 导航守卫
onBeforeRouteLeave((to, from) => {
  // 清除 sessionStorage 中的 equipmentDetailId
  // sessionStorage.removeItem('equipmentDetailId')
})

// 右上角默认数据
const detailRightTable = ref([])
//序号自增
const form = reactive({
  pageNo: 1,
  pageSize: 10
})
const tableIndex = (index: number) => {
  return (form.pageNo - 1) * form.pageSize + index + 1
}
const getDetailRightTopFun = async () => {
  const data = await AbnormalApi.getDetailRightList(paramsRightTop)
  detailRightTable.value = data.list
  // console.log('右上角数据', detailRightTable.value)
}

// 返回上一页
const goBack = () => {
  window.history.back()
}
</script>

<style lang="scss" scoped>
.my-component {
  background-color: #f6f7f9;
  header {
    width: 100%;
    height: 50px;
    background-color: white;
    display: flex;
    align-items: center;
    // border-bottom: 1px solid #ccc;
    margin-bottom: 10px;
  }
  main {
    width: 100%;
    height: 650px;
    display: flex;
    .main-left {
      width: 60%;
      height: 100%;
      margin-right: 10px;

      background-color: white;
      .title-box {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        position: relative;
        font-family: Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #3b3c3d;
        text-align: left;
        font-style: normal;
        text-transform: none;
        line-height: 24px;
        margin: 3px 0;
        padding-left: 28px;

        &::before {
          margin: 10px 0 0 10px;
          content: '';
          position: absolute;
          left: 0;
          top: 6px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #005fff;
          border: 2px solid #d4e4ff;
        }
      }
      .detail-box {
        width: 97%;
        height: 90%;
        margin: 0 12px;
        display: flex;
        .descriptions-style {
          width: 100%;
          height: 100%;
        }
      }
    }
    .main-right {
      width: 40%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;

      .right-top {
        width: 95%;
        height: 52%;
        background-color: white;
        .title-box {
          width: 100%;
          height: 40px;
          display: flex;
          align-items: center;
          position: relative;
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 16px;
          color: #3b3c3d;
          text-align: left;
          font-style: normal;
          text-transform: none;
          line-height: 24px;
          margin: 3px 0;
          padding-left: 28px;

          &::before {
            margin: 10px 0 0 10px;
            content: '';
            position: absolute;
            left: 0;
            top: 6px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #005fff;
            border: 2px solid #d4e4ff;
          }
        }
      }

      .right-bottom {
        width: 95%;
        height: 45%;
        background-color: white;
        overflow-y: auto;
        &::-webkit-scrollbar {
          width: 6px; // 滚动条宽度
          height: 6px; // 滚动条高度
        }
        &::-webkit-scrollbar-thumb {
          background-color: #c1c1c1c1; // 滚动条颜色
          border-radius: 3px; // 滚动条圆角
        }

        &::-webkit-scrollbar-track {
          background-color: #f1f1f1f1; // 滚动条轨道颜色
          border-radius: 3px; // 滚动条轨道圆角
        }
        .title-box {
          width: 100%;
          height: 40px;
          display: flex;
          align-items: center;
          position: relative;
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 16px;
          color: #3b3c3d;
          text-align: left;
          font-style: normal;
          text-transform: none;
          line-height: 24px;
          margin: 3px 0;
          padding-left: 28px;

          &::before {
            margin: 10px 0 0 10px;
            content: '';
            position: absolute;
            left: 0;
            top: 6px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #005fff;
            border: 2px solid #d4e4ff;
          }
        }
      }
    }
  }
}
// 左侧label背景颜色
:deep(.my-label) {
  background: #f7f8f8 !important;
}
// 状态按钮样式
.low-class {
  min-width: 60px;
  background-color: #ffd910; /* 蓝色背景 */
  color: #000; /* 文字 */
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.middle-class {
  min-width: 60px;
  background-color: #ff9900; /* 蓝色背景 */
  color: white; /* 白色文字 */
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.top-class {
  min-width: 60px;
  background-color: #ff5e0b; /* 蓝色背景 */
  color: white; /* 文字 */
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.emergency-class {
  min-width: 60px;
  background-color: #f5002a; /* 蓝色背景 */
  color: white; /* 文字 */
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
// 圆点样式
.status-dot-container {
  display: flex;
  align-items: center;
  gap: 6px; // 圆点和文字之间的间距
}
.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #999; // 默认颜色，根据状态动态变化
}
// 左侧label背景颜色
:deep(.my-label) {
  background: #f7f8f8 !important;
}
// 告警标签
.label-style {
  padding: 3px 8px;
  border-radius: 5px;
  background-color: #f1f4f9;
}
</style>
