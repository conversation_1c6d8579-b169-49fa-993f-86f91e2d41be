export function transformData(data) {
  // 定义递归函数
  function transformItem(item) {
    // 创建一个新的对象，将 id 改为 label，将 name 改为 value
    const newItem = {
      label: item.name,
      value: item.id
    }
    // 如果有子节点，递归处理子节点
    if (item.children && item.children.length > 0) {
      newItem.children = item.children.map(transformItem)
    }
    return newItem
  }
  // 遍历数组，对每个元素调用递归函数
  return data.map(transformItem)
}
// 表格状态类
export function getStatusClass(status: number): string {
  switch (status) {
    case 0:
      return 'status-normal'
    case 1:
      return 'status-fault'
    default:
      return ''
  }
}
// 是否在保
export function identificationStatus(value) {
  switch (value) {
    case 0:
      return '质保中'
    case 1:
      return '已过保'
    default:
      return ''
  }
}
// 是否重点
export function identificationKey(value) {
  switch (value) {
    case 0:
      return '非重点'
    case 1:
      return '重点'
    default:
      return '未知'
  }
}
// 同步状态
export function identificationSyncStatus(value) {
  switch (value) {
    case 0:
      return '未同步'
    case 1:
      return '已同步'
    default:
      return '未知'
  }
}
//时间戳转化 保留时分秒
export function timestampToDateString(timestamp: number): string {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}
//时间戳转化 不保留时分秒
export function timestampToDateOnlyString(timestamp: number): string {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从0开始
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
// hikInfo字段
// 监控类型转换
export function identificationCameraType(value) {
  switch (value) {
    case 0:
      return '枪机'
    case 1:
      return '半球'
    case 2:
      return '快球'
    case 3:
      return '云台枪机'
    default:
      return ''
  }
}
// // 录像存储位置判断
// export function isRecordLocation(value) {
//   switch (value) {
//     case 0:
//       return '中心存储'
//     case 1:
//       return '设备存储'
//     default:
//       return ''
//   }
// }

// iotInfo 字段
// 节点类型
export function isNodeType(value) {
  switch (value) {
    case '0':
      return '设备'
    case '1':
      return '网关'
    case '2':
      return '子设备'
    default:
      return ''
  }
}
// 认证方式
export function isAuthType(value) {
  switch (value) {
    case '0':
      return '设备密钥'
    case '1':
      return 'X.509证书'
    default:
      return ''
  }
}
// 设备接入协议
export function isProtocol(value) {
  switch (value) {
    case '0':
      return 'MQTT'
    case '1':
      return 'CoAP/LWM2M'
    case '2':
      return 'HTTP'
    case '3':
      return 'TCP'
    case '4':
      return 'CoAP'
    case '5':
      return 'JT/T 808'
    default:
      return ''
  }
}

// 设备状态
export function isStatus(value) {
  switch (value) {
    case '0':
      return '在线'
    case '1':
      return '离线'
    case '2':
      return '未激活'
    default:
      return ''
  }
}
// 是否启用
export function isenabled(value) {
  return value === true ? '启用' : value === false ? '禁用' : ''
}
// 订阅模式
export function isObserveMode(value) {
  switch (value) {
    case '0':
      return '自动订阅'
    case '1':
      return '手动订阅'
    default:
      return ''
  }
}

// shElectricInfo 字段
export function isDevState(value) {
  switch (value) {
    case '0':
      return '待用'
    case '1':
      return '待复核'
    case '2':
      return '未通过'
    case '3':
      return '审核通过'
    case '4':
      return '启用'
    case '5':
      return '停用'
    case '6':
      return '废弃'
    case '7':
      return '报修'
    case '8':
      return '修复'
    default:
      return ''
  }
}
// zabbixInfo 字段
export function hostStatus(value) {
  switch (value) {
    case '0':
      return '启用'
    case '1':
      return '禁用'
    default:
      return ''
  }
}
export function linkMode(value) {
  switch (value) {
    case '1':
      return '不使用加密'
    case '2':
      return '使用TLS'
    case '3':
      return '仅PSK'
    case '4':
      return '仅证书'
    default:
      return ''
  }
}

export function validationMethod(value) {
  switch (value) {
    case '1':
      return '无'
    case '2':
      return 'PSK'
    case '4':
      return '证书'
    default:
      return ''
  }
}

export function maintenanceStatus(value) {
  switch (value) {
    case '0':
      return '未维护'
    case '1':
      return '维护中'
    default:
      return ''
  }
}

export function maintenanceType(value) {
  switch (value) {
    case '0':
      return '普通'
    case '1':
      return '活动'
    default:
      return ''
  }
}

export function isAvailable(value) {
  switch (value) {
    case '0':
      return '否'
    case '1':
      return '是'
    default:
      return ''
  }
}

export function fillModel(value) {
  switch (value) {
    case '-1':
      return '禁用'
    case '0':
      return ' 手动'
    case '1':
      return '自动'
    default:
      return ''
  }
}
