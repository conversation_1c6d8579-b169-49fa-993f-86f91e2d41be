<template>
  <div class="main">
    <!-- 顶部公共的 -->
    <header>
      <div class="detail-left">
        <!--  公共左侧描述列表 -->
        <el-descriptions :column="1" border label-width="200">
          <el-descriptions-item
            label-class-name="my-label"
            v-for="(item, index) in descriptionsItems"
            :key="index"
          >
            <template #label>
              <div>{{ item.label }}</div>
            </template>
            <!-- <div class="description-value" v-html="item.value">{{ item.value }}</div> -->
            <div class="description-value" v-html="item.value"></div>
          </el-descriptions-item>
        </el-descriptions>
        <!-- 公共左侧描述列表 -->
      </div>
      <div class="detail-right">
        <!--  公共右侧描述列表 -->
        <el-descriptions :column="1" border label-width="200">
          <el-descriptions-item
            label-class-name="my-label"
            v-for="(item, index) in descriptionsItems2"
            :key="index"
          >
            <template #label>
              <div>{{ item.label }}</div>
            </template>
            <div>{{ item.value }}</div>
          </el-descriptions-item>
        </el-descriptions>
        <!--  公共右侧描述列表 -->
      </div>
    </header>
    <!-- 底部私有的 -->
    <footer>
      <div class="private-box" v-if="detailList?.hikInfo !== null">
        <div class="private-left">
          <el-descriptions :column="1" border label-width="200">
            <el-descriptions-item
              label-class-name="my-label"
              v-for="(item, index) in hikInfoLeft"
              :key="index"
            >
              <template #label>
                <div>{{ item.label }}</div>
              </template>
              <div>{{ item.value }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="private-right">
          <el-descriptions :column="1" border label-width="200">
            <el-descriptions-item
              label-class-name="my-label"
              v-for="(item, index) in hikInfoRight"
              :key="index"
            >
              <template #label>
                <div>{{ item.label }}</div>
              </template>
              <div>{{ item.value }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <div class="private-box" v-else-if="detailList?.iotInfo !== null">
        <div class="private-left">
          <el-descriptions :column="1" border label-width="200">
            <el-descriptions-item
              label-class-name="my-label"
              v-for="(item, index) in iotInfoLeft"
              :key="index"
            >
              <template #label>
                <div>{{ item.label }}</div>
              </template>
              <div>{{ item.value }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="private-right">
          <el-descriptions :column="1" border label-width="200">
            <el-descriptions-item
              label-class-name="my-label"
              v-for="(item, index) in iotInfoRight"
              :key="index"
            >
              <template #label>
                <div>{{ item.label }}</div>
              </template>
              <div>{{ item.value }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <div class="private-box" v-else-if="detailList?.shElectricInfo !== null">
        <div class="private-left">
          <el-descriptions :column="1" border label-width="200">
            <el-descriptions-item
              label-class-name="my-label"
              v-for="(item, index) in shElectricInfoLeft"
              :key="index"
            >
              <template #label>
                <div>{{ item.label }}</div>
              </template>
              <div>{{ item.value }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="private-right">
          <el-descriptions :column="1" border label-width="200">
            <el-descriptions-item
              label-class-name="my-label"
              v-for="(item, index) in shElectricInfoRight"
              :key="index"
            >
              <template #label>
                <div>{{ item.label }}</div>
              </template>
              <div>{{ item.value }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <div class="private-box" v-else-if="detailList?.zabbixInfo !== null">
        <div class="private-left">
          <el-descriptions :column="1" border label-width="200">
            <el-descriptions-item
              label-class-name="my-label"
              v-for="(item, index) in zabbixInfoLeft"
              :key="index"
            >
              <template #label>
                <div>{{ item.label }}</div>
              </template>
              <div>{{ item.value }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="private-right">
          <el-descriptions :column="1" border label-width="200">
            <el-descriptions-item
              label-class-name="my-label"
              v-for="(item, index) in zabbixInfoRight"
              :key="index"
            >
              <template #label>
                <div>{{ item.label }}</div>
              </template>
              <div>{{ item.value }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </footer>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { defineProps } from 'vue'
// import * as TicketsPushApi from '@/api/operations/ticketsPush'
import * as AbnormalApi from '@/api/alarm/abnormal' //引入api

import * as UTILS from '../utils/conversion' //引入筛选的工具函数

const props = defineProps<{
  assetsCode: string | number
  keyFlag?: boolean
}>()

onMounted(async () => {
  // 确保渲染完成后在进行调用防止id不存在
  nextTick(() => {
    getDetail()
  })
  // await getDetail()
})
// 所有数据
const detailList = ref()
// 公共部分数据
const moreDetail = ref()
// 公共左侧默认数据
const descriptionsItems = ref([])
// 公共右侧默认数据
const descriptionsItems2 = ref([])
// ==============
// 私有数据
const hikInfoLeft = ref([])
const hikInfoRight = ref([])

const iotInfoLeft = ref([])
const iotInfoRight = ref([])

const shElectricInfoLeft = ref([])
const shElectricInfoRight = ref([])

const zabbixInfoLeft = ref([])
const zabbixInfoRight = ref([])

const getDetail = async () => {
  const res = await AbnormalApi.getDetail({ assetsCode: props.assetsCode })
  detailList.value = res //拿包含外层的数据
  moreDetail.value = res.deviceOverviewInfo //不包含外层的数据
  // console.log('打印详情数据', detailList.value)
  // 公共重新赋值方便循环渲染
  descriptionsItems.value = [
    { label: '设备编号:', value: moreDetail.value?.assetsCode },
    { label: '所属项目:', value: moreDetail.value?.projectName },
    { label: '设备类型:', value: moreDetail.value?.assetsTypeName },
    { label: '所属区域:', value: moreDetail.value?.area },
    { label: '供应商', value: moreDetail.value?.supplierName },
    // {
    //   label: '经纬度:',
    //   value: `${moreDetail.value?.longitude}<br>${moreDetail.value?.latitude}`
    // },
    {
      label: '经度:',
      value: `${moreDetail.value?.longitude ?? ''}`
    },
    {
      label: '纬度:',
      value: `${moreDetail.value?.latitude ?? ''}`
    },
    { label: '规格型号:', value: moreDetail.value?.modelName },
    { label: '质保状态:', value: UTILS.identificationStatus(moreDetail.value?.warrantyStatus) },
    { label: '设备来源:', value: moreDetail.value?.assetsSourceName },
    { label: 'IP地址:', value: moreDetail.value?.ip },
    // { label: '质保单位:', value: moreDetail.value?.maintenanceUnitName },
    { label: '维保单位:', value: moreDetail.value?.maintenanceUnitName }
  ]
  descriptionsItems2.value = [
    { label: '设备名称:', value: moreDetail.value?.assetsName },
    { label: '所属单位:', value: moreDetail.value?.deptName },
    { label: '同步状态:', value: UTILS.identificationSyncStatus(moreDetail.value?.syncStatus) },
    { label: '详细位置:', value: moreDetail.value?.address },
    { label: '二维码地址:', value: moreDetail.value?.qrCode },
    { label: '告警方式:', value: moreDetail.value?.warnName },
    { label: '质保日期:', value: moreDetail.value?.warrantyDate },
    { label: '创建时间:', value: UTILS.timestampToDateString(moreDetail.value?.createTime) },
    { label: '设备标签:', value: moreDetail.value?.label },
    { label: '是否重点:', value: UTILS.identificationKey(moreDetail.value?.isKey) },
    { label: '运营商:', value: moreDetail.value?.operatingUnitName }
  ]
  // 私有重新赋值
  hikInfoLeft.value = [
    { label: '海拔:', value: detailList.value?.hikInfo?.altitude },
    { label: '监控点名称:', value: detailList.value?.hikInfo?.name },
    {
      label: '监控点类型:',
      value: UTILS.identificationCameraType(detailList.value?.hikInfo?.cameraType)
    },
    { label: '能力集:', value: detailList.value?.hikInfo?.capabilitySetName },
    { label: '智能分析能力集:', value: detailList.value?.hikInfo?.intelligentSetName },
    { label: '通道类型:', value: detailList.value?.hikInfo?.channelTypeName },
    { label: '所属编码设备唯一标识:', value: detailList.value?.hikInfo?.encodeDevIndexCode },
    { label: '所属编码:', value: detailList.value?.hikInfo?.encodeDevIndexCode },
    { label: '所属设备类型:', value: detailList.value?.hikInfo?.encodeDevResourceTypeName },
    { label: '监控点国标:', value: detailList.value?.hikInfo?.gbIndexCode }
  ]
  hikInfoRight.value = [
    { label: '键盘控制码:', value: detailList.value?.hikInfo?.keyBoardCode },
    { label: '云台控制:', value: detailList.value?.hikInfo?.ptzControllerName },
    { label: '云镜类型:', value: detailList.value?.hikInfo?.ptzName },
    { label: '录像存储位置:', value: detailList.value?.hikInfo?.recordLocationName },
    { label: '所属区域:', value: detailList.value?.hikInfo?.regionIndexCode },
    { label: '状态:', value: detailList.value?.hikInfo?.statusName },
    { label: '传输协议类型:', value: detailList.value?.hikInfo?.transTypeName },
    { label: '接入协议:', value: detailList.value?.hikInfo?.treatyTypeName },
    { label: '安装位置:', value: detailList.value?.hikInfo?.installLocation },
    { label: '监控点标识:', value: detailList.value?.hikInfo?.cameraIndexCode }
  ]
  // 私有重新赋值
  iotInfoLeft.value = [
    { label: '设备编码:', value: detailList.value?.iotInfo?.iotId },
    { label: '备注信息:', value: detailList.value?.iotInfo?.description },
    { label: '设备密钥:', value: detailList.value?.iotInfo?.deviceSecret },
    // { label: '设备标签:', value: detailList.value?.iotInfo?.deviceTagList },//清除和公共部分重复字段
    { label: '租户id:', value: detailList.value?.iotInfo?.orgId },
    { label: '设备标识符:', value: detailList.value?.iotInfo?.deviceKey },
    // { label: '设备名称:', value: detailList.value?.iotInfo?.deviceName },//清除和公共部分重复字段
    { label: '设备创建者:', value: detailList.value?.iotInfo?.createdBy },
    // {
    //   label: '创建时间:',
    //   value: UTILS.timestampToDateOnlyString(detailList.value?.iotInfo?.createdOn)
    // },//清除和公共部分重复字段
    { label: '最近更新者:', value: detailList.value?.iotInfo?.lastUpdatedBy },
    {
      label: '最近更新时间:',
      value: UTILS.timestampToDateOnlyString(detailList.value?.iotInfo?.lastUpdatedOn)
    },
    {
      label: '激活时间:',
      value: UTILS.timestampToDateOnlyString(detailList.value?.iotInfo?.activatedOn)
    },
    {
      label: '最后上线时间:',
      value: UTILS.timestampToDateOnlyString(detailList.value?.iotInfo?.lastOnlineOn)
    },
    {
      label: '设备终端注册实体:',
      value: detailList.value?.iotInfo?.deviceTerminalRegistrationEntity
    },
    { label: '	设备鉴权码:', value: detailList.value?.iotInfo?.authCode }
  ]
  iotInfoRight.value = [
    // { label: 'IP地址:', value: detailList.value?.iotInfo?.ip },//清除和公共部分重复字段
    { label: '节点类型:', value: UTILS.isNodeType(detailList.value?.iotInfo?.nodeType) },
    { label: '认证方式:', value: UTILS.isAuthType(detailList.value?.iotInfo?.authType) },
    {
      label: '设备接入协议:',
      value: UTILS.isProtocol(detailList.value?.iotInfo?.deviceConnectionProtocol)
    },
    { label: '设备所属的产品名称:', value: detailList.value?.iotInfo?.productName },
    { label: '设备状态:', value: UTILS.isStatus(detailList.value?.iotInfo?.status) },
    { label: '使用状态:', value: UTILS.isenabled(detailList.value?.iotInfo?.enabled) },
    { label: '设备IMEI:', value: detailList.value?.iotInfo?.imei },
    { label: '设备位置信息:', value: detailList.value?.iotInfo?.position },
    { label: '设备psk密钥:', value: detailList.value?.iotInfo?.psk },
    { label: '生命周期:', value: detailList.value?.iotInfo?.lifetime },
    { label: '订阅模式:', value: UTILS.isObserveMode(detailList.value?.iotInfo?.observeMode) }
  ]
  // 私有重新赋值
  shElectricInfoLeft.value = [
    { label: '主键:', value: detailList.value?.shElectricInfo?.id },
    { label: '设备编号:', value: detailList.value?.shElectricInfo?.devNo },
    // { label: '设备名称:', value: detailList.value?.shElectricInfo?.devName },//清除和公共部分重复字段
    // { label: '设备类型:', value: detailList.value?.shElectricInfo?.devType },//清除和公共部分重复字段
    {
      label: '购置日期:',
      value: UTILS.timestampToDateOnlyString(detailList.value?.shElectricInfo?.purchaseDate)
    },
    { label: '品牌:', value: detailList.value?.shElectricInfo?.brand },
    { label: '设备状态:', value: UTILS.isDevState(detailList.value?.shElectricInfo?.devState) },
    // { label: '创建时间:', value: detailList.value?.shElectricInfo?.createTime },
    { label: '批次:', value: detailList.value?.shElectricInfo?.batch },
    { label: '型号:', value: detailList.value?.shElectricInfo?.devModel },
    { label: '资产原值:', value: detailList.value?.shElectricInfo?.originalPay },
    { label: '净残率:', value: detailList.value?.shElectricInfo?.badRate },
    { label: '质保年限:', value: detailList.value?.shElectricInfo?.useYear },
    { label: '质保开始年限:', value: detailList.value?.shElectricInfo?.startTime }
  ]

  shElectricInfoRight.value = [
    { label: '质保结束:', value: detailList.value?.shElectricInfo?.endTime },
    { label: '折旧年限:', value: detailList.value?.shElectricInfo?.depreciationTime },
    { label: '负责人:', value: detailList.value?.shElectricInfo?.chargeUser },
    { label: '联系方式:', value: detailList.value?.shElectricInfo?.contact },
    { label: '描述:', value: detailList.value?.shElectricInfo?.description },
    {
      label: '折旧开始时间:',
      value: UTILS.timestampToDateOnlyString(detailList.value?.shElectricInfo?.depreciationStart)
    },
    { label: '端口:', value: detailList.value?.shElectricInfo?.port },
    { label: '单位id:', value: detailList.value?.shElectricInfo?.unitId },
    { label: '项目id:', value: detailList.value?.shElectricInfo?.projectId },
    { label: '养护类型:', value: detailList.value?.shElectricInfo?.repairType }
  ]
  // 私有重新赋值
  zabbixInfoLeft.value = [
    { label: '设备编码:', value: detailList.value?.zabbixInfo?.hostid },
    { label: '监控主机ID:', value: detailList.value?.zabbixInfo?.proxyid },
    { label: '主机技术名称:', value: detailList.value?.zabbixInfo?.host },
    { label: '主机状态:', value: UTILS.hostStatus(detailList.value?.zabbixInfo?.status) },
    { label: 'IPMI身份验证算法:', value: detailList.value?.zabbixInfo?.ipmiAuthtype },
    { label: 'IPMI特权级别:', value: detailList.value?.zabbixInfo?.ipmiPrivilege },
    { label: 'IPMI用户名:', value: detailList.value?.zabbixInfo?.ipmiUsername },
    { label: 'IPMI密码:', value: detailList.value?.zabbixInfo?.ipmiPassword },
    { label: '当前在主机上有效的维护的ID:', value: detailList.value?.zabbixInfo?.maintenanceid },
    {
      label: '有效的维护状态:',
      value: UTILS.maintenanceStatus(detailList.value?.zabbixInfo?.maintenanceStatus)
    },
    {
      label: '有效的维护类型:',
      value: UTILS.maintenanceType(detailList.value?.zabbixInfo?.maintenanceType)
    },
    { label: '有效维护的开始时间:', value: detailList.value?.zabbixInfo?.maintenanceFrom },
    { label: '主机显示的名称:', value: detailList.value?.zabbixInfo?.name },
    { label: '主机的来源:', value: detailList.value?.zabbixInfo?.flags },
    // { label: '来自主机的连接:', value: detailList.value?.zabbixInfo?.tlsAccept },//TLS验证方式
    { label: 'TLS连接模式', value: UTILS.linkMode(detailList.value?.zabbixInfo?.tlsConnect) }
  ]
  zabbixInfoRight.value = [
    { label: '证书颁发者:', value: detailList.value?.zabbixInfo?.tlsIssuer },
    { label: '证书主题:', value: detailList.value?.zabbixInfo?.tlsSubject },
    { label: '自定义接口:', value: detailList.value?.zabbixInfo?.customInterfaces },
    { label: 'uuid:', value: detailList.value?.zabbixInfo?.uuid },
    { label: '供应商名称:', value: detailList.value?.zabbixInfo?.vendorName },
    { label: '供应商版本:', value: detailList.value?.zabbixInfo?.vendorVersion },
    { label: '监控主机的proxy组的ID:', value: detailList.value?.zabbixInfo?.proxyGroupid },
    { label: '监控主机的来源:', value: detailList.value?.zabbixInfo?.monitoredBy },
    {
      label: '主机资产清单的填充模式:',
      value: UTILS.fillModel(detailList.value?.zabbixInfo?.inventoryMode)
    },
    {
      label: '主机活动接口的可用状态:',
      value: UTILS.isAvailable(detailList.value?.zabbixInfo?.activeAvailable)
    },
    {
      label: '如果主机是被proxy组监控则为Zabbix server分配的proxy的ID:',
      value: detailList.value?.zabbixInfo?.assignedProxyid
    },
    { label: 'templateid:', value: detailList.value?.zabbixInfo?.templateid },
    { label: '主机的描述:', value: detailList.value?.zabbixInfo?.description },
    // { label: '主机的连接:', value: detailList.value?.zabbixInfo?.tlsConnect },//TLS连接模式
    { label: 'TLS验证方式', value: UTILS.validationMethod(detailList.value?.zabbixInfo?.tlsAccept) }
  ]
}
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
  // height: auto;
  // background-color: pink;
  // padding-bottom: 50px;
  margin-bottom: 50px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 6px; // 滚动条宽度
    height: 6px; // 滚动条高度
  }
  &::-webkit-scrollbar-thumb {
    background-color: #c1c1c1c1; // 滚动条颜色
    border-radius: 3px; // 滚动条圆角
  }
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1f1; // 滚动条轨道颜色
    border-radius: 3px; // 滚动条轨道圆角
  }
  header {
    width: 100%;
    display: flex;

    .detail-left {
      width: 48%;
    }
    .detail-right {
      width: 48%;
    }
  }
  footer {
    width: 100%;
    .private-box {
      width: 100%;
      display: flex;
      .private-left {
        width: 48%;
      }
      .private-right {
        width: 48%;
      }
    }
  }
}
.description-value {
  max-width: 200px;
  white-space: normal !important; // 允许内容换行
  word-wrap: break-word !important; // 允许长单词或 URL 地址换行到下一行
}
</style>
