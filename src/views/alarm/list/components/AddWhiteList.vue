<template>
  <Dialog v-model="dialogVisible" width="550" title="添加白名单">
    <el-form
      ref="formRef"
      :model="localForm"
      label-width="120px"
      size="small"
      label-position="left"
      style="height: 100px"
    >
      <!-- 设置白名单有效期 -->
      <el-form-item label="设置白名单有效期:" prop="validityPeriod">
        <el-date-picker
          v-model="localForm.validityPeriod"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex; align-items: center; justify-content: center; gap: 15px">
        <el-button type="primary" @click="submitForm">确认添加</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
// import { useI18n } from 'vue-i18n';
// import { useMessage } from '/@/hooks/web/useMessage';
import { FormInstance } from 'element-plus'
import * as ListApi from '@/api/alarm/list' // 引入 API

// const { t } = useI18n(); // 国际化
// const message = useMessage(); // 消息弹窗

const dialogVisible = ref(false) // 弹窗的显示状态

// 表单 Ref
const formRef = ref<FormInstance>()

// 父组件传递的 whiteListSaveReqListVOList 数组
const props = defineProps({
  whiteListSaveReqListVOList: {
    type: Array,
    default: () => [] // 默认值为空数组
  }
})

// 创建本地表单数据副本
const localForm = reactive({
  validityPeriod: null // 白名单有效期
})

/** 打开弹窗 */
const open = () => {
  dialogVisible.value = true
}

/** 提交表单 */
const emit = defineEmits(['success'])
const submitForm = async () => {
  if (!localForm.validityPeriod) {
    ElMessage.error('请选择白名单有效期')
    // console.error('请选择白名单有效期')
    return
  }

  // 组织接口所需的参数格式
  const payload = {
    // warnWhiteListSaveReqVO: {
    whiteListSaveReqListVOList: props.whiteListSaveReqListVOList,
    startDate: localForm.validityPeriod[0] ? localForm.validityPeriod[0] : null,
    endDate: localForm.validityPeriod[1] ? localForm.validityPeriod[1] : null
    // }
  }

  // 在这里添加提交逻辑，例如调用 API
  try {
    await ListApi.postAddWhiteList(payload)
    ElMessage.success('添加白名单成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    // ElMessage.error('添加失败，请稍后重试')//已有接口返回报错
  }
}

// 暴露 open 方法，以便父组件可以通过 ref 调用
defineExpose({ open })
</script>

<style scoped>
:deep(.el-dialog__header) {
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 15px;
}

:deep(.el-dialog__footer) {
  border-top: none;
  padding-top: 0;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-button {
  width: 100px;
}
</style>
