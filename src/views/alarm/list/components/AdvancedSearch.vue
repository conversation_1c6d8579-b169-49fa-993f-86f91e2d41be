<template>
  <Dialog v-model="dialogVisible" width="1000" title="高级搜索">
    <el-form ref="formRef" :model="searchFormData" label-width="100px">
      <!-- 设备编号 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="设备编号:" prop="assetsCode">
            <el-input v-model="searchFormData.assetsCode" placeholder="请输入设备编号" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 设备名称 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="设备名称:" prop="assetsName">
            <el-input v-model="searchFormData.assetsName" placeholder="请输入设备名称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="设备类型">
            <el-cascader
              style="width: 100%"
              v-model="searchFormData.assetsTypeId"
              :options="typeList"
              :props="cascaderProps"
              collapse-tags
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 重点设备 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="重点设备:" prop="isKey">
            <el-select v-model="searchFormData.isKey" placeholder="是否重点">
              <el-option value="1" key="1" label="重点" />
              <el-option value="0" key="0" label="非重点" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 告警方式 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="告警方式:" prop="warnWarnId">
            <el-select v-model="searchFormData.warnWarnId" placeholder="选择告警方式">
              <el-option
                v-for="item in warnIdList"
                :key="item.id"
                :label="item.warnTypeName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 告警标签 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="告警标签:" prop="warnLabelId">
            <el-select v-model="searchFormData.warnLabelId" placeholder="选择告警标签">
              <el-option
                v-for="item in warnLabelList"
                :key="item.id"
                :label="item.labelName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 所属区域 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="所属区域:" prop="areaId">
            <Dictionary
              v-model="searchFormData.areaId"
              type="cascader"
              dict-type="oamArea"
              :cascader-props="{
                multiple: true,
                label: 'name',
                value: 'id'
              }"
              :max-collapse-tags="2"
              placeholder="请选择区域"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 所属项目 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="所属项目:" prop="projectId">
            <el-select
              v-model="searchFormData.projectId"
              placeholder="选择所属项目"
              multiple
              collapse-tags
              style="width: 100%"
            >
              <el-option
                v-for="item in belongsProjectList"
                :key="item.id"
                :label="item.projectName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 运营商 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="运营商:" prop="operatingUnit">
            <el-select v-model="searchFormData.operatingUnit" placeholder="选择运营商">
              <el-option
                v-for="item in operatingUnitList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 维保单位 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="维保单位:" prop="maintenanceUnit">
            <el-select v-model="searchFormData.maintenanceUnit" placeholder="选择维保单位">
              <el-option
                v-for="item in maintenanceUnitList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 告警来源 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="告警来源:" prop="assetsSource">
            <Dictionary
              v-model="searchFormData.assetsSource"
              type="select"
              dict-type="deviceSource"
              placeholder="请选择告警来源"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 最新更新时间 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="最新更新时间:" prop="warnUpdateTime">
            <el-date-picker
              v-model="searchFormData.warnUpdateTime"
              format="YYYY/MM/DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 告警生成时间 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="告警生成时间:" prop="warnCreateTime">
            <el-date-picker
              v-model="searchFormData.warnCreateTime"
              format="YYYY/MM/DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div style="display: flex; align-items: center; justify-content: center">
        <XButton title="确定" width="200px" gradient @click="submitForm" />
        <el-button style="width: 200px" @click="resetForm">重置</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, watch, reactive, onMounted } from 'vue'
import { getDeviceType } from '@/api/EquipmentManagement/ManagementCenter/index'
import * as DynamicFormApi from '@/api/DynamicForm'
import * as ListApi from '@/api/alarm/list' //引入api
import * as TicketsPushApi from '@/api/operations/ticketsPush'
defineOptions({ name: 'AdvancedSearch' })

// 定义父组件传递的搜索条件
const props = defineProps({
  // form: {
  //   type: Object,
  //   default: () => ({})
  // },
  formSearchPage: {
    type: Object,
    default: () => ({})
  }
})

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

onMounted(() => {
  getWarnIdList() //获得告警方式
  getWarnLabelFun() //获得告警标签下拉
  getBelongsProjectFun() //获得所属项目下拉
  getOperatingUnitFun() //获得运营商下拉
  getMaintenanceUnitFun() //获得维保单位下拉
  getType() //获得设备类型下拉
})

const warnIdList = ref([]) // 告警方式
const getWarnIdList = async () => {
  const data = await DynamicFormApi.getWarning()
  // console.log('告警方式', data)
  warnIdList.value = data
}

// 搜索表单的告警标签
const warnLabelList = ref([])
const getWarnLabelFun = async () => {
  const data = await ListApi.getWarnLabel()
  warnLabelList.value = data
  // console.log('打印告警标签', warnLabelList.value)
}
// 设备类型
const cascaderProps = reactive({
  value: 'id',
  label: 'name',
  children: 'children',
  multiple: true,
  checkStrictly: true,
  emitPath: false
})
const typeList = ref([])
const getType = async () => {
  const data = await TicketsPushApi.getType()
  // console.log('设备类型typeList', data)
  typeList.value = data
}
// 所属项目下拉
const belongsProjectList = ref([])
const getBelongsProjectFun = async () => {
  const data = await ListApi.getBelongsProject()
  belongsProjectList.value = data
  // console.log('打印所属项目', belongsProjectList.value)
}

// 运营商下拉
const operatingUnitList = ref([]) //运营商/运营单位

const getOperatingUnit = async (type: string) => {
  const unitTypeData = await DynamicFormApi.getUnitType({ code: type })
  const unitData = await DynamicFormApi.getUnit({ typeId: unitTypeData })
  return unitData
}
const getOperatingUnitFun = async () => {
  const data = await getOperatingUnit('operation')
  // console.log('运营商/运维单位', data)
  operatingUnitList.value = data
}
// 维保单位下拉
const maintenanceUnitList = ref([]) //维保单位
const getMaintenanceUnitFun = async () => {
  const data = await getOperatingUnit('maintenance')
  // console.log('维保单位', data)
  maintenanceUnitList.value = data
}

// 级联选择器的属性配置
const props1 = {
  value: 'id',
  label: 'name',
  children: 'children',
  multiple: true
}

const dialogVisible = ref(false) // 弹窗的显示状态

const DeviceType = ref([]) // 设备类型数据

// 创建本地表单数据副本
// const localForm = reactive({})

// 创建 searchFormData，用于存储特定字段
const searchFormData = reactive({
  pageNo: 1,
  pageSize: 10,
  // 合并来的↓
  assetsCode: '',
  assetsName: '',
  assetsTypeId: [], //设备类型 资产类型
  isKey: null,
  warnWarnId: '',
  areaId: [],
  projectId: [], // 修改为数组，支持多选
  operatingUnit: '',
  maintenanceUnit: '',
  assetsSource: '',
  // 合并前↓
  warnLabelId: '',
  warnUpdateTime: [],
  warnCreateTime: []
})

// 表单 Ref
const formRef = ref()

/** 打开弹窗 */
const open = () => {
  // 在打开弹窗时，同步父组件的最新数据
  // Object.assign(localForm, props.form)
  Object.assign(searchFormData, props.formSearchPage)
  dialogVisible.value = true
}
// defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false
}

/** 提交表单 */
const emit = defineEmits(['success'])
const submitForm = async () => {
  // 将本地数据回传到父组件
  emit('success', {
    // form: { ...localForm },
    formSearchPage: { ...searchFormData }
  })
  closeDialog() // 关闭弹窗
}

/** 加载设备类型数据 */
onMounted(async () => {
  DeviceType.value = await getDeviceType()
})

// 重置表单
// const resetForm = () => {
//   // for (const key in searchFormData) {
//   //   if (Array.isArray(searchFormData[key])) {
//   //     searchFormData[key] = []
//   //   } else if (typeof searchFormData[key] === 'object' && searchFormData[key] !== null) {
//   //     for (const k in searchFormData[key]) {
//   //       searchFormData[key][k] = null
//   //     }
//   //   } else {
//   //     searchFormData[key] = null
//   //   }
//   // }
//   searchFormData.assetsCode = ''
//   searchFormData.assetsName = ''
//   searchFormData.isKey = null
//   searchFormData.warnId = ''
//   searchFormData.warnLabelId = ''
//   searchFormData.areaId = []
//   searchFormData.projectId = []
//   searchFormData.operatingUnit = ''
//   searchFormData.maintenanceUnit = ''
//   searchFormData.assetsSource = ''
//   searchFormData.warnUpdateTime = []
//   searchFormData.warnCreateTime = []
// }
const resetForm = () => {
  searchFormData.pageNo = 1
  searchFormData.pageSize = 10
  searchFormData.assetsCode = ''
  searchFormData.assetsName = ''
  searchFormData.isKey = null
  searchFormData.warnWarnId = ''
  searchFormData.warnLabelId = ''
  searchFormData.areaId = []
  searchFormData.projectId = []
  searchFormData.operatingUnit = ''
  searchFormData.maintenanceUnit = ''
  searchFormData.assetsSource = ''
  searchFormData.warnUpdateTime = []
  searchFormData.warnCreateTime = []
}
// 暴露 resetForm 方法，以便父组件可以通过 ref 调用
defineExpose({ open, resetForm })

// 监听父组件传递的搜索条件变化，并更新子组件的表单数据
// watch(
//   () => props.form,
//   (newVal) => {
//     // 深度克隆父组件的 form 数据到子组件的 localForm
//     Object.assign(localForm, newVal)
//   },
//   { deep: true }
// )

watch(
  () => props.formSearchPage,
  (newVal) => {
    // 深度克隆父组件的 formSearchPage 数据到子组件的 searchFormData
    Object.assign(searchFormData, newVal)
  },
  { deep: true }
)
</script>
