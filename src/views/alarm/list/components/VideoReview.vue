<template>
  <el-dialog
    v-model="dialogVisible"
    title="视频复核"
    width="80%"
    custom-class="video-dialog"
    @close="handleClose"
  >
    <div class="video-container">
      <video ref="videoPlayer" class="video-player" controls>
        <source :src="videoUrl" type="video/mp4" />
        您的浏览器不支持视频播放。
      </video>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  id: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['close'])

const dialogVisible = ref(false)
const videoUrl = ref<string>('')
const videoPlayer = ref(null)

// 获取视频数据
const getVideoData = async (id: number) => {
  try {
    // 模拟 API 请求
    // 实际项目中替换为真实的 API 调用
    console.log('请求视频数据，ID:', id)
    // 模拟视频 URL
    videoUrl.value =
      'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
  } catch (error) {
    ElMessage.error('获取视频数据失败')
  }
}

// 打开对话框
const open = (id: number) => {
  if (id) {
    dialogVisible.value = true
    getVideoData(id)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  emit('close')
  // 重置视频 URL
  videoUrl.value = ''
}

// 页面加载时初始化视频播放器
onMounted(() => {
  if (videoPlayer.value) {
    videoPlayer.value.addEventListener('loadeddata', () => {
      console.log('视频加载完成')
    })
  }
})

// 定义 open 方法以便父组件调用
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
// .video-dialog {
//   .video-container {
//     width: 100%;
//     .video-player {
//       width: 100%;
//       height: auto;
//       img {
//         width: 100%;
//       }
//     }
//   }
// }
// =================
// .video-dialog {
//   .video-container {
//     width: 100%;
//     .video-player {
//       width: 100%;
//       max-width: 100%;
//       height: auto;
//       display: block;
//       object-fit: contain; // 确保图片按比例显示，不裁剪
//       background-size: cover; // 如果浏览器不支持 object-fit，可以加背景图方式兜底
//       background-position: center;
//     }
//   }
// }
// ===========

.video-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }

  .video-container {
    width: 100%;
    aspect-ratio: 16 / 9; // 可选：保持视频比例
    position: relative;
    overflow: hidden;
    background-color: #000;

    .video-player {
      width: 100%;
      height: 100%;
      object-fit: contain;
      background-size: cover;
      background-position: center;
    }
  }
}
</style>
