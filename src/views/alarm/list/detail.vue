<template>
  <ContentWrap class="my-component">
    <header>
      <el-button class="ml-5 mr-5" type="primary" plain @click="goBack">返回</el-button>
      <p>告警列表-详情</p>
    </header>
    <main>
      <div class="main-left">
        <div class="title-content-box">
          <div class="title-box">告警详情</div>
          <div class="btn-box">
            <el-button v-if="showPushBtn" type="primary" @click="pushDetailFun"
              >推送详情</el-button
            ></div
          >
        </div>

        <div class="detail-box">
          <el-descriptions class="descriptions-style" :column="1" border label-width="200">
            <el-descriptions-item
              label-class-name="my-label"
              v-for="(item, index) in descriptionsItems"
              :key="index"
            >
              <template #label>
                <div>
                  {{ item.label }}
                </div>
              </template>
              <div class="description-value">
                <div
                  class="value-status-container"
                  v-if="item?.value != null && item?.dealStatus != null"
                >
                  <div
                    style="display: flex alin-items-center"
                    :class="[item.isAlarmLabel ? 'label-style' : '', item.warnStyle]"
                    :style="{
                      color: item.link ? 'blue' : '',
                      cursor: item.link ? 'pointer' : '',
                      display: 'flex',
                      alignItems: 'center'
                    }"
                    @click="item.link ? goEquipmentDetail(item.assetsCode) : null"
                  >
                    {{ item.value }}
                    <el-tooltip content="重点设备" placement="top" v-if="item?.value && item.isKey">
                      <span>
                        <img
                          style="
                            width: 26px;
                            height: 26px;
                            margin-left: 5px;
                            vertical-align: middle;
                          "
                          src="@/assets/imgs/icons/keynote.png"
                          alt=""
                        />
                      </span>
                    </el-tooltip>

                    <el-tooltip
                      :content="item.warnTypeDescription || '无'"
                      placement="top"
                      :show-if-overflow="false"
                      v-if="item?.value && item.warnTypeDescription"
                    >
                      <img
                        v-if="item.icon"
                        src="@/assets/imgs/icons/faultTab2.png"
                        alt=""
                        style="width: 16px; height: 16px; margin-left: 5px; vertical-align: middle"
                      />
                    </el-tooltip>
                  </div>
                  <div class="label-style"
                    ><span
                      class="status-dot"
                      :style="{
                        backgroundColor: UTILS.getStatusColor(item.dealStatus),
                        marginRight: '5px'
                      }"
                    ></span>
                    <span>{{ UTILS.getStatusText(item.dealStatus) }}</span>
                  </div>
                </div>
                <!-- 如果只有一个存在，则单独展示 -->
                <span
                  v-else-if="item?.value"
                  style="display: inline-block"
                  :class="[item.isAlarmLabel ? 'label-style' : '', item.warnStyle]"
                  :style="{ color: item.link ? 'blue' : '', cursor: item.link ? 'pointer' : '' }"
                  @click="item.link ? goEquipmentDetail(item.assetsCode) : null"
                >
                  {{ item.value }}
                  <el-tooltip content="重点设备" placement="top" v-if="item?.value && item.isKey">
                    <span class="pt-5">
                      <img
                        style="width: 26px; height: 26px; margin-left: 5px; vertical-align: middle"
                        src="@/assets/imgs/icons/keynote.png"
                        alt=""
                      />
                    </span>
                  </el-tooltip>

                  <el-tooltip
                    :content="item.warnTypeDescription || '无'"
                    placement="top"
                    :show-if-overflow="false"
                    v-if="item?.value && item.warnTypeDescription"
                  >
                    <img
                      v-if="item.icon"
                      src="@/assets/imgs/icons/faultTab2.png"
                      alt=""
                      style="width: 16px; height: 16px; margin-left: 5px; vertical-align: middle"
                    />
                  </el-tooltip>
                </span>

                <span v-else-if="item?.dealStatus">{{ item.dealStatus }}</span>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <div class="main-right">
        <div class="right-top">
          <div class="title-box">历史告警</div>
          <el-table
            :data="detailRightTable"
            style="width: 95%; margin-bottom: 10px; margin-left: 25px; height: 85%"
            :header-cell-style="{
              'background-color': '#F4F6F8',
              'font-size': '14px',
              color: '#3B3C3D'
            }"
          >
            <!-- 其他列 -->
            <el-table-column type="index" :index="tableIndex" label="序号" width="60" />
            <el-table-column prop="labelName" label="告警标签" width="120" show-overflow-tooltip>
              <template #default="scope">
                <span class="label-style">
                  {{ scope.row.labelName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="warnGradeName"
              label="告警等级"
              width="100"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div>
                  <button :class="UTILS.accessStatusClass(scope.row.warnGradeName)">
                    {{ scope.row.warnGradeName }}</button
                  >
                </div>
              </template>
            </el-table-column>

            <el-table-column
              prop="warnUpdateTime"
              label="最新更新时间"
              width="160"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div>{{ UTILS.timestampToDateString(scope.row.warnUpdateTime) }}</div>
              </template>
            </el-table-column>

            <el-table-column
              prop="warnCreateTime"
              label="告警生成时间"
              width="160"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div>{{ UTILS.timestampToDateString(scope.row.warnCreateTime) }}</div>
              </template>
            </el-table-column>

            <el-table-column prop="dealStatus" label="处理状态" width="100" show-overflow-tooltip>
              <template #default="scope">
                <div class="status-dot-container">
                  <span
                    class="status-dot"
                    :style="{ backgroundColor: UTILS.getStatusColor(scope.row.dealStatus) }"
                  ></span>
                  {{ UTILS.getStatusText(scope.row.dealStatus) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="dealUserName" label="处理人" width="100" show-overflow-tooltip />
          </el-table>
        </div>

        <div class="right-bottom">
          <div class="title-box">历史工单</div>
          <el-table
            :data="[]"
            style="width: 95%; margin-bottom: 10px; margin-left: 25px"
            :header-cell-style="{
              'background-color': '#F4F6F8',
              'font-size': '14px',
              color: '#3B3C3D'
            }"
          >
            <!-- 其他列 -->
            <el-table-column type="index" :index="tableIndex" label="序号" width="60" />
            <el-table-column prop="labelName" label="工单编号" width="120" show-overflow-tooltip />
            <el-table-column
              prop="warnGradeName"
              label="工单名称"
              width="100"
              show-overflow-tooltip
            />

            <el-table-column
              prop="warnUpdateTime"
              label="生成时间"
              width="160"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div>{{ UTILS.timestampToDateString(scope.row.warnUpdateTime) }}</div>
              </template>
            </el-table-column>

            <el-table-column
              prop="warnCreateTime"
              label="完成时间"
              width="160"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div>{{ UTILS.timestampToDateString(scope.row.warnCreateTime) }}</div>
              </template>
            </el-table-column>

            <el-table-column prop="dealStatus" label="工单状态" width="100" show-overflow-tooltip>
              <template #default="scope">
                <div>{{ UTILS.getStatusText(scope.row.dealStatus) }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="dealUserName" label="处理人" width="100" show-overflow-tooltip />
          </el-table>
        </div>
      </div>
    </main>
  </ContentWrap>
  <!-- 推送弹窗 -->
  <Dialog v-model="showDialog" title="推送详情" width="30%">
    <ContentWrap class="label-style">15888888888</ContentWrap>
    <ContentWrap class="label-style">
      <div>XXXXX出现故障</div>
      <div>XXXXX出现故障</div>
      <div>XXXXX出现故障</div>
    </ContentWrap>
    <ContentWrap class="label-style"><img src="@/assets/imgs/logo.png" alt="" /></ContentWrap>
  </Dialog>
  <!-- 推送弹窗 -->
</template>

<script lang="ts" setup>
import PrivateShow from '@/components/PrivateShow/index.vue'
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute, onBeforeRouteLeave } from 'vue-router'
import { getWarnTypeDescription } from '@/views/operations/list/utils/index'
import * as ListApi from '@/api/alarm/list' //引入api
import * as UTILS from './utils/index' //引入筛选的工具函数

const router = useRouter()
const route = useRoute()
const paramsLeft = reactive({ id: null })
const paramsRightTop = reactive({ assetsCode: null })
onMounted(async () => {
  const alarmListObj = sessionStorage.getItem('alarmListObj')
  const newAlarmListObj = JSON.parse(alarmListObj)
  // console.log('传递过来的数据', newAlarmListObj)
  if (alarmListObj) {
    paramsLeft.id = newAlarmListObj.id
    await getDetailLeftFun()
    paramsRightTop.assetsCode = newAlarmListObj.assetsCode
    // paramsRightTop.assetsCode = newAlarmListObj.assetsCode
    await getDetailRightTopFun()
  } else {
    ElMessage.warning('已离开页面,请重新选择项目')
  }
})

// 使用 beforeRouteLeave 导航守卫
onBeforeRouteLeave((to, from) => {
  // 清除 sessionStorage 中的 equipmentDetailId
  // sessionStorage.removeItem('equipmentDetailId')
})

// 左侧默认数据
const descriptionsItems = ref([])
const getDetailLeftFun = async () => {
  const data = await ListApi.getDetail(paramsLeft)
  // console.log('详情数据', data)
  // 重新赋值方便循环渲染
  descriptionsItems.value = [
    {
      label: '设备编号:',
      value: data?.assetsCode,
      link: true,
      isKey: data?.isKey,
      assetsCode: data?.assetsCode,
      dealStatus: data?.dealStatus
    }, //是否为可跳转的链接和是否重点
    { label: '设备名称:', value: data?.assetsName },
    { label: '所属项目:', value: data?.projectName },
    { label: '资产类型:', value: data?.assetsTypeName },
    {
      label: '告警等级:',
      value: data?.warnGradeName,
      warnStyle: UTILS.accessStatusClass(data?.warnGradeName)
    },
    {
      label: '告警方式:',
      value: data?.warnName,
      icon: 'faultTab2.png',
      warnTypeDescription: await getWarnTypeDescription(data?.warnId)
    },
    { label: '告警标签:', value: data?.labelName, isAlarmLabel: true },
    { label: '告警详情:', value: data?.warnDetail },
    { label: '所属区域:', value: data?.area },
    { label: '运营商', value: data?.operatingUnitName },
    { label: '维保单位:', value: data?.maintenanceUnitName },
    { label: '最新更新时间:', value: UTILS.timestampToDateString(data?.warnUpdateTime) },
    { label: '告警生成时间:', value: UTILS.timestampToDateString(data?.warnCreateTime) },
    { label: '告警来源:', value: data?.sourceName }
  ]
}

// 右上角默认数据
const detailRightTable = ref([])
//序号自增
const form = reactive({
  pageNo: 1,
  pageSize: 10
})
const tableIndex = (index: number) => {
  return (form.pageNo - 1) * form.pageSize + index + 1
}
const getDetailRightTopFun = async () => {
  const data = await ListApi.getWarnList(paramsRightTop)
  detailRightTable.value = data.list
  // console.log('右上角数据', detailRightTable.value)
}

// 返回上一页
const goBack = () => {
  window.history.back()
}

// 跳转设备详情 传递assetsCode
const goEquipmentDetail = (assetsCode) => {
  // console.log('打印设备编号', assetsCode)
  sessionStorage.removeItem('equipmentDetailId') //传id清除code 传code清除id
  sessionStorage.setItem('equipmentDetailCode', assetsCode.toString())
  router.push({
    path: '/operations/equipmentDetail'
    // query: { id: id.toString() } // 将 id 值传递到详情页
  })
}
// 推送弹窗
const showPushBtn = ref(true) //是否展示推送按钮  默认不展示
const pushDetailFun = () => {
  ElMessage.warning('暂未开发')
  // showDialog.value = true
}
const showDialog = ref(false)
</script>

<style lang="scss" scoped>
.my-component {
  background-color: #f6f7f9;
  header {
    width: 100%;
    height: 50px;
    background-color: white;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  main {
    width: 100%;
    height: 650px;
    display: flex;
    justify-content: space-between;
    // background-color: width;
    .main-left {
      width: 49%;
      height: 100%;
      // margin-right: 10px;
      // margin-left: 20px;
      background-color: white;
      .title-content-box {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title-box {
          width: 120px;
          height: 40px;
          display: flex;
          align-items: center;
          position: relative;
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 16px;
          color: #3b3c3d;
          text-align: left;
          font-style: normal;
          text-transform: none;
          line-height: 24px;
          margin: 3px 0;
          padding-left: 28px;

          &::before {
            margin: 10px 0 0 10px;
            content: '';
            position: absolute;
            left: 0;
            top: 6px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #005fff;
            border: 2px solid #d4e4ff;
          }
        }
        .btn-box {
          padding-right: 20px;
        }
      }

      .detail-box {
        width: 97%;
        height: 90%;
        margin: 0 12px;
        display: flex;
        .descriptions-style {
          width: 100%;
          height: 100%;
        }
      }
    }
    .main-right {
      width: 49%;
      height: 100%;
      margin-left: auto; /* 确保靠右 */
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;

      .right-top {
        width: 100%;
        height: 52%;
        background-color: white;
        .title-box {
          width: 100%;
          height: 40px;
          display: flex;
          align-items: center;
          position: relative;
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 16px;
          color: #3b3c3d;
          text-align: left;
          font-style: normal;
          text-transform: none;
          line-height: 24px;
          margin: 3px 0;
          padding-left: 28px;

          &::before {
            margin: 10px 0 0 10px;
            content: '';
            position: absolute;
            left: 0;
            top: 6px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #005fff;
            border: 2px solid #d4e4ff;
          }
        }
      }

      .right-bottom {
        width: 100%;
        height: 45%;
        background-color: white;
        overflow-y: auto;
        &::-webkit-scrollbar {
          width: 6px; // 滚动条宽度
          height: 6px; // 滚动条高度
        }
        &::-webkit-scrollbar-thumb {
          background-color: #c1c1c1c1; // 滚动条颜色
          border-radius: 3px; // 滚动条圆角
        }

        &::-webkit-scrollbar-track {
          background-color: #f1f1f1f1; // 滚动条轨道颜色
          border-radius: 3px; // 滚动条轨道圆角
        }
        .title-box {
          width: 100%;
          height: 40px;
          display: flex;
          align-items: center;
          position: relative;
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 16px;
          color: #3b3c3d;
          text-align: left;
          font-style: normal;
          text-transform: none;
          line-height: 24px;
          margin: 3px 0;
          padding-left: 28px;

          &::before {
            margin: 10px 0 0 10px;
            content: '';
            position: absolute;
            left: 0;
            top: 6px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #005fff;
            border: 2px solid #d4e4ff;
          }
        }
      }
    }
  }
}
// 状态按钮样式
.low-class {
  min-width: 60px;
  background-color: #ffd910; /* 蓝色背景 */
  color: #000; /* 文字 */
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.middle-class {
  min-width: 60px;
  background-color: #ff9900; /* 蓝色背景 */
  color: white; /* 白色文字 */
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.top-class {
  min-width: 60px;
  background-color: #ff5e0b; /* 蓝色背景 */
  color: white; /* 文字 */
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.emergency-class {
  min-width: 60px;
  background-color: #f5002a; /* 蓝色背景 */
  color: white; /* 文字 */
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
// 圆点样式
.status-dot-container {
  display: flex;
  align-items: center;
  gap: 6px; // 圆点和文字之间的间距
}
.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #999; // 默认颜色，根据状态动态变化
}
// 左侧label背景颜色
:deep(.my-label) {
  background: #f7f8f8 !important;
}
// 告警标签
.label-style {
  padding: 3px 8px;
  border-radius: 5px;
  background-color: #f1f4f9;
}
// 设备编号左右布局
.value-status-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
</style>
