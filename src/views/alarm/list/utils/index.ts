// 时间戳转换
export function timestampToDateString(timestamp: number): string {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
}
// 时间戳转换2
export function timestampToDateString2(timestamp: number): string {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从 0 开始
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day} 00:00:00`
}
// 表格状态文字
export function getStatusText(status: number): string {
  switch (status) {
    case 0:
      return '待处理'
    case 1:
      return '处理中'
    case 2:
      return '已处理'
    case 3:
      return '已关闭'
    default:
      return ''
  }
}
// 按钮样式
export function accessStatusClass(value) {
  switch (value) {
    case '低':
      return 'low-class'
    case '中':
      return 'middle-class'
    case '高':
      return 'top-class'
    case '紧急':
      return 'emergency-class'
    default:
      return ''
  }
}
// 圆点样式
export const getStatusColor = (status) => {
  switch (status) {
    case 0:
      return '#005FFF'
    case 1:
      return '#1CB1FF'
    case 2:
      return '#21C16B'
    case 3:
      return '#A4B5D2'
    default:
      return ''
  }
}
