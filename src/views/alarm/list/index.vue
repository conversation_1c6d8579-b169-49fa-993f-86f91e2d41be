<template>
  <div class="mr-8">
    <ContentWrap class="my-component">
      <div class="top-title">告警列表</div>

      <div class="top-equipment">
        <div class="top-equipment-main" style="">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/alarmAll.png" alt="" />
            </div>
            <div class="right-txt">
              <div>全部</div>
              <div>{{ WarningCount.allCount }}</div>
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/alarmAllGrey.png" alt="" />
          </div>
        </div>
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/alarmWait.png" alt="" />
            </div>
            <div class="right-txt">
              <div>待处理</div>
              <div>{{ WarningCount.noDealCount }}</div>
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/alarmWaitGrey.png" alt="" />
          </div>
        </div>
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/alarmProcessing.png" alt="" />
            </div>
            <div class="right-txt">
              <div>处理中</div>
              <div>{{ WarningCount.dealingCount }}</div>
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/alarmProcessingGrey.png" alt="" />
          </div>
        </div>
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/label.png" alt="" />
            </div>
            <div class="right-txt">
              <div>已处理</div>
              <div>{{ WarningCount.dealCount }}</div>
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/label2.png" alt="" />
          </div>
        </div>
        <div class="top-equipment-main">
          <!-- 左侧图文盒子 -->
          <div class="top-equipment-box">
            <div class="left-img">
              <img src="@/assets/imgs/icons/fault.png" alt="" />
            </div>
            <div class="right-txt">
              <div>今日新增</div>
              <div>{{ WarningCount.todayCount }}</div>
            </div>
          </div>
          <!-- 右侧灰图盒子 -->
          <div class="grey-img">
            <img src="@/assets/imgs/icons/fault2.png" alt="" />
          </div>
        </div>
      </div>

      <div class="main">
        <!-- 右侧盒子 -->
        <div class="right-main">
          <!-- 表单部分 -->
          <ContentWrap class="form-box">
            <el-form
              ref="queryFormRef"
              :inline="true"
              :model="formSearchPage"
              class="form-search flex flex-wrap items-start -mb-15px"
            >
              <el-form-item label="" class="!mr-3">
                <el-input
                  v-model="formSearchPage.assets"
                  class="!w-240px"
                  clearable
                  placeholder="输入设备编号/名称"
                />
              </el-form-item>
              <el-form-item label="告警标签" class="!mr-3">
                <el-select
                  v-model="formSearchPage.warnLabelId"
                  class="!w-140px"
                  clearable
                  placeholder="选择告警标签"
                >
                  <el-option
                    v-for="item in warnLabelList"
                    :key="item.id"
                    :label="item.labelName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="告警来源" class="!mr-3">
                <el-select
                  v-model="formSearchPage.assetsSource"
                  class="!w-140px"
                  clearable
                  placeholder="选择告警来源"
                >
                  <el-option
                    v-for="item in warnSourceList"
                    :key="item.id"
                    :label="item.sourceName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="重点设备" class="!mr-3">
                <el-select
                  v-model="formSearchPage.isKey"
                  class="!w-140px"
                  clearable
                  placeholder="是否重点"
                >
                  <el-option value="1" key="1" label="重点" />
                  <el-option value="0" key="0" label="非重点" />
                </el-select>
              </el-form-item>
              <el-form-item label="区域" prop="areaId" class="!mr-3">
                <Dictionary
                  v-model="formSearchPage.areaId"
                  type="cascader"
                  width="180px"
                  dict-type="oamArea"
                  :cascader-props="{
                    multiple: true,
                    checkStrictly: true,
                    label: 'name',
                    value: 'id'
                  }"
                  :max-collapse-tags="2"
                  placeholder="请选择区域"
                />
              </el-form-item>
              <el-form-item label="处理状态" class="!mr-3">
                <el-select
                  v-model="formSearchPage.dealStatus"
                  class="!w-140px"
                  clearable
                  placeholder="请选择处理状态"
                >
                  <el-option :value="0" label="待处理" />
                  <el-option :value="1" label="处理中" />
                  <el-option :value="2" label="已处理" />
                  <el-option :value="3" label="已关闭" />
                </el-select>
              </el-form-item>

              <div class="min-w-[850px] flex-1 flex justify-between mb-[18px] pl-2">
                <div>
                  <XButton
                    v-hasPermi="['infra:warn-assets:query']"
                    title="查询"
                    preIcon="ep:search"
                    gradient
                    @click="onSubmit"
                  />
                  <el-button @click="resetForm"><Icon icon="ep:refresh" />重置</el-button>
                  <XButton
                    class="text-button"
                    preIcon="ep:search"
                    title="高级搜索"
                    width="100px"
                    gradient
                    @click="openSearchDialog"
                    v-hasPermi="['infra:warn-assets:query']"
                  />
                </div>
                <div>
                  <el-button @click="addWhiteFun" v-hasPermi="['infra:warn-white-list:create']">
                    <Icon icon="ep:document" />添加白名单
                  </el-button>
                  <el-button @click="shutDownFun" v-hasPermi="['infra:warn-assets:update']">
                    <el-icon class="mr-0.5"><CircleClose /></el-icon>关闭
                  </el-button>
                  <el-button
                    plain
                    @click="handleExport"
                    :loading="exportLoading"
                    v-hasPermi="['infra:warn-assets:export']"
                  >
                    <Icon icon="ep:download" />导出
                  </el-button>
                </div>
              </div>
            </el-form>
          </ContentWrap>
          <!-- 表格部分 -->
          <div class="right-table">
            <el-table
              :data="tableList"
              height="530"
              v-loading="loading"
              style="width: 100%; margin-bottom: 10px"
              @selection-change="handleSelectionChange"
              @sort-change="handleSortChange"
              :header-cell-style="{
                'background-color': '#F4F6F8',
                'font-size': '14px',
                color: '#3B3C3D'
              }"
            >
              <!-- 多选框列 -->
              <el-table-column type="selection" :selectable="selectable" min-width="55" />
              <!-- 其他列 -->
              <el-table-column type="index" :index="tableIndex" label="序号" min-width="60" />
              <el-table-column
                prop="assetsCode"
                label="设备编号"
                min-width="150"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <div
                    @click="goEquipmentDetail(scope.row.assetsCode)"
                    style="display: flex; align-items: center; color: blue; cursor: pointer"
                  >
                    <el-tooltip content="重点设备" placement="top" v-if="scope?.row?.isKey === 1">
                      <span class="pt-1">
                        <img class="w5 h5" src="@/assets/imgs/icons/keynote.png" alt="" />
                      </span>
                    </el-tooltip>

                    {{ scope.row.assetsCode }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="assetsName"
                label="设备名称"
                min-width="150"
                show-overflow-tooltip
              />
              <el-table-column
                prop="projectName"
                label="所属项目"
                min-width="100"
                show-overflow-tooltip
              />
              <el-table-column
                prop="assetsTypeName"
                label="设备类型"
                min-width="100"
                show-overflow-tooltip
              />
              <el-table-column
                prop="warnGradeName"
                label="告警等级"
                min-width="100"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <div>
                    <button :class="UTILS.accessStatusClass(scope.row.warnGradeName)">
                      {{ scope.row.warnGradeName }}</button
                    >
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="warnName"
                label="告警方式"
                min-width="90"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <el-tooltip
                    :content="scope.row.warnTypeDescription"
                    placement="top"
                    v-if="scope?.row?.warnName"
                  >
                    <span @mouseenter="loadWarnTypeDescription(scope.row)">
                      {{ scope.row.warnName }}
                      <img src="@/assets/imgs/icons/faultTab2.png" alt="" />
                    </span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column
                prop="labelName"
                label="告警标签"
                min-width="120"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <span class="label-style">
                    {{ scope.row.labelName }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="area" label="所属区域" min-width="120" show-overflow-tooltip />
              <el-table-column
                prop="operatingUnitName"
                label="运营商"
                min-width="120"
                show-overflow-tooltip
              />
              <el-table-column
                prop="maintenanceUnitName"
                label="维保单位"
                min-width="120"
                show-overflow-tooltip
              />
              <el-table-column
                prop="warnUpdateTime"
                label="最新更新时间"
                min-width="160"
                show-overflow-tooltip
                sortable="custom"
              >
                <template #default="scope">
                  <div>{{ UTILS.timestampToDateString(scope.row.warnUpdateTime) }}</div>
                </template>
              </el-table-column>

              <el-table-column
                prop="warnCreateTime"
                label="告警生成时间"
                min-width="160"
                show-overflow-tooltip
                sortable="custom"
              >
                <template #default="scope">
                  <div>{{ UTILS.timestampToDateString(scope.row.warnCreateTime) }}</div>
                </template>
              </el-table-column>
              <el-table-column
                prop="sourceName"
                label="告警来源"
                min-width="150"
                show-overflow-tooltip
              />
              <el-table-column
                prop="dealStatus"
                label="处理状态"
                min-width="90"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <div class="status-dot-container">
                    <span
                      class="status-dot"
                      :style="{ backgroundColor: UTILS.getStatusColor(scope.row.dealStatus) }"
                    ></span>
                    {{ UTILS.getStatusText(scope.row.dealStatus) }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="date" label="操作" min-width="180" fixed="right">
                <template #default="scope">
                  <el-button link type="primary" size="small" @click="videoFun(scope.row.id)"
                    >视频复核</el-button
                  >
                  <el-button
                    v-if="scope.row.dealStatus === 0"
                    link
                    type="primary"
                    size="small"
                    @click="dispatchFun(scope.row.id)"
                    >派单</el-button
                  >
                  <el-button link type="primary" size="small" @click="handleClick(scope.row)"
                    >详情</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页部分 -->
            <div class="demo-pagination-block">
              <el-pagination
                v-model:current-page="formSearchPage.pageNo"
                v-model:page-size="formSearchPage.pageSize"
                :page-sizes="[10, 20, 30, 50, 100]"
                :size="size"
                :disabled="disabled"
                :background="background"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 高级搜索组件 -->
      <AdvancedSearch
        v-model:formSearchPage="formSearchPage"
        ref="advancedSearchRef"
        @success="handleSearchSuccess"
      />
      <!-- 白名单弹窗组件 -->
      <AddWhiteList
        ref="whiteListDialog"
        :whiteListSaveReqListVOList="whiteListSaveReqListVOList"
      />
      <!-- 视频复核子组件 -->
      <VideoReview ref="videoReviewRef" :id="123" />
    </ContentWrap>
  </div>
</template>

<script lang="ts" setup>
import { CircleClose } from '@element-plus/icons-vue'
import * as ListApi from '@/api/alarm/list' //引入api
import { hasPermission } from '@/directives/permission/hasPermi' //引入权限判断
import * as WorkApi from '@/api/work'
import { ref, reactive, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import { getWarnTypeDescription } from '@/views/operations/list/utils/index'
import * as TicketsPushApi from '@/api/operations/ticketsPush'
import { getDictList } from '@/api/infra/deviceDict' //设备来源
import * as DynamicFormApi from '@/api/DynamicForm'
import { useRouter, useRoute } from 'vue-router'
import download from '@/utils/download'
import { ElMessage } from 'element-plus'
import * as UTILS from './utils/index' //引入筛选的工具函数
import * as EquipmentApi from '@/api/operations/equipment'
import AdvancedSearch from './components/AdvancedSearch.vue' //引入高级搜索组件
import AddWhiteList from './components/AddWhiteList.vue' //引入白名单弹窗组件
import VideoReview from './components/VideoReview.vue' // 引入视频播放组件

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()
const route = useRoute()

//序号自增
const tableIndex = (index: number) => {
  return (formSearchPage.pageNo - 1) * formSearchPage.pageSize + index + 1
}
const getOperatingUnit = async (type: string) => {
  const unitTypeData = await DynamicFormApi.getUnitType({ code: type })
  const unitData = await DynamicFormApi.getUnit({ typeId: unitTypeData })
  return unitData
}

// 告警数量  顶部告警数量展示
const WarningCount = ref({
  allCount: 0,
  dealCount: 0,
  dealingCount: 0,
  noDealCount: 0,
  sourceCount: {}, //将key和val分别取出来
  todayCount: 0
})
// const getWarningCountFun = async () => {
//   const data = await WorkApi.getWarningCount()
//   WarningCount.value = data
//   // console.log('父组件打印告警数量', data)
// }
// noDealCount
onMounted(() => {
  getWarnLabelFun() //获得下拉告警标签
  getWarnSourceFun() //告警来源
  getType()
  getProject() //获得所属项目
  getOperatingUnitFun() //获得运营商
  getMaintenanceUnitFun() //获得维保单位
  getAssetsSourceList() //获得设备来源
  getWarnIdList() //获得告警方式
  window.addEventListener('keydown', handleGlobalKeyDown)
  // 告警数量
  // if (hasPermission(['infra:warn-assets:query'])) {
  //   getWarningCountFun()
  // }
  // 处理状态
  const dealStatus = route.query.dealStatus ? route.query.dealStatus : ''
  const warnUpdateTime = route.query.warnUpdateTime ? route.query.warnUpdateTime : ''
  // console.log('打印处理状态1', dealStatus)
  if (dealStatus !== undefined && dealStatus !== '') {
    // 如果数据存在则进行赋值查询
    // console.log('打印处理状态3', formSearchPage.dealStatus)
    formSearchPage.dealStatus = Number(dealStatus)
    // 调用查询方法
    onSubmit()
  } else if (warnUpdateTime !== undefined && warnUpdateTime !== '') {
    formSearchPage.warnUpdateTime[0] = UTILS.timestampToDateString2(new Date())
    // formSearchPage.warnUpdateTime[1] = UTILS.timestampToDateString(new Date())
    // console.log('当日新增')
    // 调用查询方法
    onSubmit()
  } else {
    // console.log('打印处理状态2', formSearchPage.dealStatus)
    getWarnListFun() //调用分页接口 第二个接口
  }
})
onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleGlobalKeyDown)
})
const handleGlobalKeyDown = (event) => {
  if (event.key === 'Enter') {
    onSubmit()
  }
}

// 高级搜索项目列表树状下拉

const typeList = ref([])
const getType = async () => {
  const data = await TicketsPushApi.getType()
  // console.log('设备类型typeList', data)
  typeList.value = data
}
const projectList = ref([]) //所属项目
const getProject = async () => {
  const data = await DynamicFormApi.getBelongsProject()
  // console.log('所属项目', data)
  projectList.value = data
}

const operatingUnitList = ref([]) //运营商/运营单位
const getOperatingUnitFun = async () => {
  const data = await getOperatingUnit('operation')
  // console.log('运营商/运维单位', data)
  operatingUnitList.value = data
}
const maintenanceUnitList = ref([]) //维保单位
const getMaintenanceUnitFun = async () => {
  const data = await getOperatingUnit('maintenance')
  // console.log('维保单位', data)
  maintenanceUnitList.value = data
}
const assetsSourceList = ref([]) //设备来源
const getAssetsSourceList = async () => {
  const data = await getDictList('deviceSource')
  // console.log('设备来源', data)
  assetsSourceList.value = data
}
const warnIdList = ref([]) // 告警方式
const getWarnIdList = async () => {
  const data = await DynamicFormApi.getWarning()
  // console.log('告警方式', data)
  warnIdList.value = data
}
// 搜索表单的告警标签
const warnLabelList = ref([])
const getWarnLabelFun = async () => {
  const data = await ListApi.getWarnLabel()
  warnLabelList.value = data
  // console.log('打印告警标签', warnLabelList.value)
  // formSearchPage.warnLabelId = data.map((item) => item.id)
}
// 搜索表单的告警来源
const warnSourceList = ref([])
const getWarnSourceFun = async () => {
  const data = await ListApi.getWarnSource()
  warnSourceList.value = data
  // console.log('打印告警来源', warnSourceList.value)
}

// const getNoPage = async () => {
//   const data = await ListApi.postListNoPage(form)
//   // console.log('打印无分页数据', data)
//   if (data.length) {
//     // 存在数据调用下一个接口
//     formSearchPage.assetsCodes = data.map((item) => item.assetsCode)
//     // console.log('打印拼接后设备编号', formSearchPage.assetsCode)
//     getWarnListFun()
//   } else {
//     tableList.value = [] //查询不到手动置空
//     total.value = 0 //查询不到手动置空
//     //无数据清空展示并打断执行
//     return
//   }
// }

// 缓存已请求过的 warnType
const warnTypeCache = new Map()
const loadWarnTypeDescription = async (row) => {
  if (!row.warnTypeDescription && row.warnId) {
    if (warnTypeCache.has(row.warnId)) {
      row.warnTypeDescription = warnTypeCache.get(row.warnId)
    } else {
      try {
        const description = await getWarnTypeDescription(row.warnId)
        row.warnTypeDescription = description
        warnTypeCache.set(row.warnId, description)
      } catch (error) {
        // console.error('获取告警方式描述失败:', error)
        row.warnTypeDescription = '获取失败'
      }
    }
  }
}

const tableList = ref([])
// 获取表格数据分页接口
const getWarnListFun = async () => {
  loading.value = true
  try {
    const data = await ListApi.getWarnList(formSearchPage)
    // console.log('打印表格数据', data)
    // 提前解析告警方式描述
    // await Promise.all(
    //   data.list.map(async (item) => {
    //     item.warnTypeDescription = await getWarnTypeDescription(item?.warnId)
    //   })
    // )
    // 初始化时不请求描述，只赋空值
    data.list.forEach((item) => {
      item.warnTypeDescription = null
    })
    tableList.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
  // 告警接口 获取告警详细信息
  try {
    const deviceObj = await EquipmentApi.getWarn(formSearchPage)
    WarningCount.value = deviceObj
    // console.log('WarningCount.value', WarningCount.value)
  } finally {
    loading.value = false
  }
}
// 搜索表单，用于获取不分页接口
// const form = reactive({
//  })

// 获取分页数据 第二个接口参数
const formSearchPage = reactive({
  // 拼接后的新参数↓
  pageNo: 1,
  pageSize: 10,
  assets: null, //设备编号/名称
  assetsSource: null, //告警来源
  assetsTypeId: [], //设备类型 重点设备
  isKey: null, //是否重点
  areaId: [], //所属区域
  // 下面是高级搜索部分的参数
  assetsCode: null, //设备编号
  assetsName: null, //设备名称
  warnWarnId: null, //告警方式 告警等级
  projectId: [], //所属项目
  operatingUnit: null, //运营商
  maintenanceUnit: null, //推送单位/维保单位

  // 拼接前原本的↓
  // pageNo: 1,
  // pageSize: 10,
  warnLabelId: [], //告警标签id
  dealStatus: null, //处理状态下拉
  warnUpdateTime: [], //告警最新更新时间
  warnCreateTime: [], //告警生成时间
  // assetsCode: [], //设备编码
  ids2: [], //多选框选中的id 批量导出使用
  orderParam: null, //排序字段
  asc: null //是否升序boolean
})

// 分页相关
const loading = ref(true)
const total = ref(0)
const size = ref('default')
const background = ref(true)
const disabled = ref(false)

// 多选框相关
const selectedRows = ref([]) // 选中的行数据

// 处理选中行变化
const handleSelectionChange = (rows) => {
  selectedRows.value = rows
  // 有选中的进行赋值用于导出使用
  if (selectedRows.value.length) {
    formSearchPage.ids2 = rows.map((item) => item.id)
  } else {
    formSearchPage.ids2 = [] //没有选中的置空
  }
  // console.log('选中的行数据：', selectedRows.value)
}

// 控制哪些行可以被选择
const selectable = (row, index) => {
  // 例如：禁用第一行的选择
  return index !== -1
}

// 处理排序变化
const sortParams = ref({
  prop: null,
  order: null
})

const handleSortChange = (column) => {
  const { prop, order } = column
  sortParams.value = { prop, order }

  // 只对这两个时间字段做排序处理
  if (prop === 'warnUpdateTime' || prop === 'warnCreateTime') {
    // 设置排序字段和是否升序（order 为 'ascending' 表示升序）
    formSearchPage.orderParam = prop
    formSearchPage.asc = order === 'ascending'
    // 调用搜索函数刷新数据
    // getWarnListFun()
    onSubmit()
  }
}

// 其他事件
const handleSizeChange = (val: number) => {
  formSearchPage.pageSize = val
  onSubmit() //重新获取
}
const handleCurrentChange = (val: number) => {
  formSearchPage.pageNo = val
  onSubmit() //重新获取
}
// 跳转设备详情 传递assetsCode
const goEquipmentDetail = (assetsCode) => {
  // console.log('打印设备编号', assetsCode)
  sessionStorage.removeItem('equipmentDetailId') //传id清除code 传code清除id
  sessionStorage.setItem('equipmentDetailCode', assetsCode.toString())
  router.push({
    path: '/operations/equipmentDetail'
    // query: { id: id.toString() } // 将 id 值传递到详情页
  })
}
// 跳转详情
const handleClick = (obj) => {
  // console.log('打印当前行数据', obj)
  sessionStorage.setItem('alarmListObj', JSON.stringify(obj))
  router.push({
    path: '/alarm/alarmListDetail'
    // query: { id: id.toString() } // 将 id 值传递到详情页
  })
}

const onSubmit = () => {
  formSearchPage.ids2 = []
  getWarnListFun()
}

const resetForm = async () => {
  // 清理第一个表单数据
  formSearchPage.assets = null
  formSearchPage.assetsCode = null // 设备编号
  formSearchPage.assetsTypeId = [] // 设备类型
  formSearchPage.isKey = null //是否重点
  formSearchPage.areaId = [] // 所属区域
  formSearchPage.pageNo = 1 // 分页页码
  formSearchPage.pageSize = 10 // 分页大小
  formSearchPage.assetsName = null // 设备名称
  formSearchPage.operatingUnit = null // 运营商
  formSearchPage.maintenanceUnit = null // 推送单位/维保单位
  formSearchPage.assetsSource = null // 设备来源
  formSearchPage.warnWarnId = null // 告警方式
  formSearchPage.projectId = [] //所属项目

  // 清理第二个表单数据
  formSearchPage.pageNo = 1
  formSearchPage.pageSize = 10
  formSearchPage.warnLabelId = []
  formSearchPage.dealStatus = null // 处理状态
  formSearchPage.warnUpdateTime = []
  formSearchPage.warnCreateTime = []
  // formSearchPage.assetsCode = [] //必须要重置掉设备编号不然就重置不掉高级搜索的设备编号
  formSearchPage.ids2 = [] //多选框选中的数据
  formSearchPage.orderParam = null //排序字段
  formSearchPage.asc = null //是否升序boolean
  // 确保子组件已经挂载完成
  await nextTick()
  // 调用子组件的重置方法
  advancedSearchRef.value.resetForm()

  getWarnListFun() //调用分页接口 第二个接口
}

// 点击视频播放
// 视频复核功能
const selectedId = ref<number | null>(null)
const videoReviewRef = ref<InstanceType<typeof VideoReview> | null>(null)
const videoFun = async (id: number) => {
  selectedId.value = id
  if (videoReviewRef.value) {
    videoReviewRef.value.open(id)
  }
  ElMessage.warning('暂未开发')
}
// 点击派单
const dispatchFun = async (id: number) => {
  ElMessage.warning('暂未开发')
}

const whiteListDialog = ref()
// 添加白名单函数
const whiteListSaveReqListVOList = ref([])
const addWhiteFun = async () => {
  if (selectedRows.value.length) {
    // console.log('打印选中的行数据', selectedRows.value)
    whiteListDialog.value.open()
    // 提取 assetsCode 和 sourceId 组成新数组
    whiteListSaveReqListVOList.value = selectedRows.value.map((row) => ({
      assetsCode: row.assetsCode,
      sourceId: row.sourceId
    }))
  } else {
    ElMessage.warning('请选中要添加白名单的数据')
    return
  }
}

// 关闭函数
const shutDownFun = async () => {
  if (selectedRows.value.length) {
    console.log('打印选中的行数据', selectedRows.value) //dealStatus状态2或3打断执行
    // 检查是否含有 dealStatus 为 2 或 3 的项
    const hasInvalidStatus = selectedRows.value.some(
      (row) => row.dealStatus === 2 || row.dealStatus === 3
    )
    if (hasInvalidStatus) {
      ElMessage.warning('包含已处理或已关闭的数据，无法执行此操作')
      return
    } else {
      // const ids2 = selectedRows.value.map((row) => row.id)
      // 获取 id 数组并转为逗号分隔的字符串
      const ids2 = selectedRows.value.map((row) => row.id).join(',')
      // console.log('打印ids2', ids2)
      try {
        // await ListApi.deleteWarn(ids2)
        await ListApi.deleteWarn({ ids2 })
        ElMessage.success('关闭成功')
        onSubmit() //重新获取
      } catch (error) {
        ElMessage.error('关闭失败')
      }
    }
  } else {
    ElMessage.warning('请选中要关闭的数据')
    return
  }
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 发起导出
    exportLoading.value = true
    // const data = await ListApi.postListNoPage(form) //调用第一个不分页接口

    // 存在勾选的数据调用下一个接口 无需判断无勾选导出所有
    // if (selectedRows.value.length) {
    // 导出的二次确认
    await message.exportConfirm()
    const newData = await ListApi.exportWarnList(formSearchPage) //调用导出接口
    download.excel(newData, '告警列表.xls')
    // getWarnListFun()
    // } else {
    //   ElMessage.warning('请选择要导出的数据')
    //   //无数据清空展示并打断执行
    //   return
    // }
  } catch {
  } finally {
    exportLoading.value = false
  }
}

// 高级搜索组件的引用
const advancedSearchRef = ref(null)
// 打开高级搜索弹窗
const openSearchDialog = () => {
  advancedSearchRef.value.open() // 调用组件中定义的 open 方法打开弹窗
}

// 处理搜索成功后的回调
const handleSearchSuccess = (params) => {
  // console.log('搜索参数:', params)
  // 将子组件传递过来的数据赋值给父组件的 form 和 formSearchPage
  // Object.assign(form, params.form)
  Object.assign(formSearchPage, params.formSearchPage)
  formSearchPage.ids2 = []
  onSubmit()
}
</script>

<style lang="scss" scoped>
.my-component {
  // width: 100% !important;
  // height: 100% !important;
  background-color: white;
  display: flex;
  flex-direction: column;
  .top-title {
    width: 100%;
    height: 60px;
    font-size: 18px;
    font-weight: bold;
    padding-left: 20px;
    display: flex;
    align-items: center;
  }

  .top-equipment {
    // 隐藏滚动条
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Edge */
    }
    width: 100%;
    height: 70px;
    // overflow-x: auto;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    gap: 10px; // 设置固定间距，例如 10px
    // overflow-x: auto;
    // padding: 0 20px;
    .top-equipment-main {
      // width: 200px;
      // height: 70px;
      flex: 1;
      background-color: #f7f8f8;
      display: flex;
      justify-content: space-between;
      .top-equipment-box {
        width: 130px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        .left-img {
          width: 40px;
          height: 40px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .right-txt {
          width: 50%;
          height: 100%;
          font-size: 13px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          :nth-child(1) {
            text-align: center;
          }
          :nth-child(2) {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
          }
        }
      }
      .grey-img {
        width: 64px;
        height: 64px;
        img {
          width: 64px;
          height: 64px;
        }
      }
    }
    .top-equipment-main:nth-child(1):hover {
      background-color: #f97f00 !important; // 深红色背景
      color: white !important; // 文字变白
      transition: all 0.3s ease;
    }
    .top-equipment-main:nth-child(2):hover {
      background-color: #1f69e8 !important; // 深红色背景
      color: white !important; // 文字变白
      transition: all 0.3s ease;
    }
    .top-equipment-main:nth-child(3):hover {
      background-color: #009ff2 !important; // 深红色背景
      color: white !important; // 文字变白
      transition: all 0.3s ease;
    }
    .top-equipment-main:nth-child(4):hover {
      background-color: #21c16b !important; // 深红色背景
      color: white !important; // 文字变白
      transition: all 0.3s ease;
    }
    .top-equipment-main:nth-child(5):hover {
      background-color: #ea3250 !important; // 深红色背景
      color: white !important; // 文字变白
      transition: all 0.3s ease;
    }
  }
  .main {
    flex: 1;
    width: 100%;
    display: flex;
    // padding-left: 20px;

    .right-main {
      flex: 1;
      width: 100%;
      display: flex;
      flex-direction: column;
      // overflow-x: auto;
      min-width: 0; /* 允许内容被裁剪而不是挤压其他元素 */
      // 隐藏滚动条
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
      }
      .form-box {
        width: 100%;
        // background-color: palegreen;
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 10px;
        overflow-x: auto;
        .form-search {
          width: 100%;
          // background-color: pink;
          // flex: 1;
          // display: flex;
          // flex-direction: row;
          // align-items: center;
          .el-form-item {
            margin-right: 10px;
          }
        }
      }

      .right-table {
        // width: 100%;
        // flex: 1;
        padding-right: 1px; //去除黑线 操作栏没有贴满右边 导致展示部分文字形成黑线
        // max-height: 540px; //设置最大高度溢出滚动
        // overflow-y: auto;
        // 隐藏滚动条
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE 10+ */
        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Edge */
        }
        :deep(.el-table__header th) {
          text-align: left;
        }
        :deep(.el-table td) {
          text-align: left;
        }
        // .demo-pagination-block {
        //   margin-top: 10px;
        // }
        .demo-pagination-block {
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }
}
// 查询按钮样式
.text-button {
  color: #fff;
}
// 高级搜索底部按钮
.bottom-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  button {
    width: 100px;
    margin-right: 20px;
  }
}
// 状态文字颜色
.status-normal {
  color: green;
}

.status-reported {
  color: orange;
}

.status-fault {
  color: red;
}

// 状态按钮样式
.low-class {
  min-width: 60px;
  background-color: #ffd910; /* 蓝色背景 */
  color: #000; /* 文字 */
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.middle-class {
  min-width: 60px;
  background-color: #ff9900; /* 蓝色背景 */
  color: white; /* 白色文字 */
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.top-class {
  min-width: 60px;
  background-color: #ff5e0b; /* 蓝色背景 */
  color: white; /* 文字 */
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.emergency-class {
  min-width: 60px;
  background-color: #f5002a; /* 蓝色背景 */
  color: white; /* 文字 */
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// 圆点样式
.status-dot-container {
  display: flex;
  align-items: center;
  gap: 6px; // 圆点和文字之间的间距
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #999; // 默认颜色，根据状态动态变化
}
// 告警标签
.label-style {
  padding: 3px 8px;
  border-radius: 5px;
  background-color: #f1f4f9;
}
</style>
