<template>
  <ContentWrap class="my-component">
    <header>
      <el-button class="ml-5 mr-5" type="primary" plain @click="goBack">返回</el-button>
      <p>告警白名单-详情 </p>
    </header>
    <main>
      <div class="main-left">
        <p class="title-box">设备详情</p>
        <div class="detail-box">
          <PrivateShow :assetsCode="params.assetsCode" />
        </div>
      </div>
      <div class="main-right">
        <header class="border-style">
          <img v-if="detailData?.pictureUrl" :src="detailData?.pictureUrl" alt="" />
          <img v-else src="@/assets/imgs/device.png" alt="" />
        </header>
        <footer class="border-style">
          <div class="edit-time" v-for="item in logList" :key="item">
            <p>由{{ item.userName }}{{ item.subType }}</p>
            <p>{{ UTILS.timestampToDateString2(item.createTime) }}</p>
          </div>
        </footer>
      </div>
    </main>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, onBeforeRouteLeave } from 'vue-router'
import * as TicketsPushApi from '@/api/operations/ticketsPush'
import * as UTILS from './utils/index' //引入筛选的工具函数
import PrivateShow from '@/components/PrivateShow/index.vue' //引入详情组件
import * as WhiteListApi from '@/api/alarm/whiteList' //引入api
const route = useRoute()
const params = reactive({ assetsCode: '', id: '' })

const detailData = ref()

onMounted(async () => {
  const storedObjStr = sessionStorage.getItem('alarmWhiteListObj')
  if (storedObjStr) {
    const storedObj = JSON.parse(storedObjStr)
    // console.log('打印storedObj', storedObj)
    params.assetsCode = storedObj?.assetsCode //用处传递给详情组件使用
    const res = await WhiteListApi.getDetail(params)
    detailData.value = res?.deviceOverviewInfo
    if (res?.deviceOverviewInfo?.id) {
      params.id = res?.deviceOverviewInfo?.id //用于查询右下角详情日志使用
    }

    await getLog()
  } else {
    ElMessage.warning('已离开页面,请重新选择项目')
  }
  // const storedAssetsCode = sessionStorage.getItem('alarmWhiteListAssetsCode')
  // if (storedAssetsCode) {
  //   params.assetsCode = storedAssetsCode //赋值用于给详情组件使用
  // } else {
  //   ElMessage.warning('已离开页面,请重新选择项目')
  // }
})

// 使用 beforeRouteLeave 导航守卫
onBeforeRouteLeave((to, from) => {
  // 清除 sessionStorage 中的 alarmWhiteListId
  // sessionStorage.removeItem('alarmWhiteListId')
})

// 返回上一页
const goBack = () => {
  window.history.back()
}
// 获得日志
const logList = ref([])
const getLog = async () => {
  const data = await TicketsPushApi.getLog({ bizId: params.id, type: 'DEVICE_MANAGE_CENTER' })
  // console.log('日志', data)
  logList.value = data.list
}
</script>

<style lang="scss" scoped>
.border-style {
  border: 1px solid #ccc;
  border-radius: 1%;
}

.my-component {
  // width: 100%;
  // height: 100%;
  background-color: white;
  header {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ccc;
    margin-bottom: 10px;
  }
  main {
    width: 100%;
    height: 680px;
    display: flex;
    .main-left {
      flex: 1;
      margin-right: 10px;
      // margin-left: 20px;

      .title-box {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        position: relative;
        font-family: Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #3b3c3d;
        text-align: left;
        font-style: normal;
        text-transform: none;
        line-height: 24px;
        margin: 3px 0;
        padding-left: 28px;

        &::before {
          margin: 10px 0 0 10px;
          content: '';
          position: absolute;
          left: 0;
          top: 6px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #005fff;
          border: 2px solid #d4e4ff;
        }
      }
      .detail-box {
        width: 97%;
        height: 88%;
        margin: 0 12px;
        display: flex;
        .detail-left {
          width: 48%;
        }
        .detail-right {
          width: 48%;
        }
      }
    }
    .main-right {
      // background-color: navajowhite;
      width: 250px;
      height: 100%;
      border: 1px solid #ccc;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;
      header {
        width: 95%;
        height: 25%;
        // background-color: pink;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 95%;
          height: 90%;
        }
      }
      main {
        width: 95%;
        height: 25%;
        // background-color: lightcoral;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 95%;
          height: 90%;
        }
      }
      footer {
        width: 95%;
        height: 72%;
        // background-color: antiquewhite;
        overflow-y: auto;
        &::-webkit-scrollbar {
          width: 6px; // 滚动条宽度
          height: 6px; // 滚动条高度
        }
        &::-webkit-scrollbar-thumb {
          background-color: #c1c1c1c1; // 滚动条颜色
          border-radius: 3px; // 滚动条圆角
        }

        &::-webkit-scrollbar-track {
          background-color: #f1f1f1f1; // 滚动条轨道颜色
          border-radius: 3px; // 滚动条轨道圆角
        }
        .edit-time {
          margin-left: 20px;
          width: 90%;
          height: 70px;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          // background-color: palegreen;
          margin-bottom: 20px;
        }
      }
    }
  }
}
</style>
