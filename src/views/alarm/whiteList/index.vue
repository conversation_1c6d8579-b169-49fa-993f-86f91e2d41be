<template>
  <div class="mr-8">
    <ContentWrap class="my-component">
      <div class="top-title">告警白名单</div>

      <div class="main">
        <!-- 右侧盒子 -->
        <div class="right-main">
          <!-- 表单部分 -->
          <ContentWrap class="form-box">
            <el-form
              ref="queryFormRef"
              :inline="true"
              :model="formSearchPage"
              class="form-search flex flex-wrap items-start -mb-15px"
            >
              <!-- 搜索的相关输入框 -->
              <el-form-item class="!mr-3">
                <el-input
                  v-model="formSearchPage.assets"
                  placeholder="输入设备编号/名称"
                  class="!w-240px"
                  clearable
                />
              </el-form-item>

              <!-- 区域选择 -->
              <el-form-item label="区域" prop="areaId" class="!mr-3">
                <Dictionary
                  v-model="formSearchPage.areaId"
                  type="cascader"
                  width="180px"
                  dict-type="oamArea"
                  :cascader-props="{
                    multiple: true,
                    checkStrictly: true,
                    label: 'name',
                    value: 'id'
                  }"
                  :max-collapse-tags="2"
                  placeholder="请选择区域"
                />
              </el-form-item>

              <!-- 所属项目 -->
              <el-form-item label="所属项目:" prop="projectId" class="!mr-3">
                <el-select
                  v-model="formSearchPage.projectId"
                  placeholder="选择所属项目"
                  multiple
                  collapse-tags
                  class="!w-140px"
                >
                  <el-option
                    v-for="item in belongsProjectList"
                    :key="item.id"
                    :label="item.projectName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>

              <!-- 重点设备 -->
              <el-form-item label="重点设备" class="!mr-3">
                <el-select
                  v-model="formSearchPage.isKey"
                  class="!w-140px"
                  clearable
                  placeholder="是否重点"
                >
                  <el-option value="1" key="1" label="重点" />
                  <el-option value="0" key="0" label="非重点" />
                </el-select>
              </el-form-item>

              <!-- 设备来源 -->
              <el-form-item label="设备来源" class="!mr-3">
                <el-select
                  v-model="formSearchPage.assetsSource"
                  placeholder="选择设备来源"
                  class="!w-180px"
                >
                  <el-option
                    v-for="item in warnSourceList"
                    :key="item.id"
                    :label="item.sourceName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>

              <!-- 按钮区域 -->
              <div class="min-w-[850px] flex-1 flex justify-between mb-[18px] pl-2">
                <div>
                  <XButton
                    class="text-button"
                    preIcon="ep:search"
                    title="查询"
                    width="75px"
                    gradient
                    @click="onSubmit"
                    v-hasPermi="['infra:warn-white-list:query']"
                  />
                  <el-button @click="resetForm"> <Icon icon="ep:refresh" />重置 </el-button>
                </div>
                <div>
                  <el-button @click="addWhiteFun" v-hasPermi="['infra:warn-white-list:create']">
                    <Icon icon="ep:plus" />添加
                  </el-button>
                  <el-button
                    @click="openImportDialog"
                    v-hasPermi="['infra:warn-white-list:create']"
                  >
                    <Icon icon="ep:upload" />导入
                  </el-button>
                  <el-button
                    plain
                    @click="handleExport"
                    :loading="exportLoading"
                    v-hasPermi="['infra:warn-white-list:export']"
                  >
                    <Icon icon="ep:download" />导出
                  </el-button>
                  <el-button
                    plain
                    @click="allDeleteClick"
                    v-hasPermi="['infra:warn-white-list:delete']"
                  >
                    <Icon icon="ep:delete" />移出
                  </el-button>
                </div>
              </div>
            </el-form>
          </ContentWrap>
          <!-- 表格部分 -->
          <div class="right-table">
            <el-table
              :data="tableList"
              height="530"
              v-loading="loading"
              style="width: 100%; margin-bottom: 10px"
              @selection-change="handleSelectionChange"
              @sort-change="handleSortChange"
              :header-cell-style="{
                'background-color': '#F4F6F8',
                'font-size': '14px',
                color: '#3B3C3D'
              }"
            >
              <!-- 多选框列 -->
              <el-table-column type="selection" :selectable="selectable" min-width="55" />
              <!-- 其他列 -->
              <el-table-column type="index" :index="tableIndex" label="序号" min-width="60" />
              <el-table-column
                prop="assetsCode"
                label="设备编号"
                min-width="150"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <div style="display: flex; align-items: center">
                    <el-tooltip content="重点设备" placement="top" v-if="scope?.row?.isKey === 1">
                      <span class="pt-1">
                        <img class="w5 h5" src="@/assets/imgs/icons/keynote.png" alt="" />
                      </span>
                    </el-tooltip>

                    {{ scope.row.assetsCode }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="assetsName"
                label="设备名称"
                min-width="150"
                show-overflow-tooltip
              />
              <el-table-column
                prop="projectName"
                label="所属项目"
                min-width="100"
                show-overflow-tooltip
              />
              <el-table-column
                prop="assetsTypeName"
                label="设备类型"
                min-width="100"
                show-overflow-tooltip
              />
              <el-table-column prop="area" label="所属区域" min-width="100" show-overflow-tooltip />
              <el-table-column
                prop="operatingUnitName"
                label="运营商"
                min-width="120"
                show-overflow-tooltip
              />
              <el-table-column
                prop="maintenanceUnitName"
                label="维保单位"
                min-width="120"
                show-overflow-tooltip
              />
              <el-table-column
                prop="sourceName"
                label="设备来源"
                min-width="150"
                show-overflow-tooltip
              />
              <el-table-column
                prop="warnUpdateTime"
                label="有效期"
                min-width="180"
                show-overflow-tooltip
                sortable="custom"
              >
                <template #default="scope">
                  <div>{{ scope.row.startDate }}-{{ scope.row.endDate }}</div>
                </template>
              </el-table-column>
              <el-table-column
                prop="deviceStatus"
                label="设备状态"
                min-width="90"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <div :class="UTILS.getStatusTextClass(scope.row?.deviceStatus)">{{
                    scope.row?.deviceStatus
                  }}</div>
                </template>
              </el-table-column>

              <el-table-column
                prop="deviceStatus"
                label="有效状态"
                min-width="90"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <div
                    :class="UTILS.getEffectiveStatusClass(scope.row?.startDate, scope.row?.endDate)"
                    >{{ UTILS.getEffectiveStatus(scope.row?.startDate, scope.row?.endDate) }}</div
                  >
                </template>
              </el-table-column>

              <el-table-column prop="date" label="操作" min-width="100" fixed="right">
                <template #default="scope">
                  <el-button
                    link
                    type="primary"
                    size="small"
                    @click="removeFun(scope.row.id)"
                    v-hasPermi="['infra:warn-white-list:delete']"
                    >移出</el-button
                  >
                  <el-button
                    link
                    type="primary"
                    size="small"
                    @click="handleClick(scope.row)"
                    v-hasPermi="['infra:device-overview-info:query']"
                    >详情</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页部分 -->
            <div class="demo-pagination-block">
              <el-pagination
                v-model:current-page="form.pageNo"
                v-model:page-size="form.pageSize"
                :page-sizes="[10, 20, 30, 50, 100]"
                :size="size"
                :disabled="disabled"
                :background="background"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>
      </div>
    </ContentWrap>
  </div>

  <AddDialog ref="addDialogRef" @on-success="getWarnWhiteListFun" />
  <ImportComponents
    ref="importDialogRef"
    @success="getWarnWhiteListFun"
    :path="importPath"
    :downloadPath="downloadTemplatePath"
    title="告警白名单导入"
  />
</template>

<script lang="ts" setup>
import * as ListApi from '@/api/alarm/list' //引入api
import * as WhiteListApi from '@/api/alarm/whiteList' //引入api
import { hasPermission } from '@/directives/permission/hasPermi' //引入权限判断
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { getDictList } from '@/api/infra/deviceDict' //设备来源
import { useRouter } from 'vue-router'
import download from '@/utils/download'
import { ElMessage } from 'element-plus'
import * as UTILS from './utils/index.ts'
import AddDialog from './components/AddDialog.vue' // 引入遮罩层组件
import ImportComponents from '@/components/ImportComponents/index.vue' //引入全局的导入组件

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()

//序号自增
const tableIndex = (index: number) => {
  return (formSearchPage.pageNo - 1) * formSearchPage.pageSize + index + 1
}

onMounted(() => {
  // postListNoPageFun() //调用第一个接口
  getWarnWhiteListFun() //调用第二个接口
  getWarnSourceFun() //告警来源 设备来源
  getAssetsSourceList() //获得设备来源
  getBelongsProjectFun() //所属项目下拉
  window.addEventListener('keydown', handleGlobalKeyDown)
})
onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleGlobalKeyDown)
})
const handleGlobalKeyDown = (event) => {
  if (event.key === 'Enter') {
    onSubmit()
  }
}

const assetsSourceList = ref([]) //设备来源
const getAssetsSourceList = async () => {
  const data = await getDictList('deviceSource')
  // console.log('设备来源', data)
  assetsSourceList.value = data
}

// 搜索表单的告警来源 设备来源
const warnSourceList = ref([])
const getWarnSourceFun = async () => {
  const data = await ListApi.getWarnSource()
  warnSourceList.value = data
  // console.log('打印告警来源/设备来源', warnSourceList.value)
}

// 所属项目下拉
const belongsProjectList = ref([])
const getBelongsProjectFun = async () => {
  const data = await ListApi.getBelongsProject()
  belongsProjectList.value = data
  // console.log('打印所属项目', belongsProjectList.value)
}

const sortParams = ref({
  prop: null,
  order: null
})

const handleSortChange = (column) => {
  const { prop, order } = column
  sortParams.value = { prop, order }

  if (prop === 'warnUpdateTime') {
    formSearchPage.orderParam = 'startDate' // 固定传 "startDate"
    formSearchPage.asc = order === 'ascending'

    onSubmit()
  }
}
// 第一个分页查询接口
// const postListNoPageFun = async () => {
//   const data = await ListApi.postListNoPage(form)
//   // console.log('打印第一个接口数据', data)
//   if (data.length) {
//     // 存在数据调用下一个接口
//     formSearchPage.assetsCode = data.map((item) => item.assetsCode)
//     // console.log('打印拼接后设备编号', formSearchPage.assetsCode)
//     getWarnWhiteListFun()
//   }
// }
// 第二个分页查询接口
const getWarnWhiteListFun = async () => {
  loading.value = true
  try {
    const data = await WhiteListApi.getWarnWhiteList(formSearchPage)
    tableList.value = data.list
    total.value = data.total
    // console.log('打印分页数据', data)
  } finally {
    loading.value = false
  }
}
const tableList = ref([])

// 搜索表单，用于获取第一个分页接口
const form = reactive({
  assets: null, //设备编号/名称
  areaId: [], //所属区域
  projectId: [], //所属项目
  isKey: null, //是否重点 重点设备
  assetsSource: null //告警来源 设备来源
})

// 搜索表单，用于获取第二个分页接口
const formSearchPage = reactive({
  // 合并第一个表单
  assets: null, //设备编号/名称
  areaId: [], //所属区域
  projectId: [], //所属项目
  isKey: null, //是否重点 重点设备
  assetsSource: null, //告警来源 设备来源

  // 合并第二个表单
  pageNo: 1,
  pageSize: 10,
  assetsCode: [],
  ids2: [], //多选框用于导出等处理
  orderParam: null, // 排序列名
  asc: null // 是否升序：true / false
})
// 分页相关
const loading = ref(true)
const total = ref(0)
const size = ref('default')
const background = ref(true)
const disabled = ref(false)

// 多选框相关
const selectedRows = ref([]) // 选中的行数据

// 处理选中行变化
const handleSelectionChange = (rows) => {
  selectedRows.value = rows
  // 有选中的进行赋值用于导出使用
  if (selectedRows.value.length) {
    formSearchPage.ids2 = rows.map((item) => item.id)
  } else {
    formSearchPage.ids2 = [] //没有选中的置空
  }
  // console.log('选中的行数据：', selectedRows.value)
}

// 控制哪些行可以被选择
const selectable = (row, index) => {
  // 例如：禁用第一行的选择
  return index !== -1
}

// 其他事件
const handleSizeChange = (val: number) => {
  // form.pageSize = val
  formSearchPage.pageSize = val
  onSubmit() //重新获取
}
const handleCurrentChange = (val: number) => {
  // form.pageNo = val
  formSearchPage.pageNo = val
  onSubmit() //重新获取
}

const handleClick = (obj) => {
  // console.log('打印当前行数据', obj)
  sessionStorage.setItem('alarmWhiteListObj', JSON.stringify(obj))
  // sessionStorage.setItem('alarmWhiteListAssetsCode', assetsCode.toString())
  router.push({
    path: '/alarm/whiteListDetail'
    // query: { id: id.toString() } // 将 id 值传递到详情页
  })
}

const onSubmit = () => {
  formSearchPage.ids2 = [] //清除多选框
  // postListNoPageFun() //调用第一个接口
  getWarnWhiteListFun() //调用第二个接口
}

const resetForm = async () => {
  formSearchPage.pageNo = 1
  formSearchPage.pageSize = 10
  formSearchPage.assets = null //设备编号
  formSearchPage.areaId = [] // 所属区域
  formSearchPage.isKey = null //是否重点
  formSearchPage.assetsSource = null // 设备来源
  formSearchPage.projectId = [] //所属项目
  // 第二个接口数据
  formSearchPage.assetsCode = [] //资产编号
  formSearchPage.orderParam = null //排序名称
  formSearchPage.asc = null //排序方式
  // postListNoPageFun() //重新获取数据
  getWarnWhiteListFun()
}

// 点击移出
const removeFun = async (id: number) => {
  try {
    // 删除的二次确认
    await message.removeConfirm()
    await WhiteListApi.deleteWarnWhite({ ids: id })
    await message.success(t('common.removeSuccess'))
    // await postListNoPageFun() //重新获取数据
    formSearchPage.ids2 = []
    await getWarnWhiteListFun()
  } catch (error) {
    return
  }
}

// 批量移出 选中群体移出
const allDeleteClick = async () => {
  if (formSearchPage.ids2.length) {
    // 执行删除操作
    try {
      // 删除的二次确认
      await message.removeConfirm()
      // 发起移出
      await WhiteListApi.deleteWarnWhite({ ids: formSearchPage.ids2.join(',') })
      // ElMessage.success('暂未开发')
      message.success(t('common.removeSuccess'))
      // 刷新列表
      formSearchPage.ids2 = []
      await getWarnWhiteListFun()
    } catch {}
  } else {
    ElMessage.warning('请选择要移出的设备')
  }
}

const addDialogRef = ref(null)
const addWhiteFun = async () => {
  addDialogRef.value.showAddDialog()
}

// 导入子组件的引用
const importDialogRef = ref(null)

// 导入接口地址和导出模板的接口地址
const importPath = ref('/infra/warn-white-list/import-warn-white')
const downloadTemplatePath = ref('/infra/warn-white-list/get-import-template')

// 打开导入弹窗
const openImportDialog = () => {
  // 确保子组件已挂载并且 open 方法可用
  if (importDialogRef.value && typeof importDialogRef.value.open === 'function') {
    importDialogRef.value.open()
  } else {
    console.error('ImportDialog组件未正确挂载或缺少open方法')
  }
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    // const data = await ListApi.postListNoPage(form) //调用第一个不分页接口
    // if (data.length) {
    // 存在数据调用下一个接口
    // formSearchPage.assetsCode = data.map((item) => item.assetsCode)

    const newData = await WhiteListApi.exportWarnWhite(formSearchPage) //调用导出接口
    download.excel(newData, '告警白名单.xls')
    // } else {
    //   ElMessage.warning('无可导出的数据')
    //   //无数据清空展示并打断执行
    //   return
    // }
  } catch {
  } finally {
    exportLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.my-component {
  // width: 100% !important;
  // height: 100% !important;
  background-color: white;
  display: flex;
  flex-direction: column;
  .top-title {
    width: 100%;
    height: 60px;
    font-size: 18px;
    font-weight: bold;
    padding-left: 20px;
    display: flex;
    align-items: center;
  }

  .main {
    flex: 1;
    width: 100%;
    display: flex;
    // padding-left: 20px;

    .right-main {
      flex: 1;
      width: 100%;
      display: flex;
      flex-direction: column;
      // overflow-x: auto;
      min-width: 0; /* 允许内容被裁剪而不是挤压其他元素 */
      // 隐藏滚动条
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
      }
      .form-box {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 10px;
        overflow-x: auto;
        .form-search {
          // flex: 1;
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          // background-color: yellowgreen;
          .el-form-item {
            margin-right: 10px;
          }
        }
      }

      // .right-table {
      //   width: 100%;
      //   flex: 1;
      //   padding-right: 1px;
      //   max-height: 540px; //设置最大高度溢出滚动
      //   overflow-y: auto;
      //   // 隐藏滚动条
      //   scrollbar-width: none; /* Firefox */
      //   -ms-overflow-style: none; /* IE 10+ */
      //   &::-webkit-scrollbar {
      //     display: none; /* Chrome, Safari, Edge */
      //   }
      //   :deep(.el-table__header th) {
      //     text-align: left;
      //   }
      //   :deep(.el-table td) {
      //     text-align: left;
      //   }
      //   .demo-pagination-block {
      //     margin-top: 10px;
      //   }
      // }
      .demo-pagination-block {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}
// 查询按钮样式
.text-button {
  color: #fff;
}
// 高级搜索底部按钮
.bottom-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  button {
    width: 100px;
    margin-right: 20px;
  }
}
// 状态文字颜色
.status-normal {
  color: green;
}

.status-reported {
  color: orange;
}

.status-fault {
  color: red;
}
</style>
