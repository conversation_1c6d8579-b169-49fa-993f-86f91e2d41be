<template>
  <el-dialog class="add-dialog" v-model="dialogVisible" title="添加白名单" width="75%">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="150px">
      <el-form-item label="设置白名单有效期：" prop="dateRange">
        <div style="display: flex; align-items: center">
          <el-date-picker
            v-model="form.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </div>
      </el-form-item>
    </el-form>

    <main>
      <div class="dialog-left">
        <ContentWrap class="ContentWrap-style">
          <DeptTree @node-click="handleDeptNodeClick" />
        </ContentWrap>
      </div>

      <div class="dialog-right">
        <div class="right-top">
          <el-form :model="formSearch" class="form-search">
            <el-row :gutter="20">
              <el-col :span="4">
                <el-form-item>
                  <el-input v-model="formSearch.assetsCode" placeholder="输入设备编号/名称" />
                </el-form-item>
              </el-col>

              <el-col :span="4">
                <el-form-item label="类型">
                  <el-cascader
                    style="width: 100%"
                    v-model="formSearch.assetsTypeId"
                    :options="typeList"
                    :props="cascaderProps"
                    collapse-tags
                    clearable
                  />
                </el-form-item>
              </el-col>

              <el-col :span="5">
                <el-form-item label="区域" prop="areaId">
                  <Dictionary
                    v-model="formSearch.areaId"
                    type="cascader"
                    width="240px"
                    dict-type="oamArea"
                    :cascader-props="{
                      multiple: true,
                      checkStrictly: true,
                      label: 'name',
                      value: 'id'
                    }"
                    :max-collapse-tags="2"
                    placeholder="请选择区域"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="设备状态" prop="deviceStatus">
                  <el-select v-model="formSearch.deviceStatus" placeholder="选择设备状态">
                    <el-option label="正常" :value="0" />
                    <el-option label="故障" :value="1" />
                    <el-option label="报备" :value="2" />
                    <el-option label="告警" :value="3" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <XButton
                  class="text-button"
                  preIcon="ep:search"
                  title="查询"
                  width="75px"
                  gradient
                  @click="dialogSubmit"
                />
                <XButton
                  class="text-button"
                  title="重置"
                  width="75px"
                  gradient
                  @click="restNoPage"
                />
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="right-tabel">
          <el-table
            ref="tableRef"
            :data="dialogTableData"
            style="width: 100%; margin-bottom: 10px"
            @selection-change="handleSelectionChange"
            class="table-style"
          >
            <el-table-column type="selection" :selectable="selectable" width="55" />
            <el-table-column prop="assetsCode" label="资产编号" width="150" show-overflow-tooltip />
            <el-table-column prop="assetsName" label="资产名称" width="150" show-overflow-tooltip />
            <el-table-column
              prop="projectName"
              label="所属项目"
              width="120"
              show-overflow-tooltip
            />
            <el-table-column prop="groupName" label="所属分组" width="120" show-overflow-tooltip />
            <el-table-column
              prop="assetsTypeName"
              label="资产类型"
              width="120"
              show-overflow-tooltip
            />
            <el-table-column prop="area" label="区域" width="120" show-overflow-tooltip />
            <el-table-column prop="address" label="地址" width="120" show-overflow-tooltip />
            <el-table-column prop="deviceStatus" label="设备状态" width="120" show-overflow-tooltip>
              <template #default="scope">
                <div :class="UTILS.getStatusClass3(scope.row.deviceStatus)">
                  {{ UTILS.getDeviceStatusText(scope.row.deviceStatus) }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </main>

    <footer>
      <el-button type="primary" @click="submitForm">确认</el-button>
      <el-button @click="cancelFun">取消</el-button>
    </footer>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import DeptTree from './DeptTree.vue'
import * as UTILS from '../utils'
import * as TicketsPushApi from '@/api/operations/ticketsPush'
import * as WhiteListApi from '@/api/alarm/whiteList' //引入api
import { ElMessage } from 'element-plus'

onMounted(() => {
  getType() //调用设备类型下拉接口
  getNoPageListFun() //获取弹窗表格数据
})

// 引入 defineEmits
const emit = defineEmits(['on-success']) // 定义可触发的事件
const formRef = ref(null)
const dialogVisible = ref(false)
const dateRange = ref(null)
const startDate = ref(null)
const endDate = ref(null)

const form = reactive({
  dateRange: null
})

const rules = {
  dateRange: [{ required: true, message: '请选择白名单有效期', trigger: 'change' }]
}

const addForm = reactive({
  groupId: null,
  ids: []
})
const formSearch = reactive({
  assetsCode: null, //编号/名称
  assetsTypeId: [], //设备类型
  areaId: [], //区域
  deviceStatus: null, //设备状态
  projectId: [] //所属项目id
})
const dialogTableData = ref([])
const selectedRows = ref([])
const topGroup = ref([])

// 获得设备类型 顶部下拉框
const cascaderProps = reactive({
  value: 'id',
  label: 'name',
  children: 'children',
  multiple: true,
  checkStrictly: true,
  emitPath: false
})

const typeList = ref([])
const getType = async () => {
  const data = await TicketsPushApi.getType()
  // console.log('设备类型typeList', data)
  typeList.value = data
}
const tableRef = ref(null)
const showAddDialog = () => {
  dialogVisible.value = true
  resetForm()
  selectedRows.value = [] // 清空已选数据
  if (tableRef.value) {
    tableRef.value.clearSelection() // 清除表格多选状态
  }
}

// 控制哪些行可以被选择
const selectable = (row, index) => {
  return index !== -1
}

const restAddForm = () => {
  addForm.groupId = null
  addForm.ids = []
}

const restNoPage = async () => {
  formSearch.assetsCode = null //编号/名称
  formSearch.assetsTypeId = [] //设备类型
  formSearch.areaId = [] //区域
  formSearch.deviceStatus = null //设备状态
  formSearch.projectId = [] //所属项目id点击左侧搜索
  await dialogSubmit()
}

const handleDeptNodeClick = async (row: { [key: string]: any }) => {
  formSearch.projectId = [] // 清空旧数据

  if (row.children && row.children.length > 0) {
    // 如果是父节点，收集所有子节点的 id
    formSearch.projectId = row.children.map((child) => child.id)
  } else {
    // 如果是子节点，直接使用当前 id
    formSearch.projectId.push(row.id)
  }
  dialogSubmit() // 触发查询
}

// const handleDeptNodeClick = (row) => {
//   formSearch.projectId = []
//   formSearch.projectId.push(row.id)
//   dialogSubmit()
// }

const dialogSubmit = async () => {
  await getNoPageListFun()
}

// 获取对话框内不分页表格数据
const getNoPageListFun = async () => {
  const data = await TicketsPushApi.getNoPageList(formSearch)
  // console.log('打印弹窗表格数据', data)
  dialogTableData.value = data
}

const handleSelectionChange = (rows) => {
  selectedRows.value = rows
}

const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      if (!selectedRows.value.length) {
        ElMessage.warning('请选择要添加的设备')
        return
      }

      // 组装数据
      const requestData = {
        whiteListSaveReqListVOList: selectedRows.value.map((item) => ({
          assetsCode: item.assetsCode,
          sourceId: item.assetsTypeId
        })),
        startDate: form.dateRange[0],
        endDate: form.dateRange[1]
      }

      WhiteListApi.postAddWhite(requestData).then((res) => {
        dialogVisible.value = false
        ElMessage.success('操作成功')
        // 触发自定义事件，通知父组件
        emit('on-success')
      })
    } else {
      return false
    }
  })
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  restAddForm()
}

const cancelFun = () => {
  dialogVisible.value = false
  resetForm()
}

// 暴露方法给父组件
defineExpose({
  showAddDialog
})
</script>

<style lang="scss" scoped>
.add-dialog {
  width: 100%;
  height: 100%;

  main {
    width: 100%;
    height: 400px;
    display: flex;
    box-sizing: border-box;

    .dialog-left {
      width: 20%;
      height: 100%;
      border: solid 1px #ccc;
      border-radius: 3px;
      overflow-y: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;
      &::-webkit-scrollbar {
        display: none;
      }
    }

    .dialog-right {
      width: 79%;
      height: 100%;
      border: solid 1px #ccc;
      border-radius: 3px;
      margin-left: 10px;
      display: flex;
      flex-direction: column;

      .right-top {
        width: 100%;
        height: 50px;
        padding: 10px;
        box-sizing: border-box;
      }

      .right-tabel {
        width: 100%;
        flex: 1;
        overflow-y: auto;
      }
    }
  }

  footer {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: end;
    padding: 0 20px;
    box-sizing: border-box;
  }
}
// 状态文字颜色
.status-normal {
  color: green;
}

.status-fault {
  color: yellow;
}

.status-reported {
  color: orange;
}

.status-alarm {
  color: red;
}
</style>
