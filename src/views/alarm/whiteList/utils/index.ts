// 时间戳转换
export function timestampToDateString(timestamp: number): string {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}/${month}/${day}`
}
//时间戳转化
export function timestampToDateString2(timestamp: number): string {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 表格设备状态文字 样式
export function getStatusTextClass(status): string {
  switch (status) {
    case '正常':
      return 'status-normal'
    case '告警':
      return 'status-fault'
    default:
      return ''
  }
}
// 有效状态 文字
// export function getEffectiveStatus(startDate: string, endDate: string): string {
//   const currentTimestamp = Date.now()
//   const startTimestamp = new Date(startDate).getTime()
//   const endTimestamp = new Date(endDate).getTime()
//   if (currentTimestamp < startTimestamp) {
//     return '待开始'
//   } else if (currentTimestamp > endTimestamp) {
//     return '已过期'
//   } else {
//     return '生效中'
//   }
// }
export function getEffectiveStatus(startDate: string, endDate: string): string {
  const now = new Date()
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime()

  const start = new Date(startDate).getTime()
  const end = new Date(endDate).getTime()

  // 只比较“天”级别
  const startOfStartDay = new Date(
    new Date(start).getFullYear(),
    new Date(start).getMonth(),
    new Date(start).getDate()
  ).getTime()
  const startOfEndDay = new Date(
    new Date(end).getFullYear(),
    new Date(end).getMonth(),
    new Date(end).getDate()
  ).getTime()

  if (todayStart < startOfStartDay) {
    return '待开始'
  } else if (todayStart > startOfEndDay) {
    return '已过期'
  } else {
    return '生效中'
  }
}
// 有效状态 样式
// export function getEffectiveStatusClass(startDate: string, endDate: string): string {
//   const currentTimestamp = Date.now()
//   const startTimestamp = new Date(startDate).getTime()
//   const endTimestamp = new Date(endDate).getTime()
//   if (currentTimestamp < startTimestamp) {
//     return 'status-reported'
//   } else if (currentTimestamp > endTimestamp) {
//     return 'status-fault'
//   } else {
//     return 'status-normal'
//   }
// }
export function getEffectiveStatusClass(startDate: string, endDate: string): string {
  const now = new Date()
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime()

  const start = new Date(startDate).getTime()
  const end = new Date(endDate).getTime()

  const startOfStartDay = new Date(
    new Date(start).getFullYear(),
    new Date(start).getMonth(),
    new Date(start).getDate()
  ).getTime()
  const startOfEndDay = new Date(
    new Date(end).getFullYear(),
    new Date(end).getMonth(),
    new Date(end).getDate()
  ).getTime()

  if (todayStart < startOfStartDay) {
    return 'status-reported'
  } else if (todayStart > startOfEndDay) {
    return 'status-fault'
  } else {
    return 'status-normal'
  }
}
// 表格状态文字
export function getDeviceStatusText(status: number): string {
  switch (status) {
    case 0:
      return '正常'
    case 1:
      return '故障'
    case 2:
      return '报备'
    case 3:
      return '告警'
    default:
      return ''
  }
}

// 表格状态类
export function getStatusClass3(status: number): string {
  switch (status) {
    case 0:
      return 'status-normal'
    case 1:
      return 'status-fault'
    case 2:
      return 'status-reported'
    case 3:
      return 'status-alarm'
    default:
      return ''
  }
}
