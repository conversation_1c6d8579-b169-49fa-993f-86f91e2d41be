<template>

  <div class="box">
    <!-- 头部导航 -->
    <header class="header">
      <div class="header-left">
        <el-button type="info" :icon="ArrowLeft" @click="goback()" plain>返回</el-button>
        <div class="title">设备指标-指标配置</div>
      </div>
      <div class="header-right">
      </div>
    </header>

    <!-- 主体内容区 -->
    <div class="content-wrapper">
      <!-- 左侧详情 -->
      <div class="content-left">
        <!-- 辅件详情 -->
        <div class="detail-card" >
          <div style="display: flex;align-items: center;justify-content: space-between;margin-top: 20px;">
            <div class="card-header">
<!--              <span class="header-bar"></span>-->
<!--              <span>监控设施例行检查241219-0001</span>-->
            </div>
            <div style="margin-right: 20px;">
              <el-button class="gradient-btn"  @click="addquota" :icon="Plus">新增</el-button>
            </div>
          </div>
          <div class="detail-content">

         <div class="middle">
           <div class="middle-text">
             <span class="middle-title">所选类型:</span>
             <div class="middle-device">XXXX设备类型</div></div>
           <div class="middle-tabel">
             <el-table ref="myTable" row-key="id"  :max-height="400" v-loading="loading" :data="tabelList">
               <el-table-column align="left" label="序号" width="55" prop="SerialNumber"/>
               <el-table-column align="left" label="指标内容"  prop="content">
                 <template #default="scope">
                   <el-input v-model="scope.row.content" />
                 </template>
               </el-table-column>
               <el-table-column align="left" label="正常选项" prop="normal">
                 <template #default="scope">
                   <el-input disabled v-model="scope.row.normal" />
                 </template>
                 </el-table-column>
                 <el-table-column align="left" label="异常选项" prop="abnormal">
                 <template #default="scope">
                   <el-input disabled v-model="scope.row.abnormal" />
                 </template>
               </el-table-column>
                 <el-table-column align="left" label="附件" width="80" prop="attachment">
                   <template #default="scope">
                     <el-checkbox v-model="scope.row.attachment" label="" size="large" />
                   </template>
                 </el-table-column>
               <el-table-column align="left" label="说明" width="80" prop="describe">
                 <template #default="scope">
                   <el-checkbox v-model="scope.row.code" label="" size="large" />
                 </template>
               </el-table-column>
               <el-table-column align="left" width="180" label="操作">
                 <template #default="scope">
                  <div class="operate">
                  <img  src="@/assets/imgs/maintenances/tianjia.png" @click="handleAdd(scope.row)"/>
                  <img class="mageLeft" v-if="scope.row.show" src="@/assets/imgs/maintenances/xianshi.png" @click="handleShow(scope.row)"/>
                  <img class="mageLeft" v-else src="@/assets/imgs/maintenances/yingc.png" @click="handleShow(scope.row)"/>
                  <img class="mageLeft"  src="@/assets/imgs/maintenances/yidong.png" @click="handleMove(scope.row)"/>
                  <img class="mageLeft"  src="@/assets/imgs/maintenances/shanchu.png" @click="handleDelete(scope.row)"/>
                  </div>
                 </template>
               </el-table-column>
             </el-table>
           </div>
         </div>

          </div>
          <div class="detail-card" style="margin-top: 15px;" >
            <div style="display: flex;align-items: center;justify-content: space-between;">
              <div class="card-header">
                <span class="header-bar"></span>
                <span>指标联动</span>
              </div>
              <div>
              </div>
            </div>
            <div class="detail-content" style="padding-top: 0;">
              <el-scrollbar max-height="300px">
              <div class="BasicContent">
               <div v-for="itm in 3" :key="itm" class="linkage">
                 <div class="Linkagevalue">
                   <el-form
                     ref="queryFormRef"
                     :inline="true"
                     :model="queryParams"
                     class="flex flex-wrap items-start -mb-15px"
                   >
                     <el-form-item label="当前指标项" prop="name" class="!mr-3">
                       <el-select v-model="queryParams.name" placeholder="Select" style="width: 240px">
                         <el-option
                           v-for="item in options"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value"
                           :disabled="item.disabled"
                         />
                       </el-select>
                     </el-form-item>
                     <el-form-item label="选项为" prop="name" class="!mr-3">
                       <el-select v-model="queryParams.name" placeholder="Select" style="width: 100px">
                         <el-option
                           v-for="item in options"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value"
                           :disabled="item.disabled"
                         />
                       </el-select>
                     </el-form-item>
                     <el-form-item label="" prop="name" class="!mr-3">
                       <el-select v-model="queryParams.name" placeholder="Select" style="width: 100px">
                         <el-option
                           v-for="item in options"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value"
                           :disabled="item.disabled"
                         />
                       </el-select>
                     </el-form-item>
                     <el-form-item label="" prop="name" class="!mr-3">
                       <el-select v-model="queryParams.name" placeholder="Select" style="width: 240px">
                         <el-option
                           v-for="item in options"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value"
                           :disabled="item.disabled"
                         />
                       </el-select>
                     </el-form-item>
                     <el-form-item label="赋值为" prop="name" class="!mr-3">
                       <el-select v-model="queryParams.name" placeholder="Select" style="width: 100px">
                         <el-option
                           v-for="item in options"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value"
                           :disabled="item.disabled"
                         />
                       </el-select>
                     </el-form-item>
                   </el-form>
                   <div>
                     <img src="@/assets/imgs/maintenances/shanchu.png"/>
                   </div>
                 </div>

               </div>
                <div class="add-condition">
                  <img src="@/assets/imgs/maintenances/addtiaojian.png"/>
                  <span>添加联动条件</span>
                </div>
              </div>
              </el-scrollbar>

            </div>
          </div>
          <div style="display: flex;align-items: center;justify-content: center;margin-top: 40px;margin-bottom: 30px;">
            <el-button style="width: 150px;" @click="dialogVisible=false">取消</el-button>
            <XButton title="保存" width="150px" gradient  @click="onParameter" />

          </div>
        </div>


      </div>
    </div>
    <Dialog v-model="dialogVisible" width="900" title="新增">
      <div class="dialog-header">
        <div></div>
        <el-button class="gradient-btn"  @click="addquota" :icon="Plus">新增</el-button>
      </div>
      <div style="margin-top: 20px;">
        <el-table ref="myTable" row-key="id"  :max-height="500" v-loading="loading" :data="tabelList">
          <el-table-column align="left" label="选项名称" width="600" prop="content">
            <template #default="scope">
              <el-input v-model="scope.row.content"/>
            </template>
          </el-table-column>
          <el-table-column align="left" label="正常" prop="normal">
            <template #default="scope">
              <el-checkbox v-model="scope.row.code" label="" size="large"/>
            </template>
          </el-table-column>
          <el-table-column align="left" label="异常" prop="abnormal">
            <template #default="scope">
              <el-checkbox v-model="scope.row.code" label="" size="large"/>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div style="display: flex;align-items: center;justify-content: center;">
          <XButton title="保存" width="150px" gradient  @click="onParameter" />
          <el-button style="width: 150px;" @click="dialogVisible=false">关闭</el-button>
        </div>
      </template>
    </Dialog>

  </div>
</template>

<script setup lang="ts">

import { reactive,toRefs,onBeforeMount,onMounted,ref} from 'vue'
import {  ArrowLeft,Plus} from '@element-plus/icons-vue'
import {useRouter,useRoute} from "vue-router";
import { ElMessage } from 'element-plus'
const { t } = useI18n() // 国际化

const router = useRouter();
const routers = useRoute();
let id= ref('')
const activeName = ref('first');
const dialogVisible = ref(false);
const loading = ref(false);
const formData = ref({});
const queryParams = ref({});
const tabelList = ref([{content:'测试',normal:'正常',abnormal:'否',attachment:false,describe:false,show:true}]);
const options = [
  {
    value: 'Option1',
    label: 'Option1阿三大苏打去实打实大苏打大大撒大大大大',
  },
  {
    value: 'Option2',
    label: 'Option2',
    disabled: true,
  },
  {
    value: 'Option3',
    label: 'Option3',
  },
  {
    value: 'Option4',
    label: 'Option4',
  },
  {
    value: 'Option5',
    label: 'Option5',
  },
]

const goback = () =>{
  router.go(-1)
}

//表单显示
const displayform = () => {
  dialogVisible.value=true
}

//查看指标
const ViewIndicators=()=>{
  dialogVisible.value=true
}


const onParameter=()=>{
  dialogVisible.value=false
}

// 处理添加操作
const handleAdd = (row) => {
  dialogVisible.value=true
  console.log('添加操作', row)
  // 这里可以修改row的属性值
}

// 处理显示操作
const handleShow = (row) => {
  row.show=!row.show
  console.log('显示操作', row)
}

// 处理隐藏操作
const handleHide = (row) => {
  console.log('隐藏操作', row)
}

// 处理移动操作
const handleMove = (row) => {
  console.log('移动操作', row)
}

//新增指标
const addquota=()=>{
  tabelList.value.push({content:'测试',normal:'正常',abnormal:'否',attachment:false,describe:false,show:true})
}

// 处理删除操作
const handleDelete = (row) => {
  console.log('删除操作', row)
  // 可以从tabelList中删除该行
  tabelList.value = tabelList.value.filter(item => item !== row)
}

onBeforeMount(() => {

})
onMounted(() => {
  // getLog(routers.query.id)
  // getdeviceImg(routers.query.id)
})
</script>
<style lang="scss" scoped>
.box {
  display: flex;
  flex-direction: column;
  //height: 100vh;
  background: #f5f6f8;
  overflow: hidden;
}

.dialog-header{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header {
  height: 48px;
  padding: 0 16px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);

  &-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .title {
    font-size: 16px;
    font-weight: 500;
  }
}

.content-wrapper {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 1px;
  min-height: 0; // 重要：防止内容溢出
  overflow: hidden;
}

.content-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  //gap: 2px;
  min-width: 0; // 重要：防止内容溢出
}



.detail-card {
  background: #fff;
  border-radius: 4px;
  //height: calc(80% - 8px);
  //display: flex;
  //flex-direction: column;
}
.stateStyls{
  width: 96px;
  height: 32px;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  margin-right: 20px;
  line-height: 36px;
  background: linear-gradient( 180deg, #0081FF 0%, #13A1FF 100%);
  border-radius: 4px 4px 4px 4px;
  color: #ffffff;
  img{
    width: 30px;
    height: 24px;
  }
}

.BasicContent{
  background: #F6F7F9;
  padding: 10px;
  .linkage{
    background: #ffffff;
    padding:10px;
    .Linkagevalue{
      display: flex;
      justify-content: space-between;
      align-items: center;
      img {
        width: 24px;
        height: 24px;
        cursor: pointer;
      }
    }
  }
  .add-condition{
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
    margin-bottom: 10px;
    cursor: pointer;
    img{
      width: 24px;
      height: 24px;
    }
    span{
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #3B3C3D;
      line-height: 14px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}

.card-header {
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  span{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 14px;
    color: #3B3C3D;
    line-height: 21px;
    font-style: normal;
    text-transform: none;
  }
  .header-bar {
    width: 12px;
    height: 12px;
    background: #005FFF;
    border-radius: 10px ;
  }
}

.detail-content {
  flex: 1;
  padding: 16px;
  background: #ffffff;
  overflow: hidden;
  .middle{
    background: #F6F7F9;
    padding: 10px;
    .middle-text{
      display: flex;
      align-items: center;
      .middle-title{
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #545658;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .middle-device{
        margin-left: 8px;
        font-weight: bold;
        font-size: 14px;
        color: #3B3C3D;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  .middle-tabel{
    margin-top: 10px;
    padding: 10px;
    background: #FFFFFF;
    border-radius: 4px 4px 4px 4px;
  }
    .operate{
      display: flex;
      align-items:  center;
      img{
        width: 24px;
        height: 24px;
        cursor: pointer;
      }
      .mageLeft{
        margin-left: 10px;
      }
    }
  }
}

.detail-item {
  background: #f2f2f2;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.detail-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.detail-col {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: flex-start;

  .label {
    min-width: 90px;
    color: #666;
  }

  .value {
    flex: 1;
    color: #333;
    word-break: break-all;
  }
}
.gradient-btn {
  background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
  border: none !important;
  font-size: 14px !important;
  padding: 9px 16px !important;
  color: #ffffff !important;

  &:hover {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.8;
  }

  &:active {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.9;
  }

  // 覆盖 element-plus 的默认样式
  &.el-button--primary {
    --el-button-hover-bg-color: transparent;
    --el-button-active-bg-color: transparent;
  }
}

/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
