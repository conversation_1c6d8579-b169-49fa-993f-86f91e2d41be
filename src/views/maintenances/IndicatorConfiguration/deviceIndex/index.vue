<template>
  <ContentWrap class="h-1/1">
    <span class="TitleStyle">设备指标</span>
  </ContentWrap>
  <ContentWrap >
    <el-tabs :tab-position="tabPosition"  class="demo-tabs">
      <el-tab-pane v-for="itm in positionList" :key="itm.value" :label="itm.label">
        <ContentWrap>
          <!-- 搜索工作栏 -->
          <el-form
            ref="queryFormRef"
            :inline="true"
            :model="queryParams"
            class="flex flex-wrap items-start -mb-15px"
          >
            <el-form-item label="" prop="name" class="!mr-3">
              <el-input
                v-model="queryParams.name"
                class="!w-240px"
                clearable
                placeholder="输入设备类型名称查询"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
              <div>
                <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:device-overview-info:query']" gradient @click="handleQuery"/>
                <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
              </div>
              <div>
                <el-button @click="handleImport" v-hasPermi="['infra:device-overview-info:import']" plain> 指标配置</el-button>
                <el-button @click="handleImport" v-hasPermi="['infra:device-overview-info:import']" plain> <Icon icon="ep:upload" />导入</el-button>
                <el-button plain v-hasPermi="['infra:device-overview-info:export']" @click="handleExport" :loading="exportLoading">
                  <Icon icon="ep:download" />导出
                </el-button>
              </div>
            </div>

          </el-form>
        </ContentWrap>

        <!-- 列表 -->
        <ContentWrap>
          <el-table ref="myTable" v-loading="loading" :data="list">
            <el-table-column align="left" label="类型名称" prop="name"/>
            <el-table-column align="left" label="类型编码" prop="name"/>
            <el-table-column align="left" label="指标" prop="name"/>
            <el-table-column align="left" label="更新时间" prop="name"/>
            <el-table-column :width="150" align="left" label="操作">
              <template #default="scope">
                <el-button
                  link
                  preIcon="ep:basketball"
                  title="功能权限"
                  type="primary"
                  @click="openDetails(scope.row)"
                >
                  详情
                </el-button>
                <el-button
                  link
                  preIcon="ep:basketball"
                  title="功能权限"
                  type="primary"
                  @click="openConfiguration(scope.row)"
                >
                  指标配置
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <Pagination
            v-model:limit="queryParams.pageSize"
            v-model:page="queryParams.pageNo"
            :total="total"
            @pagination="getList"
          />
        </ContentWrap>
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>


  <!-- 审批弹窗 -->
  <Dialog v-model="dialogVisible" width="1000" title="审批">
    <div class="examine">
      <div>设备编号: <span>xxxxxx</span></div>
      <div>设备名称: <span>xxxxxx</span></div>
      <div>申请人: <span style="margin-left: 50px;">研发部-张三</span></div>
      <div>申请说明: <span>xxxxxx</span></div>
    </div>

    <div>
      <el-form :model="examineForm" label-width="auto" style="margin-top: 30px;">

        <el-form-item label="报废申请审批">
          <el-select v-model="examineForm.region" placeholder="请选择">
            <el-option label="通过" value="shanghai"/>
            <el-option label="驳回" value="beijing"/>
          </el-select>
        </el-form-item>
        <el-form-item label="审批说明">
          <el-input v-model="examineForm.name" :rows="4" show-word-limit maxlength="30" type="textarea" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <el-button  style="width: 200px;" type="primary" >确定</el-button>
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { useRouter } from "vue-router";
import * as RoleApi from '@/api/system/role'

import {modifyStatus} from "@/api/system/role";

defineOptions({ name: 'PluginList' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // vue-router
const loading = ref(true) // 列表的加载中
const devicesLoading = ref(false) // 列表的加载中
const dialogVisible = ref(false) //  关联设备弹窗的显示隐藏
const total = ref(0) // 列表的总页数
const examineForm = ref({}) // 列表的总页数
const list = ref([{name:'测试'}]) // 列表的数据
const deviceslist = ref([{code:'ssssss11',name:'杆件'}]) // 列表的数据
const selectionList = ref([]) // 列表选中的数据
const positionList = ref([{label:'每日',value:'1'},{label:'每周',value:'2'},{label:'每月',value:'3'},{label:'每年',value:'4'},{label:'自定义',value:'5'}]) // 列表选中的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined,
  createTime: []
})
const devicesParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中


const onchange=(val)=>{
  console.log(val)
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 指标配置 */
const openConfiguration = () => {
  router.push('typeconfiguration')
}

/** 详情按钮操作 */
const openDetails = (value:number) => {
  router.push('RepairListDetails')
}

/** 审批按钮 */
const oneXamine = () => {
  dialogVisible.value = true
}



/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}


const myTable=ref()
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  if (selectionList.value.length <1) {
    message.warning('请选择要删除的角色！')
  }else {
    const ids = selectionList.value.map((item) => item.id).join(',')
    try {
      // 删除的二次确认
      await message.delConfirm()
      // 发起删除
      // await RoleApi.deleteRole(ids)
      message.success(t('common.delSuccess'))
      // 刷新列表
      // await getList()
    } catch {
      myTable.value.clearSelection();
    }
  }
}



//单个状态修改
const SingleState = async (type: string,row:any) => {
  console.log('sssss',row)
  let parms={...row,status:type== 'Disable'? '1' : '0'}
  try {
    // 操作的二次确认
    await message.confirm('你确定要操作该角色状态吗？')
    // 发起删除
    await modifyStatus([ parms])
    message.success('操作成功！')
    // 刷新列表
    await getList()
  } catch {

  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // const data = await PostApi.getList(deptName.value)
    // console.log('排班管理',data)
    // list.value = data
    // total.value = data.total
  } finally {
    loading.value = false
  }
}


/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
<style scoped>
.examine{
  padding: 20px;
  background: rgb(242 242 242);
  div{
    margin-top: 15px;
    span{
      margin-left: 40px;
    }
  }
}

</style>
