<template>
  <ContentWrap class="h-1/1">
    <span class="TitleStyle">任务查询</span>
  </ContentWrap>
  <!--  <div class="mb-4">-->
  <!--    <el-button type="primary" plain>全部: 112</el-button>-->
  <!--    <el-button type="warning" plain>待发布：22</el-button>-->
  <!--    <el-button type="success" plain>待执行：35</el-button>-->
  <!--    <el-button type="danger" plain>进行中：35</el-button>-->
  <!--    <el-button type="danger" plain>已终止：35</el-button>-->
  <!--    <el-button type="danger" plain>已结束：35</el-button>-->
  <!--  </div>-->
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="flex flex-wrap items-start -mb-15px"
    >
      <el-form-item label="" prop="name" class="!mr-3">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="输入计划编号查询"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="计划名称" prop="name" class="!mr-3">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="请输入"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="维保单位" prop="status" class="!mr-3">
        <el-select v-model="queryParams.status" class="!w-140px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开始时间" prop="status" class="!mr-3">
        <el-date-picker
          style="width: 240px;"
          v-model="queryParams.value2"
          @change="onchange"
          type="daterange"
          value-format="YYYY-MM-DD"
          unlink-panels
          range-separator="至"
          start-placeholder="开始"
          end-placeholder="结束"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="status" class="!mr-3">
        <el-date-picker
          style="width: 240px;"
          v-model="queryParams.value3"
          value-format="YYYY-MM-DD"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始"
          end-placeholder="结束"
        />
      </el-form-item>
      <div >
        <div>
          <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:device-overview-info:query']" gradient @click="handleQuery" />
          <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
        </div>
        <!--        <div>-->
        <!--          <el-button-->
        <!--            v-hasPermi="['system:role:create']"-->
        <!--            plain-->
        <!--            @click="oneXamine()"-->
        <!--          >-->
        <!--            创建计划-->
        <!--          </el-button>-->
        <!--        </div>-->
      </div>

    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table ref="myTable" v-loading="loading" :data="list">
      <el-table-column type="index" label="序号" width="55" align="left">
        <template #default="scope">
          <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column align="left" label="任务编号" prop="code" />
      <el-table-column align="left" label="任务名称" prop="name" />
      <el-table-column align="left" label="所属计划" prop="name" />
      <el-table-column align="left" label="任务开始时间" prop="name" />
      <el-table-column align="left" label="任务截止时间" prop="name" />
      <el-table-column align="left" label="维保单位" prop="name" />
      <el-table-column align="left" label="处理人" prop="name" />
      <el-table-column :width="120" align="left" label="操作">
        <template #default="scope">
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openDetails(scope.row)"
          >
            详情
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openDetails(scope.row)"
          >
            处理
          </el-button>

        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <!-- 审批弹窗 -->
  <Dialog v-model="dialogVisible" width="1000" title="审批">
    <div class="examine">
      <div>设备编号: <span>xxxxxx</span></div>
      <div>设备名称: <span>xxxxxx</span></div>
      <div>申请人: <span style="margin-left: 50px;">研发部-张三</span></div>
      <div>申请说明: <span>xxxxxx</span></div>
    </div>

    <div>
      <el-form :model="examineForm" label-width="auto" style="margin-top: 30px;">

        <el-form-item label="报废申请审批">
          <el-select v-model="examineForm.region" placeholder="请选择">
            <el-option label="通过" value="shanghai" />
            <el-option label="驳回" value="beijing" />
          </el-select>
        </el-form-item>
        <el-form-item label="审批说明">
          <el-input v-model="examineForm.name" :rows="4" show-word-limit maxlength="30" type="textarea" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <el-button  style="width: 200px;" type="primary" >确定</el-button>
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { useRouter } from "vue-router";
import * as RoleApi from '@/api/system/role'

import {modifyStatus} from "@/api/system/role";

defineOptions({ name: 'PluginList' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // vue-router
const loading = ref(true) // 列表的加载中
const devicesLoading = ref(false) // 列表的加载中
const dialogVisible = ref(false) //  关联设备弹窗的显示隐藏
const total = ref(0) // 列表的总页数
const examineForm = ref({}) // 列表的总页数
const list = ref([]) // 列表的数据
const deviceslist = ref([{code:'ssssss11',name:'杆件'}]) // 列表的数据
const selectionList = ref([]) // 列表选中的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined,
  createTime: []
})
const devicesParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中


const onchange=(val)=>{
  console.log(val)
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 新增按钮操作 */
const openAdd = () => {
  router.push('AccessoriesAdd')
}

/** 详情按钮操作 */
const openDetails = (value:number) => {
  router.push('RepairListDetails')
}

/** 审批按钮 */
const oneXamine = () => {
  dialogVisible.value = true
}



/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}


const myTable=ref()
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  if (selectionList.value.length <1) {
    message.warning('请选择要删除的角色！')
  }else {
    const ids = selectionList.value.map((item) => item.id).join(',')
    try {
      // 删除的二次确认
      await message.delConfirm()
      // 发起删除
      // await RoleApi.deleteRole(ids)
      message.success(t('common.delSuccess'))
      // 刷新列表
      // await getList()
    } catch {
      myTable.value.clearSelection();
    }
  }
}



//单个状态修改
const SingleState = async (type: string,row:any) => {
  console.log('sssss',row)
  let parms={...row,status:type== 'Disable'? '1' : '0'}
  try {
    // 操作的二次确认
    await message.confirm('你确定要操作该角色状态吗？')
    // 发起删除
    await modifyStatus([ parms])
    message.success('操作成功！')
    // 刷新列表
    await getList()
  } catch {

  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // const data = await PostApi.getList(deptName.value)
    // console.log('排班管理',data)
    // list.value = data
    // total.value = data.total
  } finally {
    loading.value = false
  }
}


/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
<style scoped>
.examine{
  padding: 20px;
  background: rgb(242 242 242);
  div{
    margin-top: 15px;
    span{
      margin-left: 40px;
    }
  }
}

</style>
