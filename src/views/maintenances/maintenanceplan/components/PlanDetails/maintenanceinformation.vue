<template>

  <div class="box">
    <!-- 主体内容区 -->
    <div class="detail-content">

    <div class="content" v-for="(item, index) in list" :key="index">
    <div class="content-head" @click="toggleExpand(index)">
      <img src="@/assets/imgs/maintenances/xiala.png"
           :style="{'cursor': 'pointer', 'transform': expandedItems[index] ? 'rotate(90deg)' : 'rotate(0deg)', 'transition': 'transform 0.3s ease'}"/>
      <img src="@/assets/imgs/maintenances/zhandian.png"/>
      <div class="textStyle">站点 {{item.code}} - {{item.name}}</div>
    </div>
      <div class="tableStyle" v-show="expandedItems[index]">
        <el-table ref="myTable" :max-height="230" v-loading="loading" :data="[item]">
          <el-table-column type="index" label="序号" width="55" align="left">
<!--            <template #default="scope">-->
<!--              <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>-->
<!--            </template>-->
          </el-table-column>
          <el-table-column align="left" label="编号" prop="code" />
          <el-table-column align="left" label="名称" prop="name" />
          <el-table-column align="left" label="类型" prop="name" />
          <el-table-column align="left" label="维保单位" prop="name" />
          <el-table-column align="left" label="任务指标" prop="name">
            <template #default="scope">
              <span @click="onpop" style="cursor: pointer;color: #1f69e8">{{scope.row.name}}</span>
<!--            <el-button text type="primary" ></el-button>-->
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    </div>
    <Dialog v-model="dialogVisible" width="700" title="查看指标">
      <el-table ref="myTable" :max-height="230" v-loading="loading" :data="indexList">
        <el-table-column type="index" label="序号" width="55" align="left">
          <!--            <template #default="scope">-->
          <!--              <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>-->
          <!--            </template>-->
        </el-table-column>
        <el-table-column align="left" label="指标内容" prop="name" />
        <el-table-column align="left" label="检查结果" prop="name">
          <template #default="scope">
            <el-radio-group v-model="scope.row.radio1">
              <el-radio value="1" size="large">是</el-radio>
              <el-radio value="2" size="large">否</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div style="display: flex;align-items: center;justify-content: center;">
<!--          <XButton title="保存" width="200px" gradient  @click="onParameter" />-->
          <el-button style="width: 200px;" @click="dialogVisible=false">关闭</el-button>
        </div>
      </template>
    </Dialog>

  </div>
</template>

<script setup lang="ts">

import { reactive,toRefs,onBeforeMount,onMounted,watch,ref} from 'vue'
import {  ArrowLeft,ArrowRight,CirclePlus,Search,MessageBox,Position} from '@element-plus/icons-vue'
import {useRouter,useRoute} from "vue-router";
import { ElMessage } from 'element-plus'
const { t } = useI18n() // 国际化

const router = useRouter();
const routers = useRoute();
let id= ref('')
const activeName = ref('first');
const dialogVisible = ref(false);
const loading = ref(false);
const formData = ref({});
const indexList = ref([]);
const list = ref([
  {code:'001',name:'XXXXXXXXX设备1'},
  {code:'002',name:'XXXXXXXXX设备2'},
  {code:'003',name:'XXXXXXXXX设备3'},
  {code:'004',name:'XXXXXXXXX设备4'},
  {code:'005',name:'XXXXXXXXX设备5'},
  {code:'006',name:'XXXXXXXXX设备6'}
]);
const expandedItems = ref(Array(list.value.length).fill(false)); // 初始化所有项为关闭状态

const toggleExpand = (index) => {
  // 关闭其他所有展开的项
  expandedItems.value = expandedItems.value.map((_, i) => i === index ? !expandedItems.value[i] : false);
};


const goback = () =>{
  router.go(-1)
}

//表单显示
const displayform = () => {
  dialogVisible.value=true
}

//查看指标
const onpop=()=>{
  dialogVisible.value=true
}





const onParameter=()=>{
  dialogVisible.value=false

}

onBeforeMount(() => {

})
onMounted(() => {
  // getLog(routers.query.id)
  // getdeviceImg(routers.query.id)
})
</script>
<style lang="scss" scoped>
.box{
  background: #fff;
}
.detail-content {
  overflow: hidden;
  background: #fff;
  .content{
    margin-top: 10px;
    padding: 8px 16px;
    background: #F5F6F8;
    .content-head{
      display: flex;
      align-items: center;
      img{
        width: 24px;
        height: 24px;
      }
      .textStyle{
        margin-left: 10px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 14px;
        color: #3B3C3D;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    .tableStyle{
      background: #ffffff;
      padding: 15px;
      margin: 10px 0 10px 10px;
    }
  }
}



.gradient-btn {
  background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
  border: none !important;
  font-size: 14px !important;
  padding: 9px 16px !important;
  color: #ffffff !important;

  &:hover {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.8;
  }

  &:active {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.9;
  }

  // 覆盖 element-plus 的默认样式
  &.el-button--primary {
    --el-button-hover-bg-color: transparent;
    --el-button-active-bg-color: transparent;
  }
}

/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
