<template>

  <div class="box">
    <!-- 主体内容区 -->
    <div class="detail-content">
      <el-table ref="myTable" row-key="id" :tree-props="{children: 'children', hasChildren: 'hasChildren'}" :max-height="500" v-loading="loading" :data="list">
        <el-table-column align="left" label="任务编号" prop="code" />
        <el-table-column align="left" label="任务名称" prop="name" />
        <el-table-column align="left" label="开始时间" prop="name" />
        <el-table-column align="left" label="截止时间" prop="name" />
        <el-table-column align="left" label="维保单位" prop="name"/>
        <el-table-column align="left" label="任务状态" prop="name"/>
        <el-table-column align="left" width="150" label="操作">
          <template #default="scope">
            <span @click="onpop" v-if="scope.row" style="cursor: pointer;color: #1f69e8">查看指标</span>
            <span @click="onFormpop" style="cursor: pointer;color: #1f69e8;margin-left: 10px;">查看表单</span>

          </template>
        </el-table-column>
      </el-table>

    </div>

    <Dialog v-model="dialogVisible" width="900" title="查看指标">
      <el-radio-group
        v-model="popparms.radio2"
        text-color="#0053DF"
        fill="rgb(239, 240, 253)"
      >
        <el-radio-button label="全部" value="1" />
        <el-radio-button label="已完成" value="Washington" />
        <el-radio-button label="未完成" value="Los Angeles" />
      </el-radio-group>
      <el-table ref="myTable" style="margin-top: 15px;" :max-height="230" v-loading="loading" :data="indexList">
        <el-table-column type="index" label="序号" width="55" align="left"/>
          <!--            <template #default="scope">-->
          <!--              <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>-->
          <!--            </template>-->
<!--        </el-table-column>-->
        <el-table-column align="left" label="任务对象" prop="name" />
        <el-table-column align="left" label="维保单位" prop="name"/>
        <el-table-column align="left" label="提交人" prop="name"/>
        <el-table-column align="left" label="提交时间" prop="name"/>
        <el-table-column align="left" label="状态" prop="name">
          <template #default="scope">
                <span v-if="scope.row">已完成</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="任务指标" prop="name">
          <template #default="scope">
            <span @click="IndicatorDetails" v-if="scope.row" style="cursor: pointer;color: #1f69e8">查看</span>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div style="display: flex;align-items: center;justify-content: center;">
          <el-button style="width: 200px;" @click="dialogVisible=false">关闭</el-button>
        </div>
      </template>
    </Dialog>


<!--    查看详情-->
    <Dialog v-model="detailsPop" width="900" title="查看详情">
     <div style="display: flex;align-items: center;">
       <div>检查结果:</div>
       <div style="margin-left: 20px;">  <el-button size="small" type="danger">异常</el-button> </div>
     </div>
     <div style="display: flex;align-items: center;margin-top: 15px;">
       <div>检查统计:</div>
       <div style="margin-left: 20px;">共计检查 40 项，其中异常 3 项   正常 37 项 </div>
     </div>
      <el-table ref="myTable" style="margin-top: 15px;" :max-height="230" v-loading="loading" :data="indexList">
        <el-table-column align="left" label="指标内容" prop="name" />
        <el-table-column align="left" label="检查结果" prop="name"/>
        <el-table-column align="left" label="附件" prop="name"/>
        <el-table-column align="left" label="说明" prop="name"/>
<!--        <el-table-column align="left" label="状态" prop="name">-->
<!--          <template #default="scope">-->
<!--                <span v-if="scope.row">已完成</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column align="left" label="任务指标" prop="name">-->
<!--          <template #default="scope">-->
<!--            <span @click="onpop" v-if="scope.row" style="cursor: pointer;color: #1f69e8">查看</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
      </el-table>
      <template #footer>
        <div style="display: flex;align-items: center;justify-content: center;">
          <el-button style="width: 200px;" @click="detailsPop=false">关闭</el-button>
        </div>
      </template>
    </Dialog>

<!--    表单查看-->
    <Dialog v-model="formdialogVisible" width="700" title="表单">
      <div>表单预览</div>
      <template #footer>
        <div style="display: flex;align-items: center;justify-content: center;">
          <XButton title="保存" width="200px" gradient  @click="onParameter" />
          <!--        <el-button :disabled="formLoading" style="width: 200px;" type="primary">确定</el-button>-->
          <el-button style="width: 200px;" @click="formdialogVisible=false">关闭</el-button>
        </div>
      </template>
    </Dialog>

  </div>
</template>

<script setup lang="ts">

import { reactive,toRefs,onBeforeMount,onMounted,watch,ref} from 'vue'
import {  ArrowLeft,ArrowRight,CirclePlus,Search,MessageBox,Position} from '@element-plus/icons-vue'
import {useRouter,useRoute} from "vue-router";
import { ElMessage } from 'element-plus'
const { t } = useI18n() // 国际化

const router = useRouter();
const routers = useRoute();
let id= ref('')
const activeName = ref('first');
const dialogVisible = ref(false);
const detailsPop = ref(false);
const formdialogVisible = ref(false);
const popparms=ref({})
const loading = ref(false);
const formData = ref({});
const indexList = ref([{name:'测试'}]);
const list = ref([
  {id:'1', code:'001',name:'XXXXXXXXX设备1',children:[{id:'2',code:'002',name:'XXXXXXXXX设备2'},]},
  {id:'3',code:'002',name:'XXXXXXXXX设备2'},
  {id:'4',code:'003',name:'XXXXXXXXX设备3'},
  {id:'5',code:'004',name:'XXXXXXXXX设备4'},
  {id:'6',code:'005',name:'XXXXXXXXX设备5'},
  {id:'7',code:'006',name:'XXXXXXXXX设备6'}
]);
const expandedItems = ref(Array(list.value.length).fill(false)); // 初始化所有项为关闭状态

const toggleExpand = (index) => {
  // 关闭其他所有展开的项
  expandedItems.value = expandedItems.value.map((_, i) => i === index ? !expandedItems.value[i] : false);
};


const goback = () =>{
  router.go(-1)
}

//表单显示
const displayform = () => {
  dialogVisible.value=true
}

//查看指标
const onpop=()=>{
  dialogVisible.value=true
}
//查看表单
const onFormpop=()=>{
  formdialogVisible.value=true
}


//查看指标项
const IndicatorDetails=()=>{
  detailsPop.value=true
}





const onParameter=()=>{
  dialogVisible.value=false

}

onBeforeMount(() => {

})
onMounted(() => {
  // getLog(routers.query.id)
  // getdeviceImg(routers.query.id)
})
</script>
<style lang="scss" scoped>
.box{
  background: #fff;
}
.detail-content {
  overflow: hidden;
  background: #fff;
  .content{
    margin-top: 10px;
    padding: 8px 16px;
    background: #F5F6F8;
    .content-head{
      display: flex;
      align-items: center;
      img{
        width: 24px;
        height: 24px;
      }
      .textStyle{
        margin-left: 10px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 14px;
        color: #3B3C3D;
        line-height: 19px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
    .tableStyle{
      background: #ffffff;
      padding: 15px;
      margin: 10px 0 10px 10px;
    }
  }
}



.gradient-btn {
  background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
  border: none !important;
  font-size: 14px !important;
  padding: 9px 16px !important;
  color: #ffffff !important;

  &:hover {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.8;
  }

  &:active {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.9;
  }

  // 覆盖 element-plus 的默认样式
  &.el-button--primary {
    --el-button-hover-bg-color: transparent;
    --el-button-active-bg-color: transparent;
  }
}

/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
