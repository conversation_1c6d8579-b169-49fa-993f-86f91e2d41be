<template>

  <div class="box">
    <!-- 头部导航 -->
    <header class="header">
      <div class="header-left">
        <el-button type="info" :icon="ArrowLeft" @click="goback()" plain>返回</el-button>
        <div class="title">计划列表-创建计划</div>
      </div>
      <div class="header-right">
      </div>
    </header>

    <!-- 主体内容区 -->
    <div class="content-wrapper">
      <!-- 左侧详情 -->
      <div class="content-left">
        <!-- 辅件详情 -->
        <div class="detail-card" >
          <div class="card-header">
            <span class="header-bar"></span>
            <span>基础信息</span>
          </div>
          <div class="detail-content">
            <el-form
              ref="ruleFormRef"
              style="max-width: 700px"
              :model="ruleForm"
              :rules="rules"
              label-width="auto"
            >
              <el-form-item label="计划名称" prop="name">
                <el-input v-model="ruleForm.name" />
              </el-form-item>
              <el-form-item label="起止时间" prop="region">
                <el-date-picker
                  v-model="ruleForm.value2"
                  type="datetimerange"
                  start-placeholder="请选择开始日期时间"
                  end-placeholder="请选择结束日期时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  date-format="YYYY/MM/DD ddd"
                  time-format="A hh:mm:ss"
                />
              </el-form-item>
              <el-form-item label="计划描述" prop="name">
                <el-input
                  v-model="ruleForm.textarea"
                  maxlength="200"
                  size="large"
                  placeholder="请输入内容"
                  show-word-limit
                  type="textarea"
                />
              </el-form-item>
              <el-form-item label="添加附件" prop="name">
                <el-upload
                  v-model:file-list="ruleForm.fileList"
                  class="upload-demo"
                  action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
                  multiple
                  :on-preview="handlePreview"
                  :on-remove="handleRemove"
                  :before-remove="beforeRemove"
                  :limit="3"
                  :on-exceed="handleExceed"
                >
                  <el-button :icon="CirclePlus" >添加附件</el-button>
                  <template #tip>
                    <div class="el-upload__tip">
<!--                      jpg/png files with a size less than 500KB.-->
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <div class="detail-card">
          <div class="card-header">
            <span class="header-bar"></span>
            <span>计划频率</span>
          </div>
          <div class="detail-content">
            <el-form
              ref="ruleFormRef"
              style="max-width: 700px"
              :model="PlannedFrequency"
              :rules="rules"
              label-width="auto"
            >
              <el-form-item label="计划频率" prop="name">
                <el-select
                  v-model="PlannedFrequency.value"
                  @change="planChange($event)"
                  placeholder="请选择"
                  style="width: 240px"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="设置频率" v-if="PlannedFrequency.value!='2' && PlannedFrequency.value" prop="region">
                <el-button :icon="CirclePlus" @click="AddFrequency">添加频率</el-button>
              </el-form-item>
              <el-form-item v-if="PlannedFrequency.value!='2' && PlannedFrequency.value" label="     " prop="name">
              <template #default>
                <div>
                  <div>
                    <el-scrollbar v-if="PlannedFrequency.value=='3' || PlannedFrequency.value=='4'" max-height="350px" >
                    <div style="display: flex;align-items: center;margin-top: 5px;" v-for="(itm,index) in planList" :key="index">
                    <div>
                      <el-select
                        v-model="planList[index].start"
                        placeholder="请选择"
                        @change="TimeStart(index,$event)"
                        style="width: 240px"
                      >
                        <el-option
                          v-for="item in WeekSelector"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </div>
                      <div style="margin: 0 10px;">至</div>
                      <div>
                        <el-select
                          v-model="planList[index].end"
                          placeholder="请选择"
                          @change="TimeEnd(index,$event)"
                          style="width: 240px"
                        >
                          <el-option
                            v-for="item in WeekSelector"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </div>
                      <div> <el-button text @click="planDele(index)" type="primary">删除</el-button> </div>
                    </div>
                    </el-scrollbar>
                    <el-scrollbar v-if="PlannedFrequency.value=='1' || PlannedFrequency.value=='5'" max-height="350px" >
                    <div style="display: flex;align-items: center;margin-top: 5px;" v-for="(itm,index) in planList" :key="index">
                    <div>
                      <el-date-picker
                        v-model="planList[index].start"
                        placeholder="请选择"
                        :clearable="false"
                        @change="PickerStart(index,$event)"
                        type="date"
                        format="YYYY/MM/DD"
                        value-format="YYYY-MM-DD"
                      />
                    </div>
                      <div style="margin: 0 10px;">至</div>
                      <div>
                        <el-date-picker
                          v-model="planList[index].end"
                          @change="PickerEnd(index,$event)"
                          :clearable="false"
                          placeholder="请选择"
                          type="date"
                          format="YYYY/MM/DD"
                          value-format="YYYY-MM-DD"
                        />
                      </div>
                      <div> <el-button text @click="planDele(index)" type="primary">删除</el-button> </div>
                    </div>
                    </el-scrollbar>
                  </div>

                </div>
              </template>
              </el-form-item>
              <el-form-item label="派发时间" prop="name">
                <el-time-picker
                  v-model="PlannedFrequency.value2"
                  arrow-control
                  placeholder="请选择时间"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="detail-card">
          <div class="card-header">
            <span class="header-bar"></span>
            <span>维护信息</span>
          </div>
          <div style="display: flex;align-items:center;background: #F6F7F9;padding:10px;margin: 0 8px;">
            <div style="min-width: 78px;">选择对象:</div>
            <div style="border: 1px solid silver;width: 100%;height: 37px;margin-right: 10px;line-height: 30px;overflow: auto;">
              <el-tag style="margin-left: 5px;" v-for="itm in formData.value1" :key="itm.id" type="primary">{{itm.label}}</el-tag>
<!--              <el-tag style="margin-left: 5px;" v-for="itm in 30" :key="itm" type="primary">{{`这是测试数据${itm}`}}</el-tag>-->
            </div>
            <div ><el-button @click="querycriteria">
              选择 <el-icon class="el-icon--right"><ArrowRight /></el-icon>
            </el-button></div>
          </div>
          <div class="detail-content">

            <div  style="height: 400px;width: 100%; margin-top: 10px;display: flex;">
             <div style="flex: 1;margin-right: 8px;border: 1px solid #ECEDEE;padding:0 10px;">
               <div style="display: flex;justify-content: space-between;align-items: center;margin-top: 10px;">
                 <div>设备列表</div>
                 <div>
                   <el-input
                     v-model="input3"
                     style="max-width: 600px"
                     placeholder="请输入"
                     class="input-with-select"
                   >
                     <template #append>
                       <el-button :icon="Search" />
                     </template>
                   </el-input>
                 </div>
               </div>
               <el-table row-key="id" style="margin-top: 10px;"  class="devicesTable"  ref="tableRef" v-loading="devicesLoading" @selection-change="selectionChange" :data="deviceslist">
                 <el-table-column align="center" label="序号" type="index" prop="assetsCode" width="55" />
                 <el-table-column align="center" label="设备类型" prop="assetsCode">
                   <template #default="scope">
                     <el-button text type="primary" @click="ViewType" >{{scope.row.assetsCode}}</el-button>
                   </template>
                 </el-table-column>
                 <el-table-column align="center" label="任务指标" prop="assetsName">
                   <template #default="scope">
                     <el-button text type="primary" @click="ViewIndicators" >{{scope.row.assetsName}}</el-button>
                   </template>
                 </el-table-column>
                 <el-table-column align="center" type="selection"  :reserve-selection="true" width="55" />
               </el-table>
               <el-pagination :page-size="5" style="margin-top: 10px;display: flex;justify-content: right"  layout="prev, pager, next" :total="50" />
             </div>
              <div style="flex: 1;margin-left: 8px;border: 1px solid #ECEDEE;padding:0 10px;">
                <div style="display: flex;justify-content: space-between;align-items: center;margin-top: 10px;">
                  <div>辅件列表</div>
                  <div>
                    <el-input
                      v-model="input3"
                      style="max-width: 600px"
                      placeholder="请输入"
                      class="input-with-select"
                    >
                      <template #append>
                        <el-button :icon="Search" />
                      </template>
                    </el-input>
                  </div>
                </div>
                <el-table row-key="id" style="margin-top: 10px;"  class="devicesTable"  ref="tableRef" v-loading="devicesLoading" @selection-change="selectionChange" :data="deviceslist">
                  <el-table-column align="center" label="序号" type="index" prop="assetsCode" width="55" />
                  <el-table-column align="center" label="设备类型" prop="assetsCode">
                    <template #default="scope">
                      <el-button text type="primary" >{{scope.row.assetsCode}}</el-button>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="任务指标" prop="assetsName">
                    <template #default="scope">
                      <el-button text type="primary" >{{scope.row.assetsName}}</el-button>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" type="selection"   :reserve-selection="true" width="55" />
                </el-table>
                <el-pagination :page-size="5" style="margin-top: 10px;display: flex;justify-content: right"  layout="prev, pager, next" :total="50" />
              </div>
            </div>
          </div>
        </div>
        <div class="detail-card">
          <div class="card-header">
            <span class="header-bar"></span>
            <span>任务表单</span>
          </div>
          <div class="detail-content" style="padding: 16px 0;">
            <div style="display: flex;align-items:center;background: #F6F7F9;padding:10px;margin: 0 8px;">
              <div style="min-width: 78px;">选择表单:</div>
              <div style="width: 50%;">
                <el-select
                  v-model="PlannedFrequency.vale"
                  style="width: 100%"
                  placeholder="请选择"
                  @change="TimeEnd($event)"
                >
                  <el-option
                    v-for="item in WeekSelector"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
            <div style="margin: 30px 0;display: flex;align-items: center;justify-content: center;">
              <el-button style="width: 150px;" >取消</el-button>
              <el-button style="width: 150px;" :icon="MessageBox" >暂存至草稿箱</el-button>
              <el-button style="width: 150px;" class="gradient-btn" :icon="Position" >确认提交</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Dialog v-model="dialogVisible" width="700" title="选择">
      <el-form
        ref="formRef"
        v-loading="formLoading"
        :model="formData"
        label-width="100px"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="按项目:" prop="assetsCode">
              <el-select
                v-model="formData.value1"
                multiple
                @change="changlist($event)"
                value-key="id"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in selectList"
                  :key="item.value"
                  :label="item.label"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="按区域:" prop="assetsCode">
              <el-input v-model="formData.assetsCode" placeholder="请输入设备编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="按站点:" prop="assetsCode">
              <el-input v-model="formData.assetsCode" placeholder="请输入设备编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="按分组:" prop="assetsCode">
              <el-input v-model="formData.assetsCode" placeholder="请输入设备编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="按标签:" prop="assetsCode">
              <el-input v-model="formData.assetsCode" placeholder="请输入设备编号" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div style="display: flex;align-items: center;justify-content: center;">
          <XButton title="保存" width="200px" gradient  @click="onParameter" />
          <el-button style="width: 200px;" @click="resetForm">关闭</el-button>
        </div>
      </template>
    </Dialog>

    <Dialog v-model="IndexdialogVisible" width="900" title="查看指标">
      <el-table row-key="id" style="margin-top: 10px;"  class="devicesTable"  ref="tableRef" v-loading="devicesLoading" @selection-change="selectionChange" :data="IndexList">
        <el-table-column align="center" type="selection"   :reserve-selection="true" width="55" />
        <el-table-column align="center" label="序号" type="index" prop="assetsCode" width="55" />
        <el-table-column align="center" label="指标内容" prop="assetsCode">
          <template #default="scope">
            <el-button text type="primary" >{{scope.row.assetsCode}}</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="检查结果" prop="assetsCode">
          <template #default="scope">
            <el-radio-group v-model="scope.row.radio1">
              <el-radio value="1" size="large">是</el-radio>
              <el-radio value="2" size="large">否</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column align="center" label="上传附件" prop="assetsName">
          <template #default="scope">
            <el-checkbox v-model="scope.row.assetsName" label="" size="large" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="备注说明" prop="assetsName">
          <template #default="scope">
            <el-checkbox v-model="scope.row.assetsName" label="" size="large" />
          </template>
        </el-table-column>

      </el-table>
      <template #footer>
        <div style="display: flex;align-items: center;justify-content: center;">
          <XButton title="保存" width="150px" gradient  @click="onParameter" />
          <el-button style="width: 150px;" @click="IndexdialogVisible=false">关闭</el-button>
        </div>
      </template>
    </Dialog>

    <Dialog v-model="devicedialogVisible" width="900" title="查看设备">
      <div class="devicespop">
        <div class="deviceleft">
          <el-input v-model="devicequery.kword" :suffix-icon="Search"/>
            <XButton title="查询" preIcon="ep:search" style="margin-left: 10px;" v-hasPermi="['infra:device-overview-info:query']" gradient @click="handleQuery" />
            <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
        </div>
        <div>已选择16项</div>
      </div>
      <el-table row-key="id" style="margin-top: 10px;" max-height="450px"  class="devicesTable"  ref="tableRef" v-loading="devicesLoading" @selection-change="selectionChange" :data="deviceList">
        <el-table-column align="center" label="序号" type="index" prop="assetsCode" width="55" />
        <el-table-column align="center" label="编号" prop="assetsCode"/>
        <el-table-column align="center" label="名称" prop="assetsCode"/>
        <el-table-column align="center" label="类型" prop="assetsCode"/>
        <el-table-column align="center" label="维保单位" prop="assetsCode"/>
        <el-table-column align="center" label="选择" prop="assetsName">
          <template #default="scope">
            <el-checkbox v-model="scope.row.assetsName" label="" size="large" />
          </template>
        </el-table-column>
      </el-table>
      <Pagination
        style="margin-top: 30px;"
        :total="devictotal"
        v-model:page="devicequery.pageNo"
        v-model:limit="devicequery.pageSize"
        @pagination="getList"
      />
      <template #footer>
        <div style="display: flex;align-items: center;justify-content: center;margin-top: 60px;">
          <XButton title="保存" width="150px" gradient  @click="onParameter" />
          <el-button style="width: 150px;" @click="devicedialogVisible=false">关闭</el-button>
        </div>
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">

import { reactive,toRefs,onBeforeMount,onMounted,watch,ref} from 'vue'
import {  ArrowLeft,ArrowRight,CirclePlus,Search,MessageBox,Position} from '@element-plus/icons-vue'
import {useRouter,useRoute} from "vue-router";
import { ElMessage } from 'element-plus'
const { t } = useI18n() // 国际化

const router = useRouter();
const routers = useRoute();
let id= ref('')
const activeName = ref('');
const dialogVisible = ref(false);
const IndexList = ref(false); //指标弹窗数据
const IndexdialogVisible = ref(false); //指标弹窗显示
const deviceList = ref(false); //设备弹窗数据
const devicedialogVisible = ref(false); //设备弹窗显示
const devicequery = ref({kword:''}); //设备弹窗查询参数
const devictotal = ref(10); //设备弹窗页码
const pictureUrl = ref('');
const ruleForm = ref({});
const formData = ref({});
const PlannedFrequency = ref({});
let Template =ref([])
let deviceslist =ref([
  {
    assetsCode:'消防类-XX类--XX类(10)',
    assetsName:'x x指标(34）',
  },
  {
    assetsCode:'消防类-XX类--XX类(10)',
    assetsName:'x x指标(34）',
  },
  {
    assetsCode:'消防类-XX类--XX类(10)',
    assetsName:'x x指标(34）',
  },
  {
    assetsCode:'消防类-XX类--XX类(10)',
    assetsName:'x x指标(34）',
  },
  {
    assetsCode:'消防类-XX类--XX类(10)',
    assetsName:'x x指标(34）',
  },
])
let planList =ref([])  //计划频率数据
let options =ref([{label:'自定义',value:'1'},{label:'每天',value:'2'},{label:'每周',value:'3'},
  {label:'每月',value:'4'},{label:'每年',value:'5'},])
let WeekSelector =ref([])
let selectList=ref([{label:'xxxxx项目',value:'1',id:'1'},{label:'xxxxx项目2',value:'2',id:'2'}])
//返回
const goback = () =>{
  router.go(-1)
}

let customFieldsRef = ref()

//添加频率
const AddFrequency=()=>{
  if (PlannedFrequency.value.value){
    if (PlannedFrequency.value.value!='2'){
      if (planList.value.length>=1){
        // 检查是否所有已有项都填写了开始和结束时间
        const allValid = planList.value.every(item => item.start && item.end);
        if (allValid) {
          planList.value.push({
            pv: PlannedFrequency.value.value,
            start: '',
            end: '',
          });
        } else {
          ElMessage.warning('请先填写所有已有项的开始和结束时间');
        }
      } else {
        planList.value.push({
          pv: PlannedFrequency.value.value,
          start: '',
          end: '',
        });
      }
    }
  }else {
    ElMessage.warning('请先选择计划频率')
  }
}
//频率删除
const planDele =(index:string)=>{
  planList.value.splice(index,1);
}
//周选择开始
const TimeStart = (index: number, value: string) => {
  if (index > 0 && planList.value.length > index - 1) {
    const prevItem = planList.value[index - 1];
    console.log(value,prevItem.start,prevItem.end)
    if (value == prevItem.start || value <= prevItem.end) {
      ElMessage.warning('不能同频率且不能等于或小于上一个结束频率');
      planList.value[index].start = '';
    }
  }
}

//年自定义选择开始
const PickerStart = (index: number, value: string) => {
  console.log(value,)
  if (index > 0 && planList.value.length > index - 1) {
    const startDate = new Date(value);
    const endItem = new Date(planList.value[index - 1].end);
    const startItem = new Date(planList.value[index - 1].start);
    if (startDate == startItem || startDate <= endItem) {
      ElMessage.warning('不能同频率且不能等于或小于上一个结束频率');
      planList.value[index].start = '';
    }
  }
}
//年自定义选择开始结束
const PickerEnd = (index: number, value: string) => {
  const endDate = new Date(value);
  const startItem = new Date(planList.value[index].start);
  const prevItem = planList.value[index];
  if (prevItem.start) {
    if (endDate<startItem){
      ElMessage.warning('必须大于开始日期');
      planList.value[index].end = '';
    }
  }else {
    ElMessage.warning('请先选开始日期');
    planList.value[index].end = '';
  }
}
//周选择结束
const TimeEnd = (index: number, value: string) => {
  const prevItem = planList.value[index];
  if (prevItem.start) {
   if (value<prevItem.start){
     ElMessage.warning('必须大于开始时间');
     planList.value[index].end = '';
   }
  }else {
    ElMessage.warning('请先选开始时间');
    planList.value[index].end = '';
  }
}

//计划频率修改
const planChange=(value)=>{
  planList.value=[]
  if (value=='3' || value=='4'){
    generationdays(value)
  }
  console.log(value)
}

//查看指标
const ViewIndicators=()=>{
  IndexdialogVisible.value=true
}

//查看设备
const ViewType=()=>{
  devicedialogVisible.value=true
}

//循环生成月天数
const  generationdays=(value:string)=>{
  WeekSelector.value=[]
  if (value =='4'){
    for (let i = 1; i <= 31; i++){
      WeekSelector.value.push({label:`${i}号`,value:i})
    }
  }else {
    const weekDays = ['一', '二', '三', '四', '五', '六', '日'];
    for (let i = 0; i < 7; i++){
      WeekSelector.value.push({label:`周${weekDays[i]}`,value:i})
    }
  }
}

const querycriteria =()=>{
  dialogVisible.value = true
}

const changlist=(value)=>{
  console.log('ssssss',value)
}

const onParameter=()=>{

  dialogVisible.value=false
  // formData.value.value1.forEach(item=>{
  //
  // })
}

onBeforeMount(() => {

})
onMounted(() => {
  // getLog(routers.query.id)
  // getdeviceImg(routers.query.id)
})
</script>
<style lang="scss" scoped>
.box {
  display: flex;
  flex-direction: column;
  //height: 100vh;
  background: #f5f6f8;
  overflow: hidden;
}
.devicespop{
  display: flex;
  justify-content: space-between;
  .deviceleft{
    display: flex;
    align-items: center;
  }
}

.header {
  height: 48px;
  padding: 0 16px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);

  &-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .title {
    font-size: 16px;
    font-weight: 500;
  }
}

.content-wrapper {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 1px;
  min-height: 0; // 重要：防止内容溢出
  overflow: hidden;
}

.content-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  //gap: 2px;
  min-width: 0; // 重要：防止内容溢出
}



.detail-card {
  background: #fff;
  border-radius: 4px;
  //height: calc(80% - 8px);
  //display: flex;
  //flex-direction: column;
}

.card-header {
  padding: 10px;
  //border-bottom: 1px solid #eaedf2;
  display: flex;
  align-items: center;
  gap: 8px;
  span{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 14px;
    color: #3B3C3D;
    line-height: 21px;
    font-style: normal;
    text-transform: none;
  }
  .header-bar {
    width: 12px;
    height: 12px;
    background: #005FFF;
    border-radius: 10px ;
    //width: 3px;
    //height: 16px;
    //background: linear-gradient(180deg, #16B9FA 0%, #0058FF 100%);
    //border-radius: 2px;
  }
}

.detail-content {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}

.detail-item {
  background: #f2f2f2;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.detail-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.detail-col {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: flex-start;

  .label {
    min-width: 90px;
    color: #666;
  }

  .value {
    flex: 1;
    color: #333;
    word-break: break-all;
  }
}
.gradient-btn {
  background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
  border: none !important;
  font-size: 14px !important;
  padding: 9px 16px !important;
  color: #ffffff !important;

  &:hover {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.8;
  }

  &:active {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.9;
  }

  // 覆盖 element-plus 的默认样式
  &.el-button--primary {
    --el-button-hover-bg-color: transparent;
    --el-button-active-bg-color: transparent;
  }
}
// 响应式布局
@media screen and (max-width: 1024px) {
  .content-wrapper {
    flex-direction: column;
  }

  .content-right {
    width: 100%;
    height: 300px;
  }

  .detail-col {
    min-width: 100%;
  }
}
/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
