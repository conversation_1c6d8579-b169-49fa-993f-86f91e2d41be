<template>
  <ContentWrap class="h-1/1">
    <span class="TitleStyle">计划列表</span>
  </ContentWrap>
  <div class="mb-4">
    <div style="display: flex;" >
      <div class="navigation" @click="navigationChange(itm.key)" :class="itm.key == navigationsele ? navigationList[itm.key] : ''" v-for="itm in navigation" :key="itm.id">
        <img :src="getImagePath(itm.path)" alt=""/>
        <div>{{itm.label}}</div>
      </div>
    </div>
  </div>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="flex flex-wrap items-start -mb-15px"
    >
      <el-form-item label="" prop="name" class="!mr-3">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="输入计划编号查询"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="维保单位" prop="status" class="!mr-3">
        <el-select v-model="queryParams.status" class="!w-140px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开始时间" prop="status" class="!mr-3">
        <el-date-picker
          style="width: 240px;"
          v-model="queryParams.value2"
          @change="onchange"
          type="daterange"
          value-format="YYYY-MM-DD"
          unlink-panels
          range-separator="至"
          start-placeholder="开始"
          end-placeholder="结束"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="status" class="!mr-3">
        <el-date-picker
          style="width: 240px;"
          v-model="queryParams.value3"
          value-format="YYYY-MM-DD"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始"
          end-placeholder="结束"
        />
      </el-form-item>
      <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
        <div>
          <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:device-overview-info:query']" gradient @click="handleQuery" />
          <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
        </div>
        <div>
          <el-button
            v-hasPermi="['system:role:create']"
            plain
            @click="oneXamine()"
          >
            创建计划
          </el-button>
        </div>
      </div>

    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table ref="myTable" v-loading="loading" :data="list">
      <el-table-column type="index" label="序号" width="55" align="left">
        <template #default="scope">
          <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column align="left" label="计划编号" prop="code" />
      <el-table-column align="left" label="计划名称" prop="name" />
      <el-table-column align="left" label="计划开始时间" prop="name" />
      <el-table-column align="left" label="计划截止时间" prop="name" />
      <el-table-column align="left" label="计划频率" prop="name" />
      <el-table-column align="left" label="维保单位" prop="name" />
      <el-table-column align="left" label="计划进度" prop="name" />
      <el-table-column align="left" label="计划状态" prop="name" />
      <el-table-column align="left" label="创建时间" prop="name" />

      <el-table-column min-width="200" align="left" label="操作">
        <template #default="scope">
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openDetails(scope.row)"
          >
            详情
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openDetails(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openrelease(scope.row)"
          >
            发布
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="discontinue(scope.row)"
          >
            中止
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openDetails(scope.row)"
          >
            重新发布
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="restore(scope.row)"
          >
            恢复
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <!-- 审批弹窗 -->
  <Dialog v-model="dialogVisible" width="1000" title="审批">
    <div class="examine">
      <div>设备编号: <span>xxxxxx</span></div>
      <div>设备名称: <span>xxxxxx</span></div>
      <div>申请人: <span style="margin-left: 50px;">研发部-张三</span></div>
      <div>申请说明: <span>xxxxxx</span></div>
    </div>

    <div>
      <el-form :model="examineForm" label-width="auto" style="margin-top: 30px;">

        <el-form-item label="报废申请审批">
          <el-select v-model="examineForm.region" placeholder="请选择" >
            <el-option label="通过" value="shanghai" />
            <el-option label="驳回" value="beijing" />
          </el-select>
        </el-form-item>
        <el-form-item label="审批说明">
          <el-input v-model="examineForm.name" :rows="4" show-word-limit maxlength="30" type="textarea" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <el-button  style="width: 200px;" type="primary" >确定</el-button>
        <el-button style="width: 200px;" @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>
<!--  发布弹窗-->
  <Dialog v-model="releaseVisible" width="480" title="提示">
    <div>
      <div class="popStyle"><img  src="@/assets/imgs/maintenances/tishi.png"/></div>
      <div class="titleStyle">确认发布？</div>
      <div class="prompt">任务过程中存在未排班情况，请调整排班人员后发布</div>
      <div class="prompt"> 吴兴区未安排排班人员，请调整排班人员后发布</div>
    </div>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <XButton title="确认" width="200px" gradient  @click="onParameter" />
        <el-button style="width: 200px;" @click="releaseVisible=false">关闭</el-button>
      </div>
    </template>
  </Dialog>

  <!--  删除发布中止弹窗-->
  <Dialog v-model="deleteVisible" width="480" :title="title">
    <div>
      <div class="popStyle"><img  src="@/assets/imgs/maintenances/tishi.png"/></div>
      <div class="titleStyle">{{deleteboj.label}}</div>
      <div class="prompt">{{deleteboj.value}}</div>
    </div>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <XButton title="确认" width="200px" gradient  @click="onParameter" />
        <el-button style="width: 200px;" @click="deleteVisible=false">关闭</el-button>
      </div>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { useRouter } from "vue-router";
import * as RoleApi from '@/api/system/role'

import {modifyStatus} from "@/api/system/role";

defineOptions({ name: 'PluginList' })

const message = useMessage() // 消息弹窗
const navigationsele =ref('quanbu') // 标题切换
const title =ref('') // 标题切换
const { t } = useI18n() // 国际化
const router = useRouter() // vue-router
const loading = ref(true) // 列表的加载中
const devicesLoading = ref(false) // 列表的加载中
const dialogVisible = ref(false) //  关联设备弹窗的显示隐藏
const releaseVisible = ref(false) //  发布弹窗的显示隐藏
const deleteVisible = ref(false) //  发布弹窗的显示隐藏
const total = ref(0) // 列表的总页数
const examineForm = ref({}) // 列表的总页数
const deleteboj = ref({label:'',value:''}) // 列表的总页数
const navigationList = ref({quanbu:'bager',daifabu:'dafabu',progress:'jinxinz',yizhongzhi:'yizhongzhi',yijieshu:'yijeishu'}) // 列表的总页数
const list = ref([{code:'241219-0001',name:'监控设施例行检查',}]) // 列表的数据
const deviceslist = ref([{code:'ssssss11',name:'杆件'}]) // 列表的数据
const selectionList = ref([]) // 列表选中的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined,
  createTime: []
})
const devicesParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const navigation=[{
  id:'1',
  label:'全部(48)',
  key:'quanbu',
  path:'quanbu.png',
},{
  id:'2',
  label:'待发布(12)',
  key:'daifabu',
  path:'daifabu.png',
},
  {
    id:'3',
    label:'进行中(12)',
    key:'progress',
    path:'progress.png',
  },
  {
    id:'4',
    label:'已终止(12)',
    key:'yizhongzhi',
    path:'yizhongzhi.png',
  },
  {
    id:'5',
    label:'已结束(12)',
    key:'yijieshu',
    path:'yijieshu.png',
  },
]




const onchange=(val)=>{
  console.log(val)
}

const getImagePath=(path)=> {
  return new URL(`/src/assets/imgs/maintenances/${path}`, import.meta.url).href
}

const navigationChange=(value:string)=>{
  navigationsele.value=value
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 新增按钮操作 */
const openAdd = () => {
  router.push('AccessoriesAdd')
}

// 发布按钮
const openrelease=()=>{
  releaseVisible.value=true
}



/** 详情按钮操作 */
const openDetails = (value:number) => {
  router.push('PlanDetails')
}

/** 审批按钮 */
const oneXamine = () => {
  router.push('CreatePlan')
  // dialogVisible.value = true
}



/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}


const myTable=ref()

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  title.value='删除'
  deleteboj.value.label='确认删除？'
  deleteboj.value.value='确认删除后，不可恢复，是否删除'
  deleteVisible.value=true

}
/** 中止按钮操作 */
const discontinue = async (id: number) => {
  title.value='中止'
  deleteboj.value.label='确认中止？'
  deleteboj.value.value='是否确认中止暂停对任务的执行，中止后可恢复对任务的执行'
  deleteVisible.value=true

}
/** 中止按钮操作 */
const restore = async (id: number) => {
  title.value='恢复'
  deleteboj.value.label='确认恢复？'
  deleteboj.value.value='是否确认恢复任务，恢复后可以继续执行任务'
  deleteVisible.value=true

}



//单个状态修改
const SingleState = async (type: string,row:any) => {
  console.log('sssss',row)
  let parms={...row,status:type== 'Disable'? '1' : '0'}
  try {
    // 操作的二次确认
    await message.confirm('你确定要操作该角色状态吗？')
    // 发起删除
    await modifyStatus([ parms])
    message.success('操作成功！')
    // 刷新列表
    await getList()
  } catch {

  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // const data = await PostApi.getList(deptName.value)
    // console.log('排班管理',data)
    // list.value = data
    // total.value = data.total
  } finally {
    loading.value = false
  }
}


/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
<style scoped>
.examine{
  padding: 20px;
  background: rgb(242 242 242);
  div{
    margin-top: 15px;
    span{
      margin-left: 40px;
    }
  }
}
.navigation{
  display: flex;
  align-items: center;
  border: 1px solid #F4F6F8;
  //margin-left: 10px;
  cursor: pointer;
  width: 160px;
  padding:2px  0;
  background: #fff;
  img{
    width: 28px;
    height: 28px;
    border-radius: 0px 0px 0px 0px;
  }
  div{
    font-size: 14px;
    color: #545658;
    line-height: 19px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.navigation:hover{
  //border: 1px solid royalblue;
}
.popStyle{
  display: flex;
  align-items: center;
  justify-content: center;
  img{
    width: 80px;
    height: 80px;
    margin-right: 13px;
  }
}
.titleStyle{
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 18px;
  color: #3B3C3D;
  line-height: 24px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.prompt{
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #545658;
  line-height: 24px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.bager{
  background: linear-gradient( 180deg, #0053DF 0%, #005FFF 100%);
  div{
    color: #fff;
  }
}
.dafabu{
  background: linear-gradient( 360deg, #FF6B16 0%, #F54900 100%);
  div{
    color: #fff;
  }
}
.jinxinz{
  background: linear-gradient( 180deg, #0081FF 0%, #13A1FF 100%);
  div{
    color: #fff;
  }
}
.yizhongzhi{
  background: linear-gradient( 180deg, #EE1445 0%, #F53877 100%);
  div{
    color: #fff;
  }
}
.yijeishu{
  background: linear-gradient( 180deg, #395A91 0%, #6A87B7 100%);
  div{
    color: #fff;
  }
}
</style>
