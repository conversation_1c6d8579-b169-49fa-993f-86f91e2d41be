<template>
  <ContentWrap class="h-1/1">
    <span class="TitleStyle">待处理</span>
  </ContentWrap>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="flex flex-wrap items-start -mb-15px"
    >
      <el-form-item label="" prop="name" class="!mr-3">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="输入计划编号查询"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="计划名称" prop="name" class="!mr-3">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="请输入"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="维保单位" prop="status" class="!mr-3">
        <el-select v-model="queryParams.status" class="!w-140px" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开始时间" prop="status" class="!mr-3">
        <el-date-picker
          style="width: 240px;"
          v-model="queryParams.value2"
          @change="onchange"
          type="daterange"
          value-format="YYYY-MM-DD"
          unlink-panels
          range-separator="至"
          start-placeholder="开始"
          end-placeholder="结束"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="status" class="!mr-3">
        <el-date-picker
          style="width: 240px;"
          v-model="queryParams.value3"
          value-format="YYYY-MM-DD"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始"
          end-placeholder="结束"
        />
      </el-form-item>
      <div >
        <div>
          <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:device-overview-info:query']" gradient @click="handleQuery" />
          <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
        </div>
      </div>

    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table ref="myTable" v-loading="loading" :data="list">
      <el-table-column type="index" label="序号" width="55" align="left">
        <template #default="scope">
          <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column align="left" label="任务编号" prop="code"/>
      <el-table-column align="left" label="任务名称" prop="name"/>
      <el-table-column align="left" label="所属计划" prop="name"/>
      <el-table-column align="left" label="任务开始时间" prop="name"/>
      <el-table-column align="left" label="任务截止时间" prop="name"/>
      <el-table-column align="left" label="维保单位" prop="name"/>
      <el-table-column align="left" label="处理人" prop="name"/>
      <el-table-column :width="120" align="left" label="操作">
        <template #default="scope">
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openDetails(scope.row)"
          >
            详情
          </el-button>
          <el-button
            link
            preIcon="ep:basketball"
            title="功能权限"
            type="primary"
            @click="openhandle(scope.row)"
          >
            处理
          </el-button>

        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <!-- 审批弹窗 -->
  <Dialog v-model="dialogVisible" width="1000" title="任务处理">
    <div class="handlepop">
      <div class="handlepopLeft">
        <el-input
          v-model="input3"
          style="max-width: 600px"
          placeholder="请输入"
          class="input-with-select"
        >
          <template #append>
            <el-button :icon="Search" />
          </template>
        </el-input>
<!--        style="background: rgb(241 243 246);"-->
        <el-tree
          style="margin-top: 10px;"
          ref="treeRef"
          :data="deptList"
          :filter-node-method="filterNode"
          :expand-on-click-node="true"
          :props="defaultProps"
          default-expand-all
          highlight-current
          :current-node-key="currentNodeKey"
          node-key="value"
          @node-click="handleNodeClick"
        >
          <template #default="{ node, }">
        <div class="custom-tree-node" style="display: flex;align-items: center; justify-content: space-between; width: 100%;">
          <div style="display: flex; align-items: center;">
            <img v-if="node.data.paod =='0'" style="width: 23px;height: 23px;" src="@/assets/imgs/maintenances/zhandian.png"/>
            <img v-else style="width: 23px;height: 23px;" src="@/assets/imgs/maintenances/shebei.png"/>
            <span style="margin-left: 8px;">{{ node.label}}</span>
          </div>
          <div v-if="node.data.paod !='0'" >
            <div style="display: flex; align-items: center;">
              <div v-if="node.data.is =='1'" style="width: 9px;height: 9px;border-radius: 9px;background: red;"></div>
              <div v-else style="width: 9px;height: 9px;border-radius: 9px;background: #3BCC70;"></div>
              <div class="customTtree">正常</div>
            </div>

          </div>
        </div>
          </template>
        </el-tree>

      </div>
      <div class="handlepopRight">
        <el-radio-group
          v-model="examineForm.radio2"
          text-color="#0053DF"
          fill="rgb(239, 240, 253)"
        >
          <el-radio-button label="检查指标" value="1" />
          <el-radio-button label="任务表单" value="Washington" />
        </el-radio-group>
        <el-table ref="myTable" style="margin-top: 15px;" :max-height="230" v-loading="loading" :data="indexList">
          <el-table-column type="index" label="序号" width="55" align="left"/>
          <!--            <template #default="scope">-->
          <!--              <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>-->
          <!--            </template>-->
          <!--        </el-table-column>-->
          <el-table-column align="left" label="指标内容" prop="name" />
          <el-table-column align="left" label="检查结果" prop="name">
            <template #default="scope">
              <el-radio-group v-model="scope.row.radio1">
                <el-radio value="1" size="large">是</el-radio>
                <el-radio value="2" size="large">否</el-radio>
              </el-radio-group>
            </template>
          </el-table-column>
          <el-table-column align="left" label="上传附件" prop="name">
            <el-upload
              v-model:file-list="fileList"
              class="upload-demo"
              action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
              multiple
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :limit="3"
              :on-exceed="handleExceed"
            >
              <el-button text >上传附件</el-button>
            </el-upload>
          </el-table-column>
          <el-table-column align="left" label="备注说明" prop="name">
            <template #default="scope">
            <el-input v-model="scope.row.input"/>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <template #footer>
      <div style="display: flex;align-items: center;justify-content: center;">
        <el-button style="width: 100px;" @click="dialogVisible=false" >取消</el-button>
        <el-button style="width: 100px;" :icon="MessageBox">保存</el-button>
        <el-button style="width: 100px;" class="gradient-btn" :icon="Position" >提交</el-button>
      </div>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { useRouter } from "vue-router";
import {modifyStatus} from "@/api/system/role";
import {MessageBox, Position, Search} from "@element-plus/icons-vue";
import {ElTree} from "element-plus";
defineOptions({ name: 'PluginList' })
const defaultProps = {
  children: 'children',
  label: 'name',
  value: 'id',
  // isLeaf: 'leaf',
  paod:'paod',
  emitPath: false,// 用于 cascader 组件：在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值
  isLeaf: (data) => !data.children || data.children.length === 0, // 无 children 即为叶子节点
}
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // vue-router
const loading = ref(true) // 列表的加载中
const devicesLoading = ref(false) // 列表的加载中
const dialogVisible = ref(false) //  关联设备弹窗的显示隐藏
const total = ref(0) // 列表的总页数
const examineForm = ref({}) // 列表的总页数
const list = ref([{code:'111222',name:'测试'}]) // 列表的数据
const deviceslist = ref([{code:'ssssss11',name:'杆件'}]) // 列表的数据
const selectionList = ref([]) // 选中的数据
const indexList = ref([{name:'112'}]) // 选中的数据
const fileList = ref([]) // 选中的数据
const deptList = ref([{
  name:'XXXXXX站点',
  value:'1',
  paod:'0',
  children:[{
    name:'设备-001',
    value:'2',
    is:'1',
    paod:'1',
  },{
    name:'设备-001',
    value:'3',
    paod:'1',
  }]
},{
  name:'XXXXXX站点2',
  value:'4',
  paod:'0',
  children:[{
    name:'设备-005',
    value:'5',
    paod:'4',
  },{
    name:'设备-006',
    value:'6',
    paod:'4',
  },
  ]
}
]) // 弹窗树形数据
const currentNodeKey = ref(null)
const deptName = ref('')
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined,
  createTime: []
})
const devicesParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: '',
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中


const onchange=(val)=>{
  console.log(val)
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 新增按钮操作 */
const openAdd = () => {
  router.push('TaskPendingDetails')
}

/** 详情按钮操作 */
const openDetails = (value:number) => {
  router.push('TaskPendingDetails')
}

/** 审批按钮 */
const oneXamine = () => {
  dialogVisible.value = true
}



/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}


const myTable=ref()
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  if (selectionList.value.length <1) {
    message.warning('请选择要删除的角色！')
  }else {
    const ids = selectionList.value.map((item) => item.id).join(',')
    try {
      // 删除的二次确认
      await message.delConfirm()
      // 发起删除
      // await RoleApi.deleteRole(ids)
      message.success(t('common.delSuccess'))
      // 刷新列表
      // await getList()
    } catch {
      myTable.value.clearSelection();
    }
  }
}



//单个状态修改
const SingleState = async (type: string,row:any) => {
  console.log('sssss',row)
  let parms={...row,status:type== 'Disable'? '1' : '0'}
  try {
    // 操作的二次确认
    await message.confirm('你确定要操作该角色状态吗？')
    // 发起删除
    await modifyStatus([ parms])
    message.success('操作成功！')
    // 刷新列表
    await getList()
  } catch {

  }
}

//处理弹窗
const openhandle=()=>{
  dialogVisible.value = true
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // const data = await PostApi.getList(deptName.value)
    // console.log('排班管理',data)
    // list.value = data
    // total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 处理部门被点击 */
const handleNodeClick = async (node: { [key: string]: any }) => {
  if (node.paod == '0') return
  currentNodeKey.value = node.value
  console.log('Tree node data:', node)
}

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}
const  treeRef=ref()
/** 监听deptName */
watch(deptName, (val) => {
  treeRef.value!.filter(val)
})

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
<style lang="scss" scoped>
.examine{
  padding: 20px;
  background: rgb(242 242 242);
  div{
    margin-top: 15px;
    span{
      margin-left: 40px;
    }
  }
}
.gradient-btn {
  background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
  border: none !important;
  font-size: 14px !important;
  padding: 9px 16px !important;
  color: #ffffff !important;

  &:hover {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.8;
  }

  &:active {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.9;
  }

  // 覆盖 element-plus 的默认样式
  &.el-button--primary {
    --el-button-hover-bg-color: transparent;
    --el-button-active-bg-color: transparent;
  }
}
.handlepop{
  display: flex;
  align-items: center;
  .handlepopLeft{
    width: 350px;
    height: 500px;
    padding: 10px;
    border: 1px solid #DBDEE5;
    overflow: auto;
  }
  .handlepopRight{
    margin-left: 8px;
    width: 640px;
    height: 500px;
    padding: 10px;
    border: 1px solid #DBDEE5;
    overflow: auto;
  }
}
.customTtree{
  padding-right: 3px;
  font-weight: 400;
  font-size: 14px;
  color: #6B727E;
  line-height: 14px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
/* 自定义滚动条样式 */
.handlepopLeft::-webkit-scrollbar {
  width: 6px;
}

.handlepopLeft::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.handlepopLeft::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.handlepopLeft::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}


</style>
