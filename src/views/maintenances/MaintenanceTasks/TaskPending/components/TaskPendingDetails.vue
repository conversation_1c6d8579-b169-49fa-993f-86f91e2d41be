<template>

  <div class="box">
    <!-- 头部导航 -->
    <header class="header">
      <div class="header-left">
        <el-button type="info" :icon="ArrowLeft" @click="goback()" plain>返回</el-button>
        <div class="title">任务详情</div>
      </div>
      <div class="header-right">
      </div>
    </header>

    <!-- 主体内容区 -->
    <div class="content-wrapper">
      <!-- 左侧详情 -->
      <div class="content-left">
        <!-- 辅件详情 -->
        <div class="detail-card" >
          <div style="display: flex;align-items: center;justify-content: space-between;margin-top: 20px;">
            <div class="card-header">
              <span class="header-bar"></span>
              <span>监控设施例行检查241219-0001</span>
            </div>
            <div class="stateStyls">
              <img src="@/assets/imgs/maintenances/progress.png"/>
              <span>进行中</span>
            </div>
          </div>
          <div class="detail-content">

                <div class="BasicContent">
                  <div class="ContentStyle">
                    <div class="labelStyle">所属计划:</div>
                    <div class="numerical">监控设施例行检查</div>
                  </div>
                  <div class="ContentStyle" style="margin-left: 10px;">
                    <div class="labelStyle">任务起止时间:</div>
                    <div class="numerical">2024-12-20 - 2025-03-12</div>
                  </div>
<!--                  <div class="ContentStyle" style="margin-left: 10px;">-->
<!--                    <div class="labelStyle">任务表单:</div>-->
<!--                    <div class="numerical"  style="cursor: pointer;color: #005FFF;" @click="displayform" >每周周一，周二重复生成每周周一</div>-->
<!--                  </div>-->
                </div>
                <div class="BasicContent">
                  <div class="ContentStyle">
                    <div class="labelStyle">计划描述:</div>
                    <div class="numerical">检查消防通道堵塞、电动车违规充电、楼道照明缺失等问题，监测重点场所（如独居老人户、危房）安全隐患，记录并上报燃气泄漏、电路老化等紧急风险。要求每周完成至少2次网格化巡查，使
                      用APP上传问题并附照片或视频，紧急隐患各由有关部门需积极响应，确保问题在2个小时内等到响应处理。</div>
                  </div>
                  <div></div>
                </div>
                <div class="BasicContent">
                  <div class="ContentStyle">
                    <div class="labelStyle">附件:</div>
                    <div class="numerical" style="padding: 0;">
                      <div style="width: 100%;">
                        <div v-for="itm in 2" :key="itm" class="attachment">
                          <div>{{`社区安全隐患巡查${itm}.ppt`}}</div>
                          <div class="buttonStyle">
                            <div class="caozuo" style="margin-right: 35px;">
                              <img src="@/assets/imgs/maintenances/yulan.png"/>
                              预览
                            </div>
                            <div class="caozuo">
                              <img src="@/assets/imgs/maintenances/xiazai.png" style="margin-right: 1px;" />
                              下载
                            </div>
                          </div>
                        </div>
                      </div>


                    </div>
                  </div>
                  <div></div>
                </div>

          </div>
        </div>
        <div class="detail-card" style="margin-top: 15px;" >
          <div style="display: flex;align-items: center;justify-content: space-between;">
            <div class="card-header">
              <span class="header-bar"></span>
              <span>任务表单</span>
            </div>
            <div>
<!--              <img src="@/assets/imgs/maintenances/progress.png"/>-->
<!--              <span>进行中</span>-->
            </div>
          </div>
          <div class="detail-content" style="padding-top: 0;">

            <div class="BasicContent">
              <div class="ContentStyle">
                <div class="labelStyle">任务表单:</div>
                <div class="numerical"  style="cursor: pointer;color: #005FFF;" @click="displayform" >每周周一，周二重复生成每周周一</div>
              </div>
            </div>
          </div>
        </div>
        <div class="detail-card" style="margin-top: 15px;">
          <div style="display: flex;align-items: center;justify-content: space-between;">
            <div class="card-header">
              <span class="header-bar"></span>
              <span>任务内容</span>
            </div>
            <div>
<!--              <img src="@/assets/imgs/maintenances/progress.png"/>-->
<!--              <span>进行中</span>-->
            </div>
          </div>
          <div class="detail-content" style="padding-top: 0;">
            <div class="Task-content">
              <div>
                <el-radio-group
                v-model="queryParams.radio2"
                text-color="#0053DF"
                fill="rgb(239, 240, 253)"
              >
                <el-radio-button label="全部" value="1" />
                <el-radio-button label="已完成" value="Washington" />
                <el-radio-button label="未完成" value="Los Angeles" />
              </el-radio-group></div>
              <div class="statistics">已选对象：59个，已完成  41个；未完成  18个。涉及 12 个站点，其中包括 6 种设备类型、12 个设备；5 种辅件类型、8 个辅件</div>
            </div>
            <div>
              <el-table ref="myTable" style="margin-top: 15px;" :max-height="450" v-loading="loading" :data="indexList">
                <el-table-column align="left" type="index" label="序号" width="55"/>
                <el-table-column align="left" label="设备编号" prop="name" />
                <el-table-column align="left" label="设备名称" prop="name"/>
                <el-table-column align="left" label="所属站点" prop="name"/>
                <el-table-column align="left" label="维保单位" prop="name"/>
                <el-table-column align="left" label="提交人" prop="name"/>
                <el-table-column align="left" label="提交时间" prop="name"/>
                <el-table-column align="left" label="状态" prop="name"/>
                <el-table-column align="left" label="任务指标" prop="name"/>
                <!--        <el-table-column align="left" label="状态" prop="name">-->
                <!--          <template #default="scope">-->
                <!--                <span v-if="scope.row">已完成</span>-->
                <!--          </template>-->
                <!--        </el-table-column>-->
                <!--        <el-table-column align="left" label="任务指标" prop="name">-->
                <!--          <template #default="scope">-->
                <!--            <span @click="onpop" v-if="scope.row" style="cursor: pointer;color: #1f69e8">查看</span>-->
                <!--          </template>-->
                <!--        </el-table-column>-->
              </el-table>
              <Pagination
                :total="total"
                v-model:page="queryParams.pageNo"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
              />
            </div>
          </div>
        </div>


      </div>
    </div>
    <Dialog v-model="dialogVisible" width="700" title="表单">
      <div>表单预览</div>
      <template #footer>
        <div style="display: flex;align-items: center;justify-content: center;">
          <XButton title="保存" width="200px" gradient  @click="onParameter" />
          <!--        <el-button :disabled="formLoading" style="width: 200px;" type="primary">确定</el-button>-->
          <el-button style="width: 200px;" @click="dialogVisible=false">关闭</el-button>
        </div>
      </template>
    </Dialog>

  </div>
</template>

<script setup lang="ts">

import { reactive,toRefs,onBeforeMount,onMounted,watch,ref} from 'vue'
import {  ArrowLeft,ArrowRight,CirclePlus,Search,MessageBox,Position} from '@element-plus/icons-vue'
import {useRouter,useRoute} from "vue-router";
import { ElMessage } from 'element-plus'
const { t } = useI18n() // 国际化

const router = useRouter();
const routers = useRoute();
let id= ref('')
const activeName = ref('first');
const total = ref(10);
const dialogVisible = ref(false);
const formData = ref({});
const queryParams = ref({radio2:'1'});
const indexList = ref([]);


const goback = () =>{
  router.go(-1)
}

//表单显示
const displayform = () => {
  dialogVisible.value=true
}

//查看指标
const ViewIndicators=()=>{
  dialogVisible.value=true
}





const onParameter=()=>{
  dialogVisible.value=false

}

onBeforeMount(() => {

})
onMounted(() => {
  // getLog(routers.query.id)
  // getdeviceImg(routers.query.id)
})
</script>
<style lang="scss" scoped>
.box {
  display: flex;
  flex-direction: column;
  //height: 100vh;
  background: #f5f6f8;
  overflow: hidden;
}


.header {
  height: 48px;
  padding: 0 16px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);

  &-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .title {
    font-size: 16px;
    font-weight: 500;
  }
}

.content-wrapper {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 1px;
  min-height: 0; // 重要：防止内容溢出
  overflow: hidden;
}

.content-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  //gap: 2px;
  min-width: 0; // 重要：防止内容溢出
}



.detail-card {
  background: #fff;
  border-radius: 4px;
  //height: calc(80% - 8px);
  //display: flex;
  //flex-direction: column;
}
.stateStyls{
  width: 96px;
  height: 32px;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  margin-right: 20px;
  line-height: 36px;
  background: linear-gradient( 180deg, #0081FF 0%, #13A1FF 100%);
  border-radius: 4px 4px 4px 4px;
  color: #ffffff;
  img{
    width: 30px;
    height: 24px;
  }
}

.BasicContent{
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  min-height: 30px; /* 添加基准高度 */
  margin-top: 15px;
  .ContentStyle{
    flex: 1;
    display: flex;
    align-items: stretch;
    .labelStyle{
      background: #F7F8F8;
      max-width: 200px;
      min-width: 200px;
      height: 100%;
      font-style: normal;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #545658;
      border: 1px solid #ECEDEE;
    }
    .numerical{
      border: 1px solid #ECEDEE;
      width: 100%;
      padding: 7px 0;
      display: flex;
      align-items: center;
      padding-left: 15px;
      font-size: 14px;
      color: #545658;
      font-weight: 400;
      .attachment{
        border-bottom: 1px solid #ECEDEE;
        padding-left: 15px;
        padding: 7px 15px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .buttonStyle{
        display: flex;
        align-items: center;

      }
      .caozuo{
        display: flex;
        align-items: center;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #545658;
        line-height: 14px;
        text-align: left;
        font-style: normal;
        cursor: pointer;
        img{
          width: 18px;
          height: 18px;
          margin-right: 4px;
        }
      }
      .caozuo:hover{
        color: #005FFF;
      }
    }
  }
}

.card-header {
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  span{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 14px;
    color: #3B3C3D;
    line-height: 21px;
    font-style: normal;
    text-transform: none;
  }
  .header-bar {
    width: 12px;
    height: 12px;
    background: #005FFF;
    border-radius: 10px ;
  }
}

.detail-content {
  flex: 1;
  padding: 16px;
  overflow: hidden;
  .Task-content{
    display: flex;
    align-items: center;
    justify-content: space-between;
    .statistics{
      padding: 10px;
      background: #F4F7FE;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-size: 14px;
      color: #404A5B;
      line-height: 14px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}

.detail-item {
  background: #f2f2f2;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.detail-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.detail-col {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: flex-start;

  .label {
    min-width: 90px;
    color: #666;
  }

  .value {
    flex: 1;
    color: #333;
    word-break: break-all;
  }
}
.gradient-btn {
  background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
  border: none !important;
  font-size: 14px !important;
  padding: 9px 16px !important;
  color: #ffffff !important;

  &:hover {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.8;
  }

  &:active {
    background: linear-gradient( 270deg, #0053DF 0%, #3C85FF 100%) !important;
    opacity: 0.9;
  }

  // 覆盖 element-plus 的默认样式
  &.el-button--primary {
    --el-button-hover-bg-color: transparent;
    --el-button-active-bg-color: transparent;
  }
}

/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
/* 自定义滚动条样式 */
.MaintenanceRecord::-webkit-scrollbar {
  width: 6px;
}

.MaintenanceRecord::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.MaintenanceRecord::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
