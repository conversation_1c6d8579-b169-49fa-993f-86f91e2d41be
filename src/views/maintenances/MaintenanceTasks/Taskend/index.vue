<template>

  <ContentWrap class="h-1/1">
    <span class="TitleStyle">已结束</span>
  </ContentWrap>
  <el-row :gutter="24">
    <!-- 左侧部门树 -->
<!--    <el-col :span="4" :xs="24">-->
<!--      <ContentWrap    class="h-1/1" style="background: rgb(241 243 246)">-->
<!--        <DeptTree ref="treeRef" @node-click="handleDeptNodeClick" />-->
<!--      </ContentWrap>-->
<!--    </el-col>-->

    <el-col :span="24" :xs="24">
      <div class="mb-4">
        <el-button type="primary" plain>站点数量:5</el-button>
        <el-button type="primary" plain>设备类型:6</el-button>
        <el-button type="primary" plain>设备数量:15</el-button>
        <el-button type="primary" plain>辅件类型:3</el-button>
        <el-button type="primary" plain>设备数量:13</el-button>
        <el-button type="primary" plain>完成进度:75%</el-button>
      </div>
      <!-- 搜索 -->
      <ContentWrap>
        <el-form
          class="flex flex-wrap items-start -mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
        >
          <el-form-item label="" prop="assetsName" class="!mr-3">
            <el-input
              v-model="queryParams.assets"
              placeholder="输入设备编号/名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-140px"
            />
          </el-form-item>
          <el-form-item label="维保单位" prop="status" class="!mr-3">
            <el-select v-model="queryParams.status" class="!w-140px" clearable placeholder="请选择状态">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="任务状态" prop="status" class="!mr-3">
            <el-select v-model="queryParams.status" class="!w-140px" clearable placeholder="请选择状态">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="开始时间" prop="status" class="!mr-3">
            <el-date-picker
              style="width: 240px;"
              v-model="queryParams.value2"
              @change="onchange"
              type="daterange"
              value-format="YYYY-MM-DD"
              unlink-panels
              range-separator="至"
              start-placeholder="开始"
              end-placeholder="结束"
            />
          </el-form-item>
          <el-form-item label="结束时间" prop="status" class="!mr-3">
            <el-date-picker
              style="width: 240px;"
              v-model="queryParams.value3"
              value-format="YYYY-MM-DD"
              type="daterange"
              unlink-panels
              range-separator="至"
              start-placeholder="开始"
              end-placeholder="结束"
            />
          </el-form-item>
          <div class="min-w-[800px] flex-1 flex justify-between mb-[18px] pl-2">
            <div>
              <XButton title="查询" preIcon="ep:search" v-hasPermi="['infra:device-overview-info:query']" gradient @click="handleQuery" />
              <el-button @click="resetQuery"><Icon icon="ep:refresh" />重置</el-button>
            </div>
            <div>
              <el-button plain v-hasPermi="['infra:device-overview-info:export']" @click="handleExport" :loading="exportLoading">
                <Icon icon="ep:download" />导出
              </el-button>
            </div>
          </div>
        </el-form>
      </ContentWrap>

      <ContentWrap>
        <el-table ref="myTable" v-loading="loading" :data="list">
          <el-table-column type="index" label="序号" width="55" align="left">
            <template #default="scope">
              <span>{{(queryParams.pageNo-1) * queryParams.pageSize + scope.$index + 1}}</span>
            </template>
          </el-table-column>
          <el-table-column align="left" label="任务编号" prop="code"/>
          <el-table-column align="left" label="任务名称" prop="name"/>
          <el-table-column align="left" label="所属计划" prop="name"/>
          <el-table-column align="left" label="任务开始时间" prop="name"/>
          <el-table-column align="left" label="任务截止时间" prop="name"/>
          <el-table-column align="left" label="维保单位" prop="name"/>
          <el-table-column align="left" label="处理人" prop="name"/>
          <el-table-column :width="120" align="left" label="操作">
            <template #default="scope">
              <el-button
                link
                preIcon="ep:basketball"
                title="功能权限"
                type="primary"
                @click="openDetails(scope.row)"
              >
                详情
              </el-button>
              <el-button
                link
                preIcon="ep:basketball"
                title="功能权限"
                type="primary"
                @click="openhandle(scope.row)"
              >
                处理
              </el-button>

            </template>
          </el-table-column>
        </el-table>

        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import download from '@/utils/download'
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import DeptTree from './components/DeptTree.vue'
import {DICT_TYPE, getIntDictOptions} from "@/utils/dict";
const DownloadPath = '/infra/device-overview-info/get/template'
const route = useRouter()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
let options = ref([])
let projectId = ref('')
let postList = ref([
  { name: '质保中', id: '0' },
  { name: '已过保', id: '1' }
]) //质保状态
const props = { multiple: true, label: 'name' }
let selectionList = ref([])
export interface TenantExportReqVO {
  assetsCode?: string
  assetsName?: string
  assetsTypeId?: []
  areaId?: []
  supplierId?: string
  modelId?: string
  warrantyStatus?: string
  assetsSource?: string
  isKey?: string
  labelId?: []
}
let columnList = ref([
  { label: '任务编号', key: 'assetsCode' },
  { label: '任务名称', key: 'assetsName' },
  { label: '任务开始时间', key: 'assetsTypeName' },
  { label: '任务截止时间', key: 'area' },
  { label: '运维单位', key: 'supplierName' },
  { label: '任务状态', key: 'modelName' },
  { label: '检查进度', key: 'warrantyStatus' },
  { label: '操作', key: 'operate' }
]) // 列的集合
let statusList = ref([
  {
    label: '非重点',
    value: '0'
  },
  {
    label: '重点',
    value: '1'
  }
])
const loading = ref(false) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  areaId: undefined,
  username: undefined,
  warrantyStatus: undefined,
  isKey: undefined
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = false
  // loading.value = true
  try {
    loading.value = false
    // const data = await getPropertyPage(queryParams.value)
    // list.value = data.list
    // total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}

const getTableCatalogue = async () =>{
  // let res =await  getAccessoryTypePage({ pageNo: 1, pageSize: 100,dictType: 'deviceTab',})
  // if (res.list.length > 0) {
  //   res.list.forEach((item) => {
  //     if(item.status == '0'){
  //       columnList.value.push({
  //         label:item.value,
  //         key:item.label,
  //       })
  //     }
  //   })
  // }else {
  //
  // }
}


let tableRef = ref()

/** 表格选中数据 */
const checkList = (value: any) => {
  selectionList.value = value
}

const treeRef = ref()
/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    areaId: undefined,
    username: undefined,
    warrantyStatus: undefined,
    isKey: undefined
  }
  handleQuery()
  treeRef.value?.clearHighlight()
}

/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  // if (projectId.value){
  importFormRef.value.open()
  // }else {
  //   ElMessage.warning('请先选择设备类型')
  // }

}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    const ids=selectionList.value.map(item => item.id)
    // if (selectionList.value.length > 0)
    // {
    //   ids=selectionList.value.map(item => item.id)
    // }
    // // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    // const data = await deviceExportexcel({...queryParams.value,ids})
    download.excel(data, '设备数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    // await deleteProperty(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 */
onMounted(async () => {
  await getTableCatalogue()
  await getList()
})

// 获取区域信息
const getRegion = async () => {
  // let data = await getDicByDictType('oamArea')
  options.value = data
}

/** 左侧数点击 */
const handleDeptNodeClick = (value) => {
  // queryParams.value.assetsTypeId = [value.id]
  projectId.value = value.id
  getList()
}

/** 高级搜索弹窗 */
const formRef = ref()
const openForm = () => {
  formRef.value.open()
}

/** 配置二维码*/
const QRcodeRef = ref()
const Configure = () => {
  QRcodeRef.value.open()
}

//设备新增
const addition = () => {
  route.push('Equipmentaddition')
}

//设备编辑
const DeviceEdit = (id: number) => {
  route.push({
    path: 'Equipmentaddition',
    query: { id }
  })
}

//设备详情
const openDetails = (id: number) => {
  route.push({
    path: 'DeviceDetails',
    query: { id }
  })
}

</script>
<style scoped >
.staffTag {
  display: flex;
  align-items: center;
  margin-top: 3px;
  width: 100px;
  height: 50px;
  overflow: auto;
}
/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 3px;
}

/* 鼠标悬停时滚动条的样式 */
::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

</style>
