<template>
  <div class="main-top">
    <div class="header-box"> <span @click="goAlarmList(-2)">告警总览</span> </div>
    <div class="warning-box">
      <div><img class="w-16 h-16" src="@/assets/imgs/icons/workWarningBig.png" alt="" /></div>
      <div class="text-box">
        <p>告警总数</p>
        <p @click="goAlarmList(-2)" class="all-num">{{ props.warningCount.allCount }}</p>
      </div>
      <div class="text-box">
        <p>未处理</p>
        <p @click="goAlarmList(0)">{{ props.warningCount.noDealCount }}</p>
      </div>
      <div class="text-box">
        <p>处理中</p>
        <p @click="goAlarmList(1)">{{ props.warningCount.dealingCount }}</p>
      </div>
      <div class="text-box">
        <p>已处理</p>
        <p @click="goAlarmList(2)">{{ props.warningCount.dealCount }}</p>
      </div>
    </div>
    <div class="echarts-main">
      <div id="left-box" ref="chartDom"></div>
      <div id="right-box" ref="chartDom2"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, defineProps, watch } from 'vue'
import * as echarts from 'echarts'
import { useRouter } from 'vue-router'
const router = useRouter()
// echarts色块默认颜色(echarts自动生成)
const chartColors = [
  '#5470c6',
  '#91cc75',
  '#fac858',
  '#ee6666',
  '#73c0de',
  '#3ba272',
  '#fc8452',
  '#9a60b4',
  '#ea7ccc'
]

const props = defineProps({
  warningCount: {
    type: Object,
    required: true
  },
  equipmentBasis: {
    type: Object,
    required: true
  }
})

// 跳转告警
const goAlarmList = (flag) => {
  if (flag === -2) {
    router.push({
      path: '/alarm/list'
    })
  } else if (flag === -1) {
    const start = 'start'
    router.push({
      path: '/alarm/list',
      query: { warnUpdateTime: start.toString() }
    })
  } else if (flag === 0) {
    const flag = 0
    router.push({
      path: '/alarm/list',
      query: { dealStatus: flag.toString() }
    })
  } else if (flag === 1) {
    const flag = 1
    router.push({
      path: '/alarm/list',
      query: { dealStatus: flag.toString() }
    })
  } else if (flag === 2) {
    const flag = 2
    router.push({
      path: '/alarm/list',
      query: { dealStatus: flag.toString() }
    })
  } else if (flag === 3) {
    const flag = 3
    router.push({
      path: '/alarm/list',
      query: { dealStatus: flag.toString() }
    })
  }
}

const chartDom = ref(null)
const myChart = ref(null)
const chartDom2 = ref(null)
const myChart2 = ref(null)

const initGaugeChart = () => {
  if (!chartDom.value) {
    // console.warn('chartDom 未找到')
    return
  }
  if (chartDom.value) {
    if (myChart.value) {
      myChart.value.dispose()
    }
    myChart.value = echarts.init(chartDom.value)
    const dataValue = (props.warningCount.todayCount / props?.equipmentBasis?.deviceTotal) * 240
    // const dataValue = (70 / 100) * 240
    const color = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
      { offset: 0, color: '#FFB636' },
      { offset: 0.5, color: '#FF6616' },
      { offset: 1, color: 'red' }
    ])
    // var flag = '初版'
    // const option = {
    //   series: [
    //     {
    //       type: 'gauge',
    //       min: 0,
    //       max: 240,
    //       startAngle: 210,
    //       endAngle: -30,
    //       radius: '100%',
    //       center: ['50%', '55%'], // 调整仪表盘的垂直位置
    //       splitNumber: 10,
    //       // axisLine: {
    //       //   lineStyle: {
    //       //     width: 20,
    //       //     color: [[1, color]]
    //       //   }
    //       // },
    //       // axisLine: {
    //       //   lineStyle: {
    //       //     width: 20,
    //       //     color: [[1, '#FFF2EF']] // 固定背景颜色
    //       //   }
    //       // },
    //       axisLine: {
    //         lineStyle: {
    //           width: 20,
    //           color: [
    //             [
    //               dataValue / 240,
    //               new echarts.graphic.LinearGradient(0, 0, 1, 0, [
    //                 { offset: 0, color: '#FFD700' }, // 黄橙色起点
    //                 { offset: 1, color: '#FF4500' } // 红橙色终点
    //               ])
    //             ],
    //             [1, '#FFF2EF']
    //           ]
    //         }
    //       },
    //       pointer: {
    //         show: true,
    //         width: 2,
    //         length: '85%',
    //         offsetCenter: [0, '-15%'], // 调整指针位置
    //         itemStyle: {
    //           color: color
    //         }
    //       },
    //       axisTick: { show: false },
    //       splitLine: {
    //         show: true,
    //         length: 20,
    //         distance: -20,
    //         lineStyle: { width: 2, color: '#fff' }
    //       },
    //       axisLabel: {
    //         fontSize: 14,
    //         color: '#999',
    //         distance: 30,
    //         formatter(value) {
    //           if (value === 0 || value === 240) {
    //             return ''
    //           }
    //           return ''
    //         }
    //       },
    //       detail: {
    //         // valueAnimation: true,
    //         show: true,
    //         formatter: '当日新增告警',
    //         fontSize: 14,
    //         fontWeight: 'normal',
    //         color: '#333',
    //         offsetCenter: [0, '60%'] // 调整 "当日新增告警" 的位置，向下偏移
    //       },
    //       itemStyle: {
    //         borderColor: color,
    //         borderWidth: 2
    //       },
    //       data: [
    //         {
    //           value: (props.warningCount.todayCount / props?.equipmentBasis?.deviceTotal) * 240
    //           // value: (70 / 100) * 240
    //           // name: '当日新增告警',
    //           // label: {
    //           //   show: true,
    //           //   position: 'top',
    //           //   color: '#333',
    //           //   fontSize: 14
    //           // }
    //         }
    //       ]
    //     },
    //     {
    //       name: '中心圆',
    //       type: 'pie',
    //       radius: '35%',
    //       center: ['50%', '55%'], // 调整中心圆位置
    //       z: 20,
    //       animation: false,
    //       label: {
    //         show: true,
    //         position: 'center',
    //         distance: 0,
    //         formatter: function (params) {
    //           return '{a|' + props.warningCount.todayCount + '}{b|}{c|}'
    //           // return '{a|' + 70 + '}{b|}{c|}'
    //         },
    //         rich: {
    //           a: { color: '#FF6600', fontSize: 20, fontWeight: 'bold' },
    //           b: { color: '#FF6616', fontSize: 20, fontWeight: 'bold' },
    //           c: { color: 'red', fontSize: 20, fontWeight: 'bold' }
    //         }
    //       },
    //       tooltip: { show: false },
    //       itemStyle: {
    //         color: '#fff',
    //         borderColor: '#fff',
    //         borderWidth: 3
    //         // 添加以下两行以开启阴影
    //         // shadowBlur: 10, // 阴影模糊程度
    //         // shadowColor: 'rgba(0, 0, 0, 0.1)' // 阴影颜色（带透明度）
    //       },
    //       emphasis: {
    //         scale: false,
    //         itemStyle: {
    //           shadowBlur: 0,
    //           shadowOffsetX: 0,
    //           shadowOffsetY: 0
    //         }
    //       },
    //       data: [
    //         {
    //           value: props.warningCount.todayCount,
    //           name: '当日新增告警',
    //           itemStyle: {
    //             borderColor: '#fff',
    //             borderWidth: 1
    //           }
    //         }
    //       ]
    //     }
    //   ]
    // }
    const option = {
      series: [
        {
          type: 'gauge',
          min: 0,
          max: 240,
          startAngle: 210,
          endAngle: -30,
          radius: '100%',
          center: ['50%', '55%'], // 调整仪表盘的垂直位置
          splitNumber: 10,
          // axisLine: {
          //   lineStyle: {
          //     width: 20,
          //     color: [[1, color]]
          //   }
          // },
          // axisLine: {
          //   lineStyle: {
          //     width: 20,
          //     color: [[1, '#FFF2EF']] // 固定背景颜色
          //   }
          // },
          axisLine: {
            lineStyle: {
              width: 20,
              color: [
                [
                  dataValue / 240,
                  new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                    { offset: 0, color: '#FFD700' }, // 黄橙色起点
                    { offset: 1, color: '#FF4500' } // 红橙色终点
                  ])
                ],
                [1, '#FFF2EF']
              ]
            }
          },
          pointer: {
            show: true,
            width: 2,
            length: '85%',
            offsetCenter: [0, '-15%'], // 调整指针位置
            itemStyle: {
              color: color
            }
          },
          axisTick: { show: false },
          splitLine: {
            show: true,
            length: 20,
            distance: -20,
            lineStyle: { width: 2, color: '#fff' }
          },
          axisLabel: {
            fontSize: 14,
            color: '#999',
            distance: 30,
            formatter(value) {
              if (value === 0 || value === 240) {
                return ''
              }
              return ''
            }
          },
          detail: {
            // valueAnimation: true,
            show: true,
            formatter: '当日新增告警',
            fontSize: 14,
            fontWeight: 'normal',
            color: '#333',
            offsetCenter: [0, '70%'] // 调整 "当日新增告警" 的位置，向下偏移
          },
          itemStyle: {
            borderColor: color,
            borderWidth: 2
          },
          data: [
            {
              value: (props.warningCount.todayCount / props?.equipmentBasis?.deviceTotal) * 240
              // value: (70 / 100) * 240
              // name: '当日新增告警',
              // label: {
              //   show: true,
              //   position: 'top',
              //   color: '#333',
              //   fontSize: 14
              // }
            }
          ]
        },

        {
          name: '中心圆',
          type: 'pie',
          radius: '35%',
          center: ['50%', '55%'], // 调整中心圆位置
          z: 20,
          animation: false,
          label: {
            show: true,
            position: 'center',
            distance: 0,
            formatter: function (params) {
              return '{a|' + props.warningCount.todayCount + '}{b|}{c|}'
              // return '{a|' + 70 + '}{b|}{c|}'
            },
            rich: {
              a: { color: '#FF6600', fontSize: 20, fontWeight: 'bold' },
              b: { color: '#FF6616', fontSize: 20, fontWeight: 'bold' },
              c: { color: 'red', fontSize: 20, fontWeight: 'bold' }
            }
          },
          tooltip: { show: false },
          itemStyle: {
            color: '#fff',
            borderColor: '#fff',
            borderWidth: 3
            // 添加以下两行以开启阴影
            // shadowBlur: 10, // 阴影模糊程度
            // shadowColor: 'rgba(0, 0, 0, 0.1)' // 阴影颜色（带透明度）
          },
          emphasis: {
            scale: false,
            itemStyle: {
              shadowBlur: 0,
              shadowOffsetX: 0,
              shadowOffsetY: 0
            }
          },
          data: [
            {
              value: props.warningCount.todayCount,
              name: '当日新增告警',
              itemStyle: {
                borderColor: '#fff',
                borderWidth: 1
              }
            }
          ]
        },
        // 仪表盘内圆环样式
        // {
        //   type: 'pie',
        //   radius: ['35%', '55%'],
        //   center: ['50%', '55%'],
        //   labelLine: {
        //     show: false
        //   },
        //   itemStyle: {
        //     color: '#f5f7fa', // 可自定义颜色
        //     // borderColor: '#dcdcdc', // 添加柔和的边框以增强凹陷感
        //     // borderWidth: 1,
        //     // shadowBlur: 8, // 添加阴影模糊效果
        //     shadowColor: 'rgba(0, 0, 0, 0.1)' // 模拟凹陷的阴影效果
        //   },
        //   emphasis: {
        //     itemStyle: {
        //       shadowBlur: 12,
        //       shadowColor: 'rgba(0, 0, 0, 0.15)'
        //     }
        //   },
        //   data: [{ value: 1 }],
        //   hoverAnimation: true,
        //   animationType: 'radialOut',
        //   silent: true,
        //   z: 999
        // }
        // 仪表盘内圆环样式
        {
          type: 'pie',
          radius: ['35%', '55%'],
          center: ['50%', '55%'],
          labelLine: {
            show: false
          },
          itemStyle: {
            color: '#f5f7fa', // 背景色
            shadowBlur: 3, // 阴影模糊程度
            shadowColor: 'rgba(0, 0, 0, 0.1)' // 调整阴影颜色，使阴影更明显
          },
          data: [{ value: 1 }],
          // hoverAnimation: true,
          emphasis: {
            scale: true // 新方式禁用悬停放大动画
          },
          animationType: 'radialOut',
          silent: true,
          z: 999
        }
      ]
    }
    // 调用两次防止仪表板不渲染
    myChart.value.setOption(option)
    // myChart.value.setOption(option)
    // myChart.value.setOption(option)
  }
}

const hexToRGBA = (hex, alpha) => {
  const r = parseInt(hex.slice(1, 3), 16)
  const g = parseInt(hex.slice(3, 5), 16)
  const b = parseInt(hex.slice(5, 7), 16)
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}

const initPieChart = () => {
  if (chartDom2.value) {
    if (myChart2.value) {
      myChart2.value.dispose()
    }
    // 数据列表 echarts默认颜色色块
    const dataList = ref([
      { color: '#5470c6' },
      { color: '#91cc75' },
      { color: '#fac858' },
      { color: '#ee6666' },
      { color: '#73c0de' },
      { color: '#3ba272' },
      { color: '#fc8452' },
      { color: '#9a60b4' },
      { color: '#ea7ccc' }
    ])
    myChart2.value = echarts.init(chartDom2.value)
    // const processedData = Object.entries(props.warningCount.sourceCount).map(([key, value]) => ({
    //   name: key,
    //   value: value,
    //   itemStyle: {
    //     color: dataList[index % dataList.value.length].color // 使用 dataList 的颜色
    //   }
    // }))
    const processedData = Object.entries(props.warningCount.sourceCount).map(
      ([key, value], index) => ({
        name: key,
        value: value,
        itemStyle: {
          color: dataList.value[index % dataList.value.length].color.trim() // 确保无空格
        }
      })
    )

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: (params) => {
          const item = processedData.find((item) => item.name === params.name)
          const total = processedData.reduce((sum, item) => sum + item.value, 0)
          const percent = Math.round((item.value / total) * 100)
          return `${params.name}: ${item.value} (${percent}%)`
        }
      },
      legend: {
        orient: 'vertical',
        left: '55%',
        top: 'center',
        itemWidth: 10,
        itemHeight: 10,
        width: 130, // 固定宽度，防止内容撑开
        height: 200, // 设置一个固定高度 溢出滚动
        overflow: 'scroll', // 开启滚动
        pageButtonItemGap: 0, // 页面按钮间距
        pageIconSize: 8, // 分页图标大小
        pageIconColor: '#999', // 分页图标颜色
        pageIconInactiveColor: '#ccc', // 分页图标禁用颜色
        pageTextStyle: {
          color: '#999'
        },
        textStyle: {
          color: '#808080'
        },
        formatter: (name) => {
          const item = processedData.find((item) => item.name === name)
          const total = processedData.reduce((sum, item) => sum + item.value, 0)
          const percent = Math.round((item.value / total) * 100)

          const maxLength = 10
          let formattedName = name
          if (name.length > maxLength) {
            formattedName = name.substring(0, maxLength - 3) + '...'
          }
          // return `${formattedName} ${percent}%`
          return `${formattedName}`
        },
        data: processedData
      },
      series: [
        // 饼图
        // 最内层小圆环（包裹文字）
        // {
        //   type: 'pie',
        //   radius: ['40%', '55%'],
        //   center: ['30%', '50%'],
        //   label: {
        //     show: false // 关键：关闭 label 显示
        //   },
        //   labelLine: {
        //     show: false // 关键：关闭引线
        //   },
        //   itemStyle: {
        //     color: '#f5f7fa', // 可自定义颜色
        //     borderWidth: 0
        //   },
        //   data: [{ value: 1 }],
        //   hoverAnimation: false,
        //   silent: true,
        //   z: 999
        // },
        // 内圆环饼图
        {
          type: 'pie',
          radius: ['35%', '45%'],
          // radius: ['45%', '60%'],
          // radius: ['55%', '60%'],
          center: ['30%', '50%'],
          labelLine: {
            show: false
          },

          itemStyle: {
            color: '#f5f7fa', // 背景色
            shadowBlur: 3, // 阴影模糊程度
            shadowColor: 'rgba(0, 0, 0, 0.1)' // 调整阴影颜色，使阴影更明显
          },
          data: [{ value: 1 }],
          // hoverAnimation: false,//废弃
          emphasis: {
            scale: false // 新方式禁用悬停放大动画
          },
          silent: true,
          z: 999
        },
        // 半透明灰色圆环
        // {
        //   type: 'pie',
        //   radius: ['60%', '85%'],
        //   center: ['30%', '50%'],
        //   label: { show: false },
        //   labelLine: { show: false },
        //   itemStyle: {
        //     color: function (params) {
        //       return 'rgba(0, 0, 0, 0.05)' // 半透明灰色
        //     }
        //   },
        //   data: processedData,
        //   z: 2, // 确保显示在外层圆环之上
        //   silent: true
        // },

        // 外层圆环
        {
          type: 'pie',
          radius: ['55%', '80%'],
          // radius: ['70%', '95%'],
          center: ['30%', '50%'],
          label: {
            show: true,
            position: 'outside',
            formatter: '{d}%', // 只显示百分比
            fontSize: 12,
            color: 'inherit', //  允许继承 rich 的颜色
            rich: {
              a: (params) => ({ color: params.color }) // 绑定颜色
            }
          },
          // labelLine: { show: false },
          labelLine: {
            show: true,
            length: 10, // 第一段引线长度（靠近饼图）
            length2: 10, // 第二段引线长度（靠近标签）
            // 注释掉lineStyle引线默认继承色块颜色
            // lineStyle: {
            //   color: 'inherit' // 引线颜色也继承扇区颜色
            //   // color: function (params) {
            //   //   return params.color // 手动返回扇区颜色
            //   // }
            // },
            data: processedData
          },
          itemStyle: {
            borderWidth: 5,
            borderColor: '#fff'
          },
          data: processedData
        }
      ],
      graphic: [
        {
          type: 'text',
          left: '27%',
          top: 'center',
          style: {
            text: '来源',
            textAlign: 'center',
            fill: '#333',
            fontSize: 14
          }
        }
      ]
    }

    myChart2.value.setOption(option)
  }
  myChart2.value.on('legendselectchanged', updateSourceText)
}
const updateSourceText = () => {
  if (myChart2.value) {
    const optionUpdate = {
      graphic: [
        {
          type: 'text',
          left: '27%',
          top: 'center',
          style: {
            text: '来源',
            textAlign: 'center',
            fill: '#333',
            fontSize: 14
          }
        }
      ]
    }
    myChart2.value.setOption(optionUpdate)
  }
}
const updateGraphicText = () => {
  if (myChart2.value) {
    const optionUpdate = {
      graphic: [
        {
          type: 'text',
          left: '25%',
          top: 'center',
          style: {
            text: '来源',
            textAlign: 'center',
            fill: '#333',
            fontSize: 14
          }
        }
      ]
    }
    myChart2.value.setOption(optionUpdate)
  }
}

const resizeChart = () => {
  if (myChart.value) {
    myChart.value.resize()
  }
  if (myChart2.value) {
    myChart2.value.resize()
    // updateGraphicText() // 更新文字
    updateSourceText() // 更新来源文字
  }
}
onMounted(() => {
  nextTick(() => {
    initGaugeChart()
    initPieChart()
    window.addEventListener('resize', resizeChart)
    // console.log('打印告警数据', props.warningCount)
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', resizeChart)
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
  if (myChart2.value) {
    myChart2.value.dispose()
    myChart2.value = null
  }
})

// const resizeChart = () => {
//   if (myChart.value) {
//     myChart.value.resize()
//   }
//   if (myChart2.value) {
//     myChart2.value.resize()
//   }
// }

// 使用 watch 监听 props.warningCount 的变化
// watch(
//   () => props.warningCount,
//   () => {
//     initGaugeChart()
//     initPieChart()
//   },
//   { deep: true }
// )
watch(
  () => [props.warningCount, props.equipmentBasis],
  () => {
    nextTick(() => {
      initGaugeChart()
      initPieChart()
    })
  },
  { deep: true }
)
</script>

<style lang="scss" scoped>
.main-top {
  // overflow: hidden;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff !important;
  border-radius: 8px !important;

  .header-box {
    span {
      &:hover {
        color: #005fff;
        cursor: pointer;
      }
    }
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    position: relative;
    font-family: Microsoft YaHei;
    font-weight: bold;
    font-size: 16px;
    color: #3b3c3d;
    text-align: left;
    font-style: normal;
    text-transform: none;
    line-height: 24px;
    margin: 3px 0;
    padding-left: 28px;

    &::before {
      margin: 10px 0 0 10px;
      content: '';
      position: absolute;
      left: 0;
      top: 6px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #005fff;
      border: 2px solid #d4e4ff;
    }
  }
  .warning-box {
    width: 96%;
    height: 64px;
    // margin: 0 20px;
    background-color: #fffaf5;
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-bottom: 5px;
    .text-box {
      font-size: 14px;
      .all-num {
        color: orangered;
        text-align: center;
      }
      p:nth-child(2) {
        font-size: 18px;
        font-weight: bold;
        text-align: center;
        cursor: pointer;
      }
    }
  }
  .echarts-main {
    width: 100%;
    // height: 167px;
    height: 220px;
    // background-color: pink;
    // flex: 1;
    display: flex;
    #left-box {
      width: 100%;
      height: 100%;
    }
    #right-box {
      width: 100%;
      height: 100%;
      // background-color: pink;
    }
  }
}
</style>
