<template>
  <div class="left-bottom">
    <div class="title-box">项目信息总览</div>
    <div id="main" ref="chartDom"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, defineProps, watch } from 'vue'
import * as echarts from 'echarts'

const chartDom = ref(null)
const myChart = ref(null)

// 定义 props，接收父组件传递的 ProjectCount
const props = defineProps({
  projectCount: {
    type: Object,
    required: true
  }
})

// 数据列表
const dataList = ref([
  { name: '运维项目', value: props.projectCount.oamCount, color: '#FFBC1C ' },
  { name: '建设项目', value: props.projectCount.buildCount, color: '#3681FF' }
])
// const dataList = ref([
//   { name: '运维项目', value: 0, color: '#FFBC1C ' },
//   { name: '建设项目', value: 0, color: '#3681FF' }
// ])

const hexToRGBA = (hex, alpha) => {
  const r = parseInt(hex.slice(1, 3), 16)
  const g = parseInt(hex.slice(3, 5), 16)
  const b = parseInt(hex.slice(5, 7), 16)
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}
// 初始化图表
const initChart = () => {
  if (chartDom.value) {
    // 确保在初始化新实例前清理旧实例
    if (myChart.value) {
      myChart.value.dispose()
    }

    const total = dataList.value.reduce((acc, item) => acc + item.value, 0)
    let percentage1 = '0%'
    let percentage2 = '0%'

    if (total > 0) {
      percentage1 = ((dataList.value[0].value / total) * 100).toFixed(2) + '%'
      percentage2 = ((dataList.value[1].value / total) * 100).toFixed(2) + '%'
    } else {
      // 如果数据为空则置空不展示
      percentage1 = ''
      percentage2 = ''
    }
    // const percentage1 = ((dataList.value[0].value / total) * 100).toFixed(2)
    // const percentage2 = ((dataList.value[1].value / total) * 100).toFixed(2)

    myChart.value = echarts.init(chartDom.value)

    // 动态计算中间文字的位置
    const count = Number(props.projectCount.allCount)
    // const count = 1000
    const middleTextLeft =
      count < 10
        ? -6
        : count >= 10 && count < 100
          ? -12
          : count >= 100 && count < 1000
            ? -20
            : count >= 1000 && count < 9999
              ? -28
              : '50%'
    const option = {
      // title: {
      //   text: '项目信息总览',
      //   left: '20',
      //   top: '10',
      //   textStyle: {
      //     fontSize: 16,
      //     color: '#000',
      //     fontWeight: 'normal'
      //   }
      // },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        itemWidth: 10,
        itemHeight: 10,
        bottom: 20,
        left: 'center',
        textStyle: {
          color: '#808080'
        },
        data: dataList.value.map((item) => ({
          name: item.name,
          icon: 'rect',
          itemStyle: {
            color: item.color
          }
        }))
      },
      series: [
        // 最内存圆环包裹文字
        {
          type: 'pie',
          radius: ['37%', '40%'],
          center: ['50%', '40%'],
          labelLine: {
            show: false
          },
          itemStyle: {
            color: '#f5f7fa', // 背景色
            shadowBlur: 3, // 阴影模糊程度
            shadowColor: 'rgba(0, 0, 0, 0.1)' // 调整阴影颜色，使阴影更明显
          },
          data: [{ value: 1 }],
          // hoverAnimation: false,
          emphasis: {
            scale: false // 新方式禁用悬停放大动画
          },
          silent: true,
          z: 999
        },
        // 浅色内圆环
        {
          name: '内圆环',
          type: 'pie',
          radius: ['40%', '55%'],
          center: ['50%', '40%'],
          silent: true,
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          itemStyle: {
            color: function (params) {
              const color = dataList.value[params.dataIndex].color
              return hexToRGBA(color, 0.5)
            }
          },
          data: dataList.value
        },
        // 外圆环
        {
          name: '项目信息',
          type: 'pie',
          radius: ['55%', '70%'],
          center: ['50%', '40%'],
          label: {
            show: true,
            position: 'outside',
            formatter: (params) => {
              // return `{name|${params.percent.toFixed(2)}%}`
              // 只有存在数据时才展示百分比
              return params.value === 0 ? '' : `{name|${params.percent.toFixed(2)}%}`
            },
            rich: {
              name: (params) => {
                const color = params.name === '运维项目' ? '#FFBC1C' : '#3681FF'
                return { color: color.trim() }
              }
            },
            // textStyle: {
            color: 'inherit', //  允许继承 rich 的颜色
            fontSize: 12,
            // },
            labelLine: {
              show: true,
              length: 10, // 第一段引线长度（靠近饼图）
              length2: 20, // 第二段引线长度（靠近标签）
              lineStyle: {
                color: 'inherit' // 引线颜色也继承扇区颜色
              }
            }
          },
          itemStyle: {
            color: function (params) {
              return dataList.value[params.dataIndex].color.trim()
            }
          },
          // data: dataList.value
          // 只有存在数据时才展示引线
          data: dataList.value.map((item) => ({
            ...item,
            label: {
              show: item.value > 0
            },
            labelLine: {
              show: item.value > 0
            }
          }))
        }
      ],
      graphic: [
        {
          type: 'group',
          left: 'center',
          top: '33%',
          // top: 'center',
          z: 10,
          children: [
            {
              type: 'text',
              left: middleTextLeft,
              top: '-15',
              style: {
                text: `${count}`,
                fontSize: 24,
                fontWeight: 'bold',
                fill: '#000',
                textAlign: 'center'
              }
            },
            {
              type: 'text',
              left: 'center',
              top: '15',
              style: {
                text: '项目总数',
                fontSize: 12,
                fill: '#808080',
                textAlign: 'center'
              }
            }
          ]
        }
      ]
    }

    myChart.value.setOption(option)

    // 添加 legendselectchanged 事件监听
    myChart.value.on('legendselectchanged', updateGraphic)
  }
}

const updateGraphic = () => {
  if (myChart.value) {
    // 获取当前图表的配置项
    const currentOption = myChart.value.getOption()
    // 重新应用配置项
    myChart.value.setOption(currentOption)
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
    window.addEventListener('resize', resizeChart)
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', resizeChart)
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
})

const resizeChart = () => {
  if (myChart.value) {
    myChart.value.resize()
    updateGraphic() // 调用 updateGraphic 确保 graphic 内容正确
  }
}
// 使用 watch 监听 props.projectCount 的变化
watch(
  () => props.projectCount,
  (newVal) => {
    dataList.value = [
      { name: '运维项目', value: newVal.oamCount, color: '#FFBC1C' },
      { name: '建设项目', value: newVal.buildCount, color: '#3A86FF' }
    ]

    nextTick(() => {
      initChart()
      updateGraphic() // 调用 updateGraphic 确保 graphic 内容正确
    })
  },
  { deep: true }
)
</script>

<style lang="scss" scoped>
.left-bottom {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .title-box {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    position: relative;
    font-family: Microsoft YaHei;
    font-weight: bold;
    font-size: 16px;
    color: #3b3c3d;
    text-align: left;
    font-style: normal;
    text-transform: none;
    line-height: 24px;
    margin: 3px 0;
    padding-left: 28px;

    &::before {
      margin: 10px 0 0 10px;
      content: '';
      position: absolute;
      left: 0;
      top: 6px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #005fff;
      border: 2px solid #d4e4ff;
    }
  }
  #main {
    flex: 1;
  }
}
</style>
