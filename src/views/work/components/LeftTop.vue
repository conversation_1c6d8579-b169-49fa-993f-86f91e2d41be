<template>
  <div class="left-top">
    <div class="header-user">
      <div class="left-user">
        <div class="img-box">
          <el-image :src="userInfo?.avatar">
            <template #error>
              <img src="@/assets/imgs/icons/userIcon.png" alt="Default User" />
            </template>
          </el-image>
        </div>
        <div class="user-info">
          <p>{{ userInfo?.nickname }}</p>
          <p>{{ userInfo?.dept?.name }} {{ userInfo?.roles?.[0]?.name }}</p>
        </div>
      </div>
      <div class="right-calendar">
        <!-- <div class="img-box"><img src="@/assets/imgs/icons/synoptic.png" alt="" /></div> -->
        <div class="calendar-info">
          <p>{{ currentDate }}</p>
          <p>{{ currentDay }}</p>
        </div>
      </div>
    </div>
    <div class="tickets-box">
      <div class="tickets-box1">
        <div class="tickets-left">
          <div class="img-box"><img src="@/assets/imgs/icons/disposalIconBig.png" alt="" /></div>
          <div class="info-box">
            <p>待处理工单</p>
            <p>160</p>
          </div>
        </div>
        <div class="tickets-right">
          <el-icon><ArrowRight /></el-icon>
        </div>
      </div>
      <div class="tickets-box2">
        <div class="tickets-left">
          <div class="img-box"><img src="@/assets/imgs/icons/auditIconBig.png" alt="" /></div>
          <div class="info-box">
            <p>待审核工单</p>
            <p>160</p>
          </div>
        </div>
        <div class="tickets-right">
          <el-icon><ArrowRight /></el-icon>
        </div>
      </div>
    </div>
    <div class="calendar-box">
      <Calendar />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getUserProfile, ProfileVO } from '@/api/system/user/profile' //引入获取用户信息接口
import { ArrowRight } from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'
import VueCal from 'vue-cal'
import 'vue-cal/dist/vuecal.css'
import Calendar from './Calendar.vue'

const value = ref(new Date())
const backendData = ref([])
const userInfo = ref({} as ProfileVO)
const getUserInfo = async () => {
  const users = await getUserProfile()
  userInfo.value = users
  // console.log('打印用户信息', userInfo.value)
  // console.log('打印头像', userInfo.value.avatar)
}

const currentDate = ref('')
const currentDay = ref('')

const formatCurrentDate = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

const getDayOfWeek = () => {
  const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  const now = new Date()
  return days[now.getDay()]
}

onMounted(async () => {
  await getUserInfo()
  currentDate.value = formatCurrentDate()
  currentDay.value = getDayOfWeek()
})
const getDayStyle = (day) => {
  if (!Array.isArray(day) || day.length === 0) {
    return ''
  } else {
    const allDutyStatusIs1 = day.every((item) => item.dutyStatus === 1)
    const hasDutyStatus2 = day.some((item) => item.dutyStatus === 2)

    if (allDutyStatusIs1) {
      return 'text-[#0053DF]'
    } else if (hasDutyStatus2) {
      return 'text-[#F67C00]'
    } else {
      return 'text-[#3B3C3D]'
    }
  }
}
const getPeopleForDate = (obj) => {
  const date = obj.cell.formattedDate
  const event = backendData.value.find((item) => item.date === date)
  return event ? event.dutyStaffs : []
}
</script>

<style lang="scss" scoped>
.left-top {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  // background-color: lightgreen;
  .header-user {
    width: 100%;
    height: 20%;
    // background-color: plum;
    display: flex;
    justify-content: space-between;
    .left-user {
      width: 50%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      // background-color: lawngreen;
      .img-box {
        width: 48px;
        height: 48px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .user-info {
        width: 65%;
        p:nth-child(1) {
          font-size: 18px;
          font-weight: bold;
          padding-bottom: 5px;
        }
        p:nth-child(2) {
          font-size: 12px;
          padding-bottom: 5px;
        }
      }
      // background-color: aquamarine;
    }
    .right-calendar {
      width: 50%;
      height: 100%;
      // background-color: rgb(45, 151, 29);
      display: flex;
      align-items: center;
      justify-content: center;
      .calendar-info {
        p {
          font-size: 14px;
        }
      }
    }
  }
  .tickets-box {
    width: 100%;
    height: 20%;
    // background-color: rgb(29, 151, 92);
    display: flex;
    justify-content: space-evenly;
    .tickets-box1 {
      width: 45%;
      height: 90%;
      background-color: #f6f7f9;
      border-radius: 5px;
      display: flex;
      justify-content: space-around;
      .tickets-left {
        display: flex;
        align-items: center;
        .img-box {
          width: 40px;
          height: 40px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .info-box {
          p:nth-child(1) {
            font-size: 14px;
            padding-bottom: 5px;
          }
          p:nth-child(2) {
            font-size: 18px;
            font-weight: bold;
            padding-bottom: 5px;
          }
        }
      }
      .tickets-right {
        display: flex;
        align-items: center;
      }
    }
    .tickets-box2 {
      width: 45%;
      height: 90%;
      background-color: #f6f7f9;
      border-radius: 5px;
      display: flex;
      justify-content: space-around;
      .tickets-left {
        display: flex;
        align-items: center;
        .img-box {
          width: 40px;
          height: 40px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .info-box {
          p:nth-child(1) {
            font-size: 14px;
            padding-bottom: 5px;
          }
          p:nth-child(2) {
            font-size: 18px;
            font-weight: bold;
            padding-bottom: 5px;
          }
        }
      }
      .tickets-right {
        display: flex;
        align-items: center;
      }
    }
  }
  .calendar-box {
    width: 100%;
    height: 60%;
    // background-color: pink;
  }
}
</style>
