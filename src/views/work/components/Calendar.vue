<template>
  <div class="custom-calendar">
    <div class="calendar-header">
      <div class="title">排班信息</div>
      <div class="year-month-selector">
        <el-cascader
          v-model="currentYearMonth"
          :options="yearMonthOptions"
          @change="handleYearMonthChange"
          placeholder="选择年月"
          style="width: 140px"
        />
      </div>
    </div>
    <div class="calendar-body">
      <div class="weekdays">
        <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
      </div>
      <div class="days">
        <div
          v-for="(day, index) in days"
          :key="index"
          class="day"
          :class="{
            'other-month': day.otherMonth,
            today: isToday(day.date),
            selected: isSelected(day.date)
          }"
          @click="handleDayClick(day.date)"
          @mouseover="handleDayHover(day.date)"
          tabindex="0"
          ref="dayRefs"
        >
          {{ day.date.date() }}
          <div v-if="day.shiftInfo" class="shift-info-tag">值</div>
          <el-popover
            v-if="isSelected(day.date)"
            placement="right"
            width="264"
            height="156"
            trigger="click"
            v-model:visible="showPopover"
            @after-leave="handlePopoverLeave"
          >
            <template #reference>
              <div class="popover-reference"></div>
            </template>
            <div class="popover-content" style="font-size: 12px">
              <div
                class="popover-header"
                style="
                  height: 30px;
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  font-size: 12px;
                  border-radius: 5px 5px 0 0;
                "
              >
                <div class="selected-date">
                  <span style="color: #0053df; font-weight: bold">{{
                    selectedDate ? selectedDate.format('YYYY年MM月DD日') : ''
                  }}</span>
                  <!-- 值班人员信息 -->
                </div>
                <div class="close-btn" style="padding-right: 8px" @click="closePopover">
                  <el-icon style="cursor: pointer"><Close /></el-icon>
                </div>
              </div>
              <div class="popover-body" style="border-radius: 0 0 5px 5px">
                <el-table
                  :data="shiftInfoList"
                  style="
                    width: 100%;
                    height: 150px;
                    overflow-y: auto;
                    font-size: 12px;
                    border-radius: 0 0 5px 5px;
                  "
                >
                  <el-table-column
                    prop="deptName"
                    label="运维单位"
                    min-width="80"
                    style="font-size: 12px"
                    width="80"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="omuAdminName"
                    label="负责人"
                    min-width="80"
                    style="font-size: 12px"
                    width="80"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="omuAdminPhone"
                    label="联系方式"
                    min-width="120"
                    style="font-size: 12px"
                    width="80"
                    show-overflow-tooltip
                  />
                </el-table>
              </div>
            </div>
          </el-popover>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Close } from '@element-plus/icons-vue'
import { ref, computed, watch, inject, nextTick } from 'vue'
import dayjs from 'dayjs'

const updateYearMonth = inject('updateYearMonth', Function)
const schedulingInfo = inject('schedulingInfo', ref([])) // 注入爷爷组件的排班信息

const currentYear = ref(dayjs().year())
const currentMonth = ref(dayjs().month() + 1) // 月份从1开始
const currentYearMonth = ref([dayjs().year(), dayjs().month() + 1])
const selectedDate = ref<dayjs.Dayjs | null>(null)
const showPopover = ref(false)
const shiftInfoList = ref([]) // 定义 shiftInfoList
const dayRefs = ref([]) // 用于存储每个日期元素的引用

// 这里需要根据点击的日期来过滤数据
const updateShiftInfoList = () => {
  if (!selectedDate.value) {
    shiftInfoList.value = []
    return
  }

  const selectedDateString = selectedDate.value.format('YYYY-MM-DD')
  // 确保 schedulingInfo 是一个数组
  if (Array.isArray(schedulingInfo.value)) {
    const matchedItem = schedulingInfo.value.find((item) => item.date === selectedDateString)
    console.log('打印匹配的日期', matchedItem)
    if (matchedItem) {
      shiftInfoList.value = matchedItem.list
    } else {
      shiftInfoList.value = []
    }
  } else {
    shiftInfoList.value = []
  }
}

// 当 schedulingInfo 或 selectedDate 更新时，重新筛选数据
watch([schedulingInfo, selectedDate], updateShiftInfoList, { immediate: true })

const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

// 计算年份范围（当前年份的前后10年）
const yearRange = computed(() => {
  const startYear = dayjs().year() - 10
  const endYear = dayjs().year() + 10
  return Array.from({ length: 21 }, (_, i) => startYear + i)
})

// 创建年月选项
const yearMonthOptions = computed(() => {
  return yearRange.value.map((year) => ({
    value: year,
    label: year + '年',
    children: Array.from({ length: 12 }, (_, i) => ({
      value: i + 1,
      label: i + 1 + '月'
    }))
  }))
})

// 计算当前月份的天数以及上个月和下个月的部分天数
const days = computed(() => {
  const firstDay = dayjs()
    .set('year', currentYear.value)
    .set('month', currentMonth.value - 1)
    .startOf('month')
  const firstDayOfWeek = firstDay.day() // 获取第一天是星期几（0-6，0是星期日）
  const daysInMonth = firstDay.daysInMonth()

  const days = []

  // 调整偏移量，使周一为一周的开始
  const adjustedFirstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1

  // 添加上个月的最后几天
  if (adjustedFirstDayOfWeek > 0) {
    const prevMonth = firstDay.subtract(1, 'month')
    const prevMonthLastDay = prevMonth.daysInMonth()
    for (let i = prevMonthLastDay - adjustedFirstDayOfWeek + 1; i <= prevMonthLastDay; i++) {
      const date = prevMonth.date(i)
      days.push({
        date,
        otherMonth: true,
        shiftInfo: false
      })
    }
  }

  // 添加本月的天数
  for (let i = 1; i <= daysInMonth; i++) {
    const date = firstDay.date(i)
    days.push({
      date,
      otherMonth: false,
      shiftInfo: getShiftInfo(date)
    })
  }

  // 添加下个月的前几天
  const remainingCells = 7 - (days.length % 7)
  if (remainingCells !== 7) {
    const nextMonth = firstDay.add(1, 'month')
    for (let i = 1; i <= remainingCells; i++) {
      const date = nextMonth.date(i)
      days.push({
        date,
        otherMonth: true,
        shiftInfo: false
      })
    }
  }

  return days
})

// 判断是否是今天
const isToday = (date: dayjs.Dayjs) => {
  return date.isSame(dayjs(), 'day')
}

// 判断是否是选中的日期
const isSelected = (date: dayjs.Dayjs) => {
  return selectedDate.value && date.isSame(selectedDate.value, 'day')
}

// 获取值班人员信息
const getShiftInfo = (date: dayjs.Dayjs) => {
  // 这里可以根据日期动态获取值班人员信息
  // 示例：假设特定日期有值班人员信息
  return date.date() === 9 && date.month() + 1 === 2 && date.year() === 2025
}

// 处理年月改变
const handleYearMonthChange = (newYearMonth: number[]) => {
  currentYear.value = newYearMonth[0]
  currentMonth.value = newYearMonth[1]
}

// 处理日期点击
const handleDayClick = (day: dayjs.Dayjs) => {
  if (day.month() + 1 !== currentMonth.value) return // 防止选择其他月份的日期
  selectedDate.value = day
  showPopover.value = true // 显示 popover
}

// 处理日期悬停
const handleDayHover = (day: dayjs.Dayjs) => {
  if (day.month() + 1 === currentMonth.value) {
    day.set('year', currentYear.value)
  }
}

// 关闭弹窗
const closePopover = () => {
  showPopover.value = false
}

// 处理 popover 关闭后的逻辑
const handlePopoverLeave = () => {
  nextTick(() => {
    if (selectedDate.value) {
      const selectedDayIndex = days.value.findIndex((day) =>
        day.date.isSame(selectedDate.value, 'day')
      )
      if (selectedDayIndex !== -1 && dayRefs.value[selectedDayIndex]) {
        dayRefs.value[selectedDayIndex].focus()
      }
    }
  })
}

// 监听 currentYearMonth 变化
watch(currentYearMonth, (newYearMonth) => {
  currentYear.value = newYearMonth[0]
  currentMonth.value = newYearMonth[1]
  if (typeof updateYearMonth === 'function') {
    updateYearMonth(newYearMonth)
  }
})
</script>

<style lang="scss" scoped>
.custom-calendar {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  border-radius: 4px;
  padding: 10px 20px;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .calendar-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 12px;
  }

  .days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    overflow-y: auto;
    flex-grow: 1;
    justify-items: center; //设置日期居中展示
  }

  .day {
    aspect-ratio: 1/1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%; /* 将背景改为圆形 */
    cursor: pointer;
    position: relative;
    font-size: 12px;
    width: 26px; //日期单个框的宽高
    height: 26px;

    &.other-month {
      color: #ccc;
    }

    &.today {
      background-color: #f0f7ff;
      color: #409eff;
      font-weight: bold;
    }

    &.selected {
      background-color: #409eff;
      color: white;
      font-weight: bold;
    }

    &:hover:not(.other-month):not(.selected) {
      background-color: #f5f7fa;
    }

    .shift-info-tag {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%); /* 居中 */
      background-color: #409eff;
      color: white;
      border-radius: 50%;
      width: 12px !important;
      height: 12px !important;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
    }
  }

  :deep(.el-popover) {
    width: 264px;
    height: 156px;
    font-size: 10px;
    border-radius: 4px;
  }

  :deep(.el-table) {
    font-size: 10px;
  }

  :deep(.el-table th) {
    padding: 5px 0;
    font-size: 10px;
  }

  :deep(.el-table td) {
    padding: 3px 0;
    font-size: 10px;
  }

  .popover-content {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .popover-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
  }

  .popover-body {
    flex: 1;
    overflow-y: auto;
  }

  :deep(.selected-date) {
    font-size: 12px;
    font-weight: bold;
    color: #409eff;
  }

  .close-btn {
    cursor: pointer;
    font-weight: bold;
  }
}
:deep(.el-table--default .cell) {
  font-size: 12px;
  padding: 0 12px;
}
</style>
