import { ref, onMounted, onUnmounted } from 'vue'

interface Scale {
  width: number
  height: number
}

export default function useScreenAdapter(
  containerRef: () => HTMLElement,
  baseWidth: number = 1920,
  baseHeight: number = 1080
) {
  const scale = ref<Scale>({ width: 1, height: 1 })
  const timer = ref<number | null>(null)

  const baseProportion = baseWidth / baseHeight

  const calcRate = () => {
    const container = containerRef()
    if (!container) return

    const currentRate = window.innerWidth / window.innerHeight
    let widthScale = 1
    let heightScale = 1

    if (currentRate > baseProportion) {
      // 更宽
      widthScale = window.innerHeight / baseHeight
      heightScale = widthScale
    } else {
      // 更高
      heightScale = window.innerWidth / baseWidth
      widthScale = heightScale
    }

    scale.value = { width: widthScale, height: heightScale }
    container.style.transform = `scale(${widthScale}, ${heightScale})`
    container.style.transformOrigin = '0 0'
  }

  const resize = () => {
    if (timer.value) {
      clearTimeout(timer.value)
    }
    timer.value = window.setTimeout(calcRate, 200)
  }

  onMounted(() => {
    calcRate()
    window.addEventListener('resize', resize)
  })

  onUnmounted(() => {
    if (timer.value) {
      clearTimeout(timer.value)
    }
    window.removeEventListener('resize', resize)
  })

  return {
    scale,
    calcRate
  }
}
