/**
 * 字段默认配置
 * 包含各种字段类型的默认配置值
 */

import type { TextType, DuplicateCheck, DataValidationType } from '../enums/field'

/**
 * 文本字段默认配置
 */
export const defaultTextFieldForm = {
  /** 文本类型 */
  textType: '0' as TextType,
  /** 重复检查 */
  duplicateCheck: '0' as DuplicateCheck,
  /** 数据验证 */
  dataValidation: '0' as DataValidationType,
  /** 正则表达式 */
  regex: '',
  /** 提示信息 */
  prompt: ''
}

/**
 * 数字字段默认配置
 */
export const defaultNumberFieldForm = {
  /** 数字类型 */
  numberType: '0',
  /** 重复检查 */
  duplicateCheck: '0' as DuplicateCheck,
  /** 小数位数 */
  decimalPlaces: '0'
}

/**
 * 日期精度字段默认配置
 */
export const defaultDatePrecisionForm = {
  /** 日期精度 */
  datePrecision: '0',
  /** Code2 */
  code2: ''
}

/**
 * 附件字段默认配置
 */
export const defaultUploadFieldForm = {
  /** 最大尺寸 */
  sizeLimit: 1,
  /** 最大数量 */
  countLimit: 1,
  /** 允许的文件类型 */
  allowedTypes: 'jpeg,bmp,jpg,png,pdf'
}

/**
 * 字段扩展配置默认值
 */
export const defaultFieldConfExt = {
  ...defaultTextFieldForm,
  ...defaultNumberFieldForm,
  ...defaultDatePrecisionForm,
  ...defaultUploadFieldForm
}

// ==================== 类型导出 ====================

/** 文本字段表单类型 */
export type TextFieldForm = typeof defaultTextFieldForm

/** 数字字段表单类型 */
export type NumberFieldForm = typeof defaultNumberFieldForm

/** 附件字段表单类型 */
export type UploadFieldForm = typeof defaultUploadFieldForm

/** 日期精度字段表单类型 */
export type DatePrecisionForm = typeof defaultDatePrecisionForm

/** 字段扩展配置类型 */
export type FieldConfExt = typeof defaultFieldConfExt
