/**
 * 操作配置
 * 包含操作按钮、权限等相关配置
 */

import { OperateType } from '../enums/common'

/**
 * 操作类型文字映射
 */
export const OperateTypeLabels: Record<OperateType, string> = {
  [OperateType.ADD]: '添加',
  [OperateType.EDIT]: '编辑',
  [OperateType.DELETE]: '删除',
  [OperateType.IMPORT]: '导入',
  [OperateType.EXPORT]: '导出',
  [OperateType.VIEW]: '查看'
}

/**
 * 操作按钮配置
 */
export const OperateButtonConfig = {
  [OperateType.ADD]: {
    type: 'primary',
    icon: 'Plus',
    color: '#409EFF'
  },
  [OperateType.EDIT]: {
    type: 'primary',
    icon: 'Edit',
    color: '#409EFF'
  },
  [OperateType.DELETE]: {
    type: 'danger',
    icon: 'Delete',
    color: '#F56C6C'
  },
  [OperateType.IMPORT]: {
    type: 'warning',
    icon: 'Upload',
    color: '#E6A23C'
  },
  [OperateType.EXPORT]: {
    type: 'success',
    icon: 'Download',
    color: '#67C23A'
  },
  [OperateType.VIEW]: {
    type: 'info',
    icon: 'View',
    color: '#909399'
  }
} as const

/**
 * 权限操作映射
 */
export const PermissionActions = {
  create: 'create',
  read: 'read',
  update: 'update',
  delete: 'delete',
  export: 'export',
  import: 'import'
} as const

/**
 * 操作权限映射
 */
export const OperatePermissionMap = {
  [OperateType.ADD]: PermissionActions.create,
  [OperateType.EDIT]: PermissionActions.update,
  [OperateType.DELETE]: PermissionActions.delete,
  [OperateType.IMPORT]: PermissionActions.import,
  [OperateType.EXPORT]: PermissionActions.export,
  [OperateType.VIEW]: PermissionActions.read
} as const

/**
 * 获取操作按钮配置
 */
export const getOperateButtonConfig = (type: OperateType) => {
  return {
    label: OperateTypeLabels[type],
    ...OperateButtonConfig[type]
  }
}

/**
 * 获取操作权限
 */
export const getOperatePermission = (type: OperateType): string => {
  return OperatePermissionMap[type]
}

// ==================== 兼容性导出 ====================

/** @deprecated 请使用 OperateTypeLabels */
export const OperateTypeText = OperateTypeLabels
