/**
 * 验证配置
 * 包含各种验证规则和正则表达式
 */

/**
 * 正则表达式配置
 */
export const ValidationPatterns = {
  // 身份证号
  idCard: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  
  // 手机号
  mobile: /^1[3-9]\d{9}$/,
  
  // 电话号码
  phone: /^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/,
  
  // 统一社会信用代码
  uscc: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
  
  // 邮箱
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  
  // 网址
  url: /^https?:\/\/(([a-zA-Z0-9_-])+(\.)?)*(:\d+)?(\/((\.)?(\?)?=?&?[a-zA-Z0-9_-](\?)?)*)*$/i,
  
  // 中文字符
  chinese: /^[\u4e00-\u9fa5]+$/,
  
  // 英文字符
  english: /^[a-zA-Z]+$/,
  
  // 数字
  number: /^\d+$/,
  
  // 小数
  decimal: /^\d+(\.\d+)?$/,
  
  // 正整数
  positiveInteger: /^[1-9]\d*$/,
  
  // 非负整数
  nonNegativeInteger: /^(0|[1-9]\d*)$/
} as const

/**
 * 验证错误消息模板
 */
export const ValidationMessages = {
  required: '请输入{field}',
  pattern: '{field}格式不正确',
  min: '{field}不能少于{min}个字符',
  max: '{field}不能超过{max}个字符',
  range: '{field}长度应在{min}-{max}个字符之间',
  email: '请输入正确的邮箱地址',
  mobile: '请输入正确的手机号码',
  phone: '请输入正确的电话号码',
  idCard: '请输入正确的身份证号码',
  uscc: '请输入正确的统一社会信用代码',
  url: '请输入正确的网址',
  number: '请输入数字',
  positiveNumber: '请输入正数',
  integer: '请输入整数',
  positiveInteger: '请输入正整数'
} as const

/**
 * 字段验证规则映射
 */
export const FieldValidationRules = {
  // 文本字段验证
  text: {
    required: true,
    trigger: 'blur'
  },
  
  // 数字字段验证
  number: {
    required: true,
    pattern: ValidationPatterns.decimal,
    message: ValidationMessages.number,
    trigger: 'blur'
  },
  
  // 邮箱验证
  email: {
    pattern: ValidationPatterns.email,
    message: ValidationMessages.email,
    trigger: 'blur'
  },
  
  // 手机号验证
  mobile: {
    pattern: ValidationPatterns.mobile,
    message: ValidationMessages.mobile,
    trigger: 'blur'
  },
  
  // 身份证验证
  idCard: {
    pattern: ValidationPatterns.idCard,
    message: ValidationMessages.idCard,
    trigger: 'blur'
  },
  
  // 统一社会信用代码验证
  uscc: {
    pattern: ValidationPatterns.uscc,
    message: ValidationMessages.uscc,
    trigger: 'blur'
  }
} as const

/**
 * 数据验证类型映射
 */
export const DataValidationTypeMap = {
  '0': null,                                    // 不校验
  '1': 'custom',                               // 自定义正则
  '2': ValidationPatterns.idCard,              // 身份证
  '3': ValidationPatterns.uscc,                // 统一社会信用代码
  '4': ValidationPatterns.mobile,              // 手机号
  '5': ValidationPatterns.phone                // 电话号码
} as const

/**
 * 获取验证错误消息
 */
export const getValidationMessage = (type: string, field: string, options?: Record<string, any>): string => {
  const template = ValidationMessages[type as keyof typeof ValidationMessages] || ValidationMessages.pattern
  let message = template.replace('{field}', field)
  
  if (options) {
    Object.keys(options).forEach(key => {
      message = message.replace(`{${key}}`, options[key])
    })
  }
  
  return message
}

/**
 * 创建必填验证规则
 */
export const createRequiredRule = (field: string, trigger = 'blur') => ({
  required: true,
  message: getValidationMessage('required', field),
  trigger
})

/**
 * 创建正则验证规则
 */
export const createPatternRule = (pattern: RegExp, field: string, trigger = 'blur') => ({
  pattern,
  message: getValidationMessage('pattern', field),
  trigger
})

/**
 * 创建长度验证规则
 */
export const createLengthRule = (min: number, max: number, field: string, trigger = 'blur') => ({
  min,
  max,
  message: getValidationMessage('range', field, { min, max }),
  trigger
})
