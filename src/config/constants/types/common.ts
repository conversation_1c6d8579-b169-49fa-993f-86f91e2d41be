/**
 * 通用类型定义
 * 包含项目中通用的类型接口
 */

/**
 * 基础选项接口
 */
export interface BaseOption {
  label: string
  value: string | number
  disabled?: boolean
}

/**
 * 树形选项接口
 */
export interface TreeOption extends BaseOption {
  children?: TreeOption[]
}

/**
 * 分页参数接口
 */
export interface PaginationParams {
  pageNo: number
  pageSize: number
  [key: string]: any
}

/**
 * 分页响应接口
 */
export interface PaginationResponse<T = any> {
  list: T[]
  total: number
  pageNo: number
  pageSize: number
}

/**
 * API 响应接口
 */
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
  success?: boolean
}

/**
 * 表格列配置接口
 */
export interface TableColumn {
  prop: string
  label: string
  width?: number | string
  minWidth?: number | string
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  align?: 'left' | 'center' | 'right'
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
  [key: string]: any
}

/**
 * 表格数据项接口
 */
export interface TableDataItem {
  id: string
  [key: string]: any
}

/**
 * 操作按钮配置接口
 */
export interface ActionButton {
  label: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  icon?: string
  disabled?: boolean
  loading?: boolean
  onClick: (row: TableDataItem) => void
}

/**
 * 搜索表单数据接口
 */
export interface SearchFormData {
  keyword?: string
  dateRange?: [string, string]
  status?: string | number
  [key: string]: any
}

/**
 * 树节点数据接口
 */
export interface TreeNodeData {
  id: string
  label: string
  value: string
  children?: TreeNodeData[]
  [key: string]: any
}

/**
 * 文件上传配置接口
 */
export interface UploadConfig {
  accept?: string
  multiple?: boolean
  limit?: number
  sizeLimit?: number
  autoUpload?: boolean
}

/**
 * 表单验证规则接口
 */
export interface ValidationRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: any) => void
}
