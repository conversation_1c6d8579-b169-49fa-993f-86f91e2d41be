/**
 * 用户相关类型定义
 * 包含用户信息、权限、角色等相关类型
 */

import type { UserStatus, PostStatus, ViewLevel } from '../enums/user'

/**
 * 用户基础信息接口
 */
export interface UserInfo {
  id: string
  username: string
  nickname?: string
  email?: string
  mobile?: string
  avatar?: string
  status: UserStatus
  createTime?: string
  updateTime?: string
}

/**
 * 用户详细信息接口
 */
export interface UserDetail extends UserInfo {
  roles?: Role[]
  permissions?: string[]
  deptId?: string
  deptName?: string
  postIds?: string[]
  postNames?: string[]
}

/**
 * 角色信息接口
 */
export interface Role {
  id: string
  name: string
  code: string
  description?: string
  status: number
  createTime?: string
}

/**
 * 权限信息接口
 */
export interface Permission {
  id: string
  name: string
  code: string
  type: 'menu' | 'button' | 'api'
  parentId?: string
  path?: string
  component?: string
  icon?: string
  sort?: number
  status: number
}

/**
 * 部门信息接口
 */
export interface Department {
  id: string
  name: string
  parentId?: string
  code?: string
  leader?: string
  phone?: string
  email?: string
  status: number
  sort?: number
  children?: Department[]
}

/**
 * 岗位信息接口
 */
export interface Post {
  id: string
  name: string
  code: string
  sort?: number
  status: PostStatus
  remark?: string
}

/**
 * 用户查询参数接口
 */
export interface UserQueryParams {
  username?: string
  nickname?: string
  mobile?: string
  email?: string
  status?: UserStatus
  deptId?: string
  createTime?: [string, string]
  pageNo?: number
  pageSize?: number
}

/**
 * 用户创建/更新参数接口
 */
export interface UserFormData {
  id?: string
  username: string
  nickname?: string
  password?: string
  email?: string
  mobile?: string
  avatar?: string
  status: UserStatus
  deptId?: string
  postIds?: string[]
  roleIds?: string[]
  remark?: string
}
