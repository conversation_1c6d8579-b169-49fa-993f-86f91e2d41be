/**
 * 字段相关类型定义
 * 包含字段配置、扩展配置等相关类型
 */

import type { FormRules } from 'element-plus'
import type { FieldType, TextType, DuplicateCheck, DataValidationType } from '../enums/field'
import type { BooleanEnum, BusinessType } from '../enums/common'
import type { BaseOption } from './common'

/**
 * 字段扩展配置接口
 */
export interface FieldConfExt {
  id?: string
  name: string
  value: any
  type?: number
  optionsJson?: BaseOption[]
}

/**
 * 字段配置接口
 */
export interface LabelFieldConfig {
  /** 字段UUID，前端临时处理 */
  uuid?: string
  /** 字段ID */
  id?: string
  /** 管理ID */
  manageId: string
  /** 字段编码 */
  code: string
  /** 字段名称 */
  name: string
  /** 备注信息 */
  remark: string
  /** 字段类型 */
  fieldType: FieldType
  /** 字段业务类型 */
  bizType: BusinessType
  /** 字段长度 */
  length?: number
  /** 是否加密 */
  encFlag: BooleanEnum
  /** 加密类型 */
  encType: BooleanEnum
  /** 是否新增表单 */
  addFlag: BooleanEnum
  /** 是否编辑表单 */
  editFlag: BooleanEnum
  /** 移动端是否可见 */
  appViewFlag: BooleanEnum
  /** PC端是否可见 */
  pcViewFlag: BooleanEnum
  /** 字段配置详情列表 */
  fieldConfExtDOList: FieldConfExt[]
  /** 父级字段编码 */
  parentCode: string
}

/**
 * 带可见性配置的字段接口
 */
export interface FieldConfigWithVisibility extends LabelFieldConfig {
  linkage?: {
    enabled: boolean
    targetFieldId: string
    targetFieldValue: string
    condition: 'equals' | 'notEquals' | 'contains' | 'notContains' | 'startsWith' | 'endsWith'
  }
  value?: any
}

/**
 * 字段组接口
 */
export interface FieldGroup {
  id: string
  name?: string
  fields: LabelFieldConfig[]
}

/**
 * 文本字段表单类型
 */
export interface TextFieldForm {
  textType: TextType
  duplicateCheck: DuplicateCheck
  dataValidation: DataValidationType
  regex: string
  prompt: string
}

/**
 * 数字字段表单类型
 */
export interface NumberFieldForm {
  numberType: string
  duplicateCheck: DuplicateCheck
  decimalPlaces: string
}

/**
 * 日期精度字段表单类型
 */
export interface DatePrecisionForm {
  datePrecision: string
  code2: string
}

/**
 * 附件字段表单类型
 */
export interface UploadFieldForm {
  sizeLimit: number
  countLimit: number
  allowedTypes: string
}

/**
 * 表单渲染对象接口
 */
export interface FormRenderItem {
  formData: Record<string, any>
  nodeId: string
  formRule: FormRules
  formRenderData: FieldGroup[]
}

// ==================== 兼容性导出 ====================

/** @deprecated 请使用 LabelFieldConfig */
export type FieldConfig = LabelFieldConfig


export type LabelDragField = LabelFieldConfig & {
  label: string
  id: string
  fieldConfExtObj: {
    options?: any[],
    value?: any,
    name?: string
  }
  type: 'placeholder' | 'field'  // 占位符 字段 ？？？ 我忘了什么意思了
  linkage: {
    enabled: boolean
    targetFieldId: string | null
    targetFieldValue: any | null
    effect: 'show' | 'hide'
    condition: 'equals' | 'not_equals'
  }
}