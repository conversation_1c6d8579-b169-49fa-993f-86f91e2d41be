/**
 * 高德地图选择器组件类型定义
 */

// 位置数据接口
export interface LocationData {
  lng: number        // 经度
  lat: number        // 纬度
  address: string    // 详细地址
  name?: string      // 位置名称（可选）
}

// 搜索结果接口
export interface SearchResult {
  name: string                    // 地点名称
  address: string                 // 详细地址
  district: string                // 行政区域
  location: [number, number]      // 经纬度坐标
  value?: string                  // 用于自动完成的显示值
}

// 组件Props接口
export interface AMapSelectorProps {
  modelValue?: LocationData       // 绑定值
  placeholder?: string            // 输入框占位符
  dialogTitle?: string            // 弹窗标题
  dialogWidth?: string            // 弹窗宽度
  mapHeight?: string              // 地图高度
  amapKey?: string                // 高德地图API密钥（可选，不传则使用环境变量）
  defaultCenter?: [number, number] // 默认中心点
  defaultZoom?: number            // 默认缩放级别
  disabled?: boolean              // 是否禁用
  readonly?: boolean              // 是否只读
  clearable?: boolean             // 是否可清空
}

// 组件Emits接口
export interface AMapSelectorEmits {
  (e: 'update:modelValue', value: LocationData): void
  (e: 'change', value: LocationData): void
}

// 高德地图相关类型（简化版）
export interface AMapInstance {
  setCenter: (center: [number, number]) => void
  setZoom: (zoom: number) => void
  on: (event: string, callback: Function) => void
  add: (overlay: any) => void
  remove: (overlay: any) => void
  destroy: () => void
}

export interface AMapMarker {
  setPosition: (position: [number, number]) => void
  on: (event: string, callback: Function) => void
}

export interface AMapGeocoder {
  getAddress: (
    position: [number, number],
    callback: (status: string, result: any) => void
  ) => void
}

export interface AMapAutoComplete {
  search: (
    keyword: string,
    callback: (status: string, result: any) => void
  ) => void
}

// 地图点击事件类型
export interface MapClickEvent {
  lnglat: {
    lng: number
    lat: number
  }
}

// 标记拖拽事件类型
export interface MarkerDragEvent {
  lnglat: {
    lng: number
    lat: number
  }
}
