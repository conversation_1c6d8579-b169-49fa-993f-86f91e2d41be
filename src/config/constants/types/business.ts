/**
 * 业务相关类型定义
 * 包含标签、操作、展示等业务相关类型
 */

import type { OperateType, BusinessType } from '../enums/common'
import type { TagType, OperatorType } from '../enums/business'

/**
 * 标签信息接口
 */
export interface TagInfo {
  id: string
  name: string
  type?: TagType
  parentId?: string
  children?: TagInfo[]
}

/**
 * 标签表单数据接口
 */
export interface TagFormData {
  nodeId: string
  tagName: string
  formData: Record<string, any>
}

/**
 * 操作配置接口
 */
export interface OperateConfig {
  id: number
  manageId: number
  operateName: string
  operateType: OperateType
  showFlag: number
  sort: number
}

/**
 * 操作符选项接口
 */
export interface OperatorOption {
  label: string
  value: OperatorType
}

/**
 * 业务数据接口
 */
export interface BusinessData {
  id?: string
  manageId?: string
  [key: string]: any
}

/**
 * 搜索条件接口
 */
export interface SearchCondition {
  fieldCode: string
  operator: OperatorType
  value: any
  logic?: 'AND' | 'OR'
}

/**
 * 业务数据列表请求接口
 */
export interface BusinessDataListRequest {
  manageId: string
  pageNo?: number
  pageSize?: number
  conditions?: SearchCondition[]
  keyword?: string
  sortField?: string
  sortOrder?: 'ASC' | 'DESC'
}

/**
 * 统计项接口
 */
export interface StatisticItem {
  id?: string
  uuid: string
  name: string
  fieldId?: string
  type?: number
  data?: string
  bizType?: BusinessType
  filterType?: number
  value?: number | string
  fields?: any[]
}

/**
 * 查询配置项接口
 */
export interface QueryConfigItem {
  id: string
  fieldCode: string
  fieldName: string
  fieldType: number
  required: boolean
  defaultValue?: any
  options?: any[]
}

/**
 * 展示字段配置接口
 */
export interface ExhibitionFieldConfig {
  key: string
  label: string
  fields: string[]
}

/**
 * 敏感密码表单接口
 */
export interface SensitivePasswordForm {
  password: string
}

// ==================== 兼容性导出 ====================

// 重新导出 API 相关类型
export type { QueryResItem as SearchFieldConfig } from '@/api/system/data/query-conf'
export type { DictDataVO as DictOption } from '@/api/system/dict/dict.data'
