/**
 * 通用枚举定义
 * 包含项目中通用的枚举类型
 */

/**
 * 是否枚举
 */
export enum YesNoEnum {
  YES = '是',
  NO = '否'
}

/**
 * 布尔值枚举（数字类型）
 */
export enum BooleanEnum {
  TRUE = 1,   // 是/启用/显示
  FALSE = 0   // 否/禁用/隐藏
}

/**
 * 状态枚举
 */
export enum StatusEnum {
  ENABLED = 1,    // 启用
  DISABLED = 0    // 禁用
}

/**
 * 显示状态枚举
 */
export enum ShowEnum {
  SHOW = 1,       // 显示
  HIDE = 0        // 隐藏
}

/**
 * 操作类型枚举
 */
export enum OperateType {
  ADD = 0,        // 添加
  EDIT = 1,       // 编辑
  DELETE = 2,     // 删除
  IMPORT = 3,     // 导入
  EXPORT = 4,     // 导出
  VIEW = 5        // 查看
}

/**
 * 表单类型枚举
 */
export enum FormType {
  CREATE = 0,     // 新增
  EDIT = 1,       // 编辑
  DETAIL = 2      // 详情
}

/**
 * 业务类型枚举
 */
export enum BusinessType {
  SYSTEM = 0,     // 系统
  BASIC = 1,      // 基础
  BUSINESS = 2    // 业务
}

// ==================== 选项配置 ====================

/**
 * 是否选项
 */
export const YesNoOptions = [
  { label: YesNoEnum.YES, value: BooleanEnum.TRUE },
  { label: YesNoEnum.NO, value: BooleanEnum.FALSE }
] as const

/**
 * 布尔选项
 */
export const BooleanOptions = [
  { label: '是', value: BooleanEnum.TRUE },
  { label: '否', value: BooleanEnum.FALSE }
] as const

/**
 * 状态选项
 */
export const StatusOptions = [
  { label: '启用', value: StatusEnum.ENABLED },
  { label: '禁用', value: StatusEnum.DISABLED }
] as const

/**
 * 显示选项
 */
export const ShowOptions = [
  { label: '显示', value: ShowEnum.SHOW },
  { label: '隐藏', value: ShowEnum.HIDE }
] as const

/**
 * 操作类型选项
 */
export const OperateTypeOptions = [
  { label: '添加', value: OperateType.ADD },
  { label: '编辑', value: OperateType.EDIT },
  { label: '删除', value: OperateType.DELETE },
  { label: '导入', value: OperateType.IMPORT },
  { label: '导出', value: OperateType.EXPORT },
  { label: '查看', value: OperateType.VIEW }
] as const

/**
 * 表单类型选项
 */
export const FormTypeOptions = [
  { label: '新增', value: FormType.CREATE },
  { label: '编辑', value: FormType.EDIT },
  { label: '详情', value: FormType.DETAIL }
] as const

/**
 * 业务类型选项
 */
export const BusinessTypeOptions = [
  { label: '系统', value: BusinessType.SYSTEM },
  { label: '基础', value: BusinessType.BASIC },
  { label: '业务', value: BusinessType.BUSINESS }
] as const

// ==================== 标签映射 ====================

/**
 * 操作类型标签映射
 */
export const OperateTypeLabels: Record<OperateType, string> = {
  [OperateType.ADD]: '添加',
  [OperateType.EDIT]: '编辑',
  [OperateType.DELETE]: '删除',
  [OperateType.IMPORT]: '导入',
  [OperateType.EXPORT]: '导出',
  [OperateType.VIEW]: '查看'
}

/**
 * 业务类型标签映射
 */
export const BusinessTypeLabels: Record<BusinessType, string> = {
  [BusinessType.SYSTEM]: '系统',
  [BusinessType.BASIC]: '基础',
  [BusinessType.BUSINESS]: '业务'
}
