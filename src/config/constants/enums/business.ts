/**
 * 业务相关枚举定义
 * 包含标签、操作符、展示等业务相关枚举
 */

// ==================== 标签相关 ====================

/**
 * 标签类型枚举
 */
export enum TagType {
  BASIC = 0,      // 基础标签
  BUSINESS = 1    // 业务标签
}

/**
 * 标签类型选项
 */
export const TagTypeOptions = [
  { label: '基础标签', value: TagType.BASIC },
  { label: '业务标签', value: TagType.BUSINESS }
] as const

// ==================== 操作符相关 ====================

/**
 * 操作符类型枚举
 */
export enum OperatorType {
  EQUALS = 1,         // 等于
  NOT_EQUALS = 0      // 不等于
}

/**
 * 操作符选项
 */
export const OperatorOptions = [
  { label: '等于', value: OperatorType.EQUALS },
  { label: '不等于', value: OperatorType.NOT_EQUALS }
] as const

// ==================== 展示相关 ====================

/**
 * 展示操作接口
 */
export interface ExhibitionOperate {
  id: string
  manageId: string
  operateName: string
  operateType: number
  showFlag: number
  sort: number
  type: string
}

// ==================== 兼容性导出 ====================
// 从原有文件迁移的内容，保持向后兼容

// 重新导出通用枚举（从 common.ts）
export { 
  BooleanEnum, 
  YesNoEnum, 
  OperateType, 
  FormType as ViewFormType,
  BusinessType,
  BooleanOptions,
  YesNoOptions,
  OperateTypeOptions,
  OperateTypeLabels,
  BusinessTypeOptions,
  BusinessTypeLabels
} from './common'

/**
 * 操作符选项类型接口
 */
export interface OperatorOption {
  label: string
  value: number
}
