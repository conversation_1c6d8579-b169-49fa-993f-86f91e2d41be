/**
 * 业务配置
 * 包含项目特定的业务逻辑配置
 */

// 展示字段配置（从 exhibitionFieldConfig.ts 迁移）
export const exhibitionFieldConfig = [
  {
    key: 'people',
    label: '人口',
    fields: ['type', 'idCard'], // 证件类型、证件号码
    submitButtonText: '添加',
    showCloseButton: true,
    closeButtonText: '暂不添加', // 该人口已入库  编辑人口信息 （跳转编辑页面）
    editButtonText: '编辑人口信息', // 人口： 该人口未入库  添加（跳转新建）、暂不添加（关闭弹窗）
    existsText: '该人口已入库',
    notExistsText: '该人口未入库'
  },
  {
    key: 'place',
    label: '场所',
    fields: ['uscc'], // 统一信用代码
    submitButtonText: '添加',
    showCloseButton: true,
    closeButtonText: '暂不添加',
    editButtonText: '编辑房屋信息', // 房屋：添加（跳转新建）、暂不添加（关闭弹窗）、 编辑房信息（编辑页面）
    existsText: '该场所已入库',
    notExistsText: '该场所未入库'
  },
  {
    key: 'property',
    label: '物地',
    fields: ['code'], // 编码
    submitButtonText: '添加',
    showCloseButton: true,
    closeButtonText: '暂不添加',
    editButtonText: '编辑物地信息',
    existsText: '该物地已入库',
    notExistsText: '该物地未入库'
  },
  {
    key: 'ybss',
    label: '一标三实',
    fields: ['id'], // 编码
    submitButtonText: '添加',
    showCloseButton: true,
    closeButtonText: '暂不添加',
    editButtonText: '编辑标准地址信息', // 一标三识：添加（跳转新建）、暂不添加（关闭弹窗）、 编辑标准地址信息（编辑页面
    existsText: '该标准地址已入库',
    notExistsText: '该标准地址未入库'
  }
] as const

// 导出类型
export type ExhibitionFieldConfig = typeof exhibitionFieldConfig[number]
