import { FieldType } from '@/config/constants/enums/field'
import { BusinessType } from '@/config/constants/enums/business'

/**
 * 表单处理工具类
 * 提供静态方法处理表单相关逻辑
 */
export class FormProcessor {
  // 字段类型常量
  static readonly FIELD_TYPES = {
    CHECKBOX: FieldType.CHECKBOX,
    RADIO: FieldType.RADIO,
    DATE_RANGE: FieldType.DATE_RANGE,
    TEXT: FieldType.TEXT,
    NUMBER: FieldType.NUMBER
  } as const

  /**
   * 标准化字段扩展配置
   */
  static normalizeFieldExtConfig(field: any): void {
    if (Array.isArray(field.fieldConfExtDOList)) {
      field.fieldConfExtDOList.forEach((item: any) => {
        item.type = item.type == null ? 0 : Number(item.type)
      })
    }
  }

  /**
   * 处理系统字段禁用状态
   */
  static processSystemFieldDisabled(field: any, disabledKeys: string[]): void {
    if (field.bizType === BusinessType.SYSTEM.toString()) {
      field.fieldConfExtDOList = field.fieldConfExtDOList || []
      field.fieldConfExtDOList.push({
        name: 'disabled',
        value: disabledKeys.includes(field.code),
      })
    }
  }

  /**
   * 根据字段类型初始化字段值
   */
  static initFieldValue(field: any, sourceValue: any, storeData?: any, businessData?: any): any {
    const { fieldType, fieldConfExtDOList, code } = field

    switch (fieldType) {
      case this.FIELD_TYPES.CHECKBOX:
        return sourceValue ? String(sourceValue).split(',') : []

      case this.FIELD_TYPES.RADIO:
        return sourceValue?.toString() || ''

      case this.FIELD_TYPES.DATE_RANGE:
        const code2 = fieldConfExtDOList?.find((item: any) => item.name === 'code2')?.value || ''
        const code2Value = (storeData?.[code2]) || businessData?.[code2] || ''
        return {
          [code]: [sourceValue, code2Value],
          [code2]: code2Value
        }

      default:
        return sourceValue || ''
    }
  }

  /**
   * 处理表单提交时的数据转换
   */
  static processSubmitValue(field: any, formValue: any): Record<string, any> {
    const { fieldType, fieldConfExtDOList, code } = field
    const result: Record<string, any> = {}

    switch (fieldType) {
      case this.FIELD_TYPES.CHECKBOX:
        result[code] = Array.isArray(formValue) ? formValue.join(',') : ''
        break

      case this.FIELD_TYPES.DATE_RANGE:
        if (Array.isArray(formValue) && formValue.length === 2) {
          result[code] = formValue[0]
          const code2 = fieldConfExtDOList?.find((item: any) => item.name === 'code2')?.value
          if (code2) {
            result[code2] = formValue[1]
          }
        }
        break

      default:
        result[code] = formValue
    }

    return result
  }

  /**
   * 批量处理字段组
   */
  static processFieldGroups(
    fieldGroups: any[],
    options: {
      disabledKeys?: string[]
      normalizeExtConfig?: boolean
      processSystemFields?: boolean
    } = {}
  ): any[] {
    const { disabledKeys = [], normalizeExtConfig = true, processSystemFields = true } = options

    return fieldGroups.map(group => ({
      ...group,
      fields: group.fields.map((field: any) => {
        // 标准化扩展配置
        if (normalizeExtConfig) {
          this.normalizeFieldExtConfig(field)
        }

        // 处理系统字段
        if (processSystemFields) {
          this.processSystemFieldDisabled(field, disabledKeys)
        }

        return field
      })
    }))
  }

  /**
   * 批量初始化表单数据
   */
  static initFormData(
    fieldGroups: any[],
    storeData: any = {},
    businessData: any = {}
  ): Record<string, any> {
    const formData: Record<string, any> = {}

    fieldGroups.forEach(group => {
      group.fields.forEach((field: any) => {
        const { code } = field
        
        // 获取字段值（优先级：store > business > 默认）
        let sourceValue = ''
        if (storeData && Object.prototype.hasOwnProperty.call(storeData, code)) {
          sourceValue = storeData[code]
        } else if (businessData && businessData[code] !== undefined) {
          sourceValue = businessData[code]
        }

        // 根据字段类型处理值
        const fieldValue = this.initFieldValue(field, sourceValue, storeData, businessData)
        
        if (field.fieldType === this.FIELD_TYPES.DATE_RANGE && typeof fieldValue === 'object') {
          Object.assign(formData, fieldValue)
        } else {
          formData[code] = fieldValue
        }
      })
    })

    return formData
  }

  /**
   * 批量处理提交数据
   */
  static processSubmitData(
    fieldGroups: any[],
    formData: Record<string, any>
  ): Record<string, any> {
    const businessJson: Record<string, any> = {}

    fieldGroups.forEach(group => {
      group.fields.forEach((field: any) => {
        const { code } = field
        const formValue = formData[code]
        const processedValue = this.processSubmitValue(field, formValue)
        Object.assign(businessJson, processedValue)
      })
    })

    return businessJson
  }

  /**
   * 检查字段是否需要选项数据
   */
  static needsOptions(field: any): boolean {
    return field.fieldType === this.FIELD_TYPES.RADIO || 
           field.fieldType === this.FIELD_TYPES.CHECKBOX
  }

  /**
   * 获取字段的扩展配置值
   */
  static getFieldExtValue(field: any, extName: string): any {
    return field.fieldConfExtDOList?.find((item: any) => item.name === extName)?.value
  }

  /**
   * 检查字段是否为必填
   */
  static isRequired(field: any): boolean {
    return Boolean(field.required)
  }

  /**
   * 检查字段是否被禁用
   */
  static isDisabled(field: any): boolean {
    return Boolean(this.getFieldExtValue(field, 'disabled'))
  }

  /**
   * 获取字段显示名称
   */
  static getFieldDisplayName(field: any): string {
    return field.name || field.fieldName || field.code || '未知字段'
  }

  /**
   * 深度克隆字段配置（避免修改原始数据）
   */
  static cloneFieldConfig(fieldConfig: any): any {
    return JSON.parse(JSON.stringify(fieldConfig))
  }

  /**
   * 验证字段值是否有效
   */
  static validateFieldValue(field: any, value: any): { valid: boolean; message?: string } {
    // 必填验证
    if (this.isRequired(field)) {
      if (value === null || value === undefined || value === '') {
        return { valid: false, message: `${this.getFieldDisplayName(field)}不能为空` }
      }
      
      if (field.fieldType === this.FIELD_TYPES.CHECKBOX && Array.isArray(value) && value.length === 0) {
        return { valid: false, message: `${this.getFieldDisplayName(field)}至少选择一项` }
      }
    }

    return { valid: true }
  }
}

/**
 * 表单处理工具函数（函数式API）
 */
export const formUtils = {
  /**
   * 快速初始化表单数据
   */
  quickInit: (fieldGroups: any[], storeData?: any, businessData?: any) => {
    return FormProcessor.initFormData(fieldGroups, storeData, businessData)
  },

  /**
   * 快速处理提交数据
   */
  quickSubmit: (fieldGroups: any[], formData: Record<string, any>) => {
    return FormProcessor.processSubmitData(fieldGroups, formData)
  },

  /**
   * 快速处理字段组
   */
  quickProcess: (fieldGroups: any[], disabledKeys?: string[]) => {
    return FormProcessor.processFieldGroups(fieldGroups, { disabledKeys })
  }
}

export default FormProcessor
