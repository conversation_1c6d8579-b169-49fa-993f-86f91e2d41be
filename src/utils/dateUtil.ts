/**
 * 日期时间工具函数集合
 * 基于 dayjs 的独立时间操作工具，便于后续切换到其他日期库
 *
 * @description 提供常用的日期格式化、计算、验证等功能
 */

import dayjs, { type ConfigType, type Dayjs } from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import duration from 'dayjs/plugin/duration'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'

// 扩展 dayjs 插件
dayjs.extend(relativeTime)
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(duration)
dayjs.extend(isSameOrBefore)
dayjs.extend(isSameOrAfter)

// 设置中文语言
dayjs.locale('zh-cn')

// ========== 常量定义 ==========

/**
 * 常用日期时间格式常量
 */
export const DATE_TIME_FORMATS = {
  /** 完整日期时间格式：YYYY-MM-DD HH:mm:ss */
  FULL: 'YYYY-MM-DD HH:mm:ss',
  /** 日期格式：YYYY-MM-DD */
  DATE: 'YYYY-MM-DD',
  /** 时间格式：HH:mm:ss */
  TIME: 'HH:mm:ss',
  /** 年月格式：YYYY-MM */
  YEAR_MONTH: 'YYYY-MM',
  /** 月日格式：MM-DD */
  MONTH_DAY: 'MM-DD',
  /** 小时分钟格式：HH:mm */
  HOUR_MINUTE: 'HH:mm',
  /** 中文日期格式：YYYY年MM月DD日 */
  DATE_CN: 'YYYY年MM月DD日',
  /** 中文完整格式：YYYY年MM月DD日 HH时mm分ss秒 */
  FULL_CN: 'YYYY年MM月DD日 HH时mm分ss秒',
  /** ISO 格式 */
  ISO: 'YYYY-MM-DDTHH:mm:ss.SSSZ'
} as const

// 默认格式
const DATE_TIME_FORMAT = DATE_TIME_FORMATS.FULL
const DATE_FORMAT = DATE_TIME_FORMATS.DATE

// ========== 类型定义 ==========

/**
 * 日期输入类型
 */
export type DateInput = ConfigType

/**
 * 日期格式类型
 */
export type DateFormat = keyof typeof DATE_TIME_FORMATS | string

// ========== 基础格式化函数 ==========

/**
 * 格式化日期时间
 * @param date - 日期输入，支持多种格式
 * @param format - 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期时间字符串
 * @example
 * formatToDateTime() // 返回当前时间: '2023-12-01 14:30:00'
 * formatToDateTime(new Date(), 'YYYY年MM月DD日') // 返回: '2023年12月01日'
 */
export function formatToDateTime(date?: DateInput, format: string = DATE_TIME_FORMAT): string {
  return dayjs(date).format(format)
}

/**
 * 格式化为日期
 * @param date - 日期输入，支持多种格式
 * @param format - 格式化模板，默认为 'YYYY-MM-DD'
 * @returns 格式化后的日期字符串
 * @example
 * formatToDate() // 返回当前日期: '2023-12-01'
 * formatToDate(new Date(), 'MM/DD/YYYY') // 返回: '12/01/2023'
 */
export function formatToDate(date?: DateInput, format: string = DATE_FORMAT): string {
  return dayjs(date).format(format)
}

/**
 * 格式化为时间
 * @param date - 日期输入，支持多种格式
 * @param format - 格式化模板，默认为 'HH:mm:ss'
 * @returns 格式化后的时间字符串
 * @example
 * formatToTime() // 返回当前时间: '14:30:00'
 * formatToTime(new Date(), 'HH:mm') // 返回: '14:30'
 */
export function formatToTime(date?: DateInput, format: string = DATE_TIME_FORMATS.TIME): string {
  return dayjs(date).format(format)
}

// ========== 相对时间函数 ==========

/**
 * 获取相对时间描述
 * @param date - 日期输入
 * @returns 相对时间描述，如 '2小时前'、'3天后'
 * @example
 * getRelativeTime(dayjs().subtract(2, 'hour')) // 返回: '2小时前'
 * getRelativeTime(dayjs().add(3, 'day')) // 返回: '3天后'
 */
export function getRelativeTime(date: DateInput): string {
  return dayjs(date).fromNow()
}

/**
 * 获取两个日期之间的相对时间
 * @param date1 - 第一个日期
 * @param date2 - 第二个日期，默认为当前时间
 * @returns 相对时间描述
 * @example
 * getRelativeTimeBetween('2023-12-01', '2023-12-03') // 返回: '2天前'
 */
export function getRelativeTimeBetween(date1: DateInput, date2?: DateInput): string {
  return dayjs(date1).from(dayjs(date2))
}

// ========== 日期计算函数 ==========

/**
 * 添加时间
 * @param date - 基础日期
 * @param value - 要添加的数值
 * @param unit - 时间单位
 * @returns 新的日期对象
 * @example
 * addTime(new Date(), 7, 'day') // 7天后
 * addTime('2023-12-01', 2, 'month') // 2个月后
 */
export function addTime(date: DateInput, value: number, unit: dayjs.ManipulateType): Dayjs {
  return dayjs(date).add(value, unit)
}

/**
 * 减少时间
 * @param date - 基础日期
 * @param value - 要减少的数值
 * @param unit - 时间单位
 * @returns 新的日期对象
 * @example
 * subtractTime(new Date(), 7, 'day') // 7天前
 * subtractTime('2023-12-01', 2, 'month') // 2个月前
 */
export function subtractTime(date: DateInput, value: number, unit: dayjs.ManipulateType): Dayjs {
  return dayjs(date).subtract(value, unit)
}

/**
 * 获取两个日期之间的差值
 * @param date1 - 第一个日期
 * @param date2 - 第二个日期
 * @param unit - 返回的时间单位，默认为毫秒
 * @returns 时间差值
 * @example
 * getDifference('2023-12-03', '2023-12-01', 'day') // 返回: 2
 */
export function getDifference(date1: DateInput, date2: DateInput, unit?: dayjs.QUnitType): number {
  return dayjs(date1).diff(dayjs(date2), unit)
}

// ========== 日期验证函数 ==========

/**
 * 验证日期是否有效
 * @param date - 待验证的日期
 * @returns 如果日期有效返回 true
 * @example
 * isValidDate('2023-12-01') // 返回: true
 * isValidDate('invalid-date') // 返回: false
 */
export function isValidDate(date: DateInput): boolean {
  return dayjs(date).isValid()
}

/**
 * 判断是否为今天
 * @param date - 待判断的日期
 * @returns 如果是今天返回 true
 */
export function isToday(date: DateInput): boolean {
  return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * 判断是否为昨天
 * @param date - 待判断的日期
 * @returns 如果是昨天返回 true
 */
export function isYesterday(date: DateInput): boolean {
  return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day')
}

/**
 * 判断是否为明天
 * @param date - 待判断的日期
 * @returns 如果是明天返回 true
 */
export function isTomorrow(date: DateInput): boolean {
  return dayjs(date).isSame(dayjs().add(1, 'day'), 'day')
}

/**
 * 判断日期是否在指定范围内
 * @param date - 待判断的日期
 * @param startDate - 开始日期
 * @param endDate - 结束日期
 * @param unit - 比较单位，默认为 'day'
 * @returns 如果在范围内返回 true
 */
export function isInRange(
  date: DateInput,
  startDate: DateInput,
  endDate: DateInput,
  unit: dayjs.OpUnitType = 'day'
): boolean {
  const target = dayjs(date)
  return target.isSameOrAfter(dayjs(startDate), unit) && target.isSameOrBefore(dayjs(endDate), unit)
}

// ========== 日期获取函数 ==========

/**
 * 获取当前时间戳（毫秒）
 * @returns 当前时间戳
 */
export function getCurrentTimestamp(): number {
  return dayjs().valueOf()
}

/**
 * 获取当前时间戳（秒）
 * @returns 当前时间戳（秒）
 */
export function getCurrentTimestampSecond(): number {
  return Math.floor(dayjs().valueOf() / 1000)
}

/**
 * 获取今天的开始时间
 * @returns 今天 00:00:00 的日期对象
 */
export function getStartOfToday(): Dayjs {
  return dayjs().startOf('day')
}

/**
 * 获取今天的结束时间
 * @returns 今天 23:59:59 的日期对象
 */
export function getEndOfToday(): Dayjs {
  return dayjs().endOf('day')
}

/**
 * 获取本周的开始时间
 * @returns 本周第一天 00:00:00 的日期对象
 */
export function getStartOfWeek(): Dayjs {
  return dayjs().startOf('week')
}

/**
 * 获取本周的结束时间
 * @returns 本周最后一天 23:59:59 的日期对象
 */
export function getEndOfWeek(): Dayjs {
  return dayjs().endOf('week')
}

/**
 * 获取本月的开始时间
 * @returns 本月第一天 00:00:00 的日期对象
 */
export function getStartOfMonth(): Dayjs {
  return dayjs().startOf('month')
}

/**
 * 获取本月的结束时间
 * @returns 本月最后一天 23:59:59 的日期对象
 */
export function getEndOfMonth(): Dayjs {
  return dayjs().endOf('month')
}

/**
 * 获取本年的开始时间
 * @returns 本年第一天 00:00:00 的日期对象
 */
export function getStartOfYear(): Dayjs {
  return dayjs().startOf('year')
}

/**
 * 获取本年的结束时间
 * @returns 本年最后一天 23:59:59 的日期对象
 */
export function getEndOfYear(): Dayjs {
  return dayjs().endOf('year')
}

// ========== 便捷工具函数 ==========

/**
 * 获取友好的时间显示
 * @param date - 日期输入
 * @returns 友好的时间显示，如 '刚刚'、'5分钟前'、'今天 14:30'、'昨天 14:30'、'2023-12-01'
 */
export function getFriendlyTime(date: DateInput): string {
  const target = dayjs(date)
  const now = dayjs()
  const diffMinutes = now.diff(target, 'minute')

  if (diffMinutes < 1) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`
  } else if (isToday(target)) {
    return `今天 ${target.format('HH:mm')}`
  } else if (isYesterday(target)) {
    return `昨天 ${target.format('HH:mm')}`
  } else if (now.diff(target, 'day') < 7) {
    return `${now.diff(target, 'day')}天前`
  } else {
    return target.format('YYYY-MM-DD')
  }
}

/**
 * 解析时间范围字符串
 * @param range - 时间范围，如 '7d'、'1M'、'3y'
 * @returns 开始和结束日期
 * @example
 * parseTimeRange('7d') // 返回最近7天的开始和结束时间
 * parseTimeRange('1M') // 返回最近1个月的开始和结束时间
 */
export function parseTimeRange(range: string): { start: Dayjs; end: Dayjs } {
  const now = dayjs()
  const match = range.match(/^(\d+)([dwMy])$/)

  if (!match) {
    throw new Error('Invalid time range format. Use format like "7d", "1M", "3y"')
  }

  const [, value, unit] = match
  const num = parseInt(value, 10)

  let unitType: dayjs.ManipulateType
  switch (unit) {
    case 'd':
      unitType = 'day'
      break
    case 'w':
      unitType = 'week'
      break
    case 'M':
      unitType = 'month'
      break
    case 'y':
      unitType = 'year'
      break
    default:
      throw new Error('Invalid time unit')
  }

  return {
    start: now.subtract(num, unitType).startOf('day'),
    end: now.endOf('day')
  }
}

// ========== 导出 dayjs 实例 ==========

/**
 * 导出 dayjs 实例，用于更复杂的日期操作
 * @description 如果需要使用 dayjs 的其他功能，可以直接使用此实例
 */
export const dateUtil = dayjs

/**
 * 创建日期实例的便捷函数
 * @param date - 日期输入
 * @returns dayjs 实例
 */
export function createDate(date?: DateInput): Dayjs {
  return dayjs(date)
}
