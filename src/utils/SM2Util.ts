// 去除 todo zhaokun
import { sm2 } from 'sm-crypto'
const privateKey = '42ACEF5303833A493CABEFDBB4BC30A44048D5D7221DB8091C789B832144620A'
const publicKey =
  '0411346A8BA885E5AB4DE70695A543366565B278F203CB16F8312BC7ECC7FAB6BBE326D22C7A96C7A0B7A0CB18B57F3BD449EAE90BD37CA16DA5BB1299EA8B2432'
export default {
  // 解密
  decryptedSM2: function (encryptedData) {
    return sm2.doDecrypt(encryptedData, privateKey)
  },
  // 加密
  enryptedSM2: function (originalData) {
    return sm2.doEncrypt(originalData, publicKey, 1)
  }
}
