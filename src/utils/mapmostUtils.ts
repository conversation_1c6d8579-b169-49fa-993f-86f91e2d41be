import Mapmost from '@mapmost/mapmost-webgl'

// mapmost tk
const userId = 'a4e6957797efd68348a406aff86c5302544a08acf740d22696e9c309123ee2f1'
// 天地图 tk
const tk = '8096e391381646488bfacfeea63cdaac'
export const center = [120.088, 30.86]
/**
 * 地图初始化配置
 */
const mapConf: Mapmost.MapOptions = {
  style: 'https://wisioncim.wxskii.com/mms-style/1728979355616-admin-customStyle.json',
  center,
  zoom: 12,
  userId
}

/**
 * 初始化 Mapmost 地图
 * @param mapContainer 地图容器的 DOM 元素
 * @returns 返回 Mapmost.Map 实例
 */
export function initMap(mapContainer: HTMLDivElement): Mapmost.Map {
  const map = new Mapmost.Map({
    container: mapContainer,
    ...mapConf
  })

  map.on('load', () => {
    map.addLayer({
      id: 'wmts-test-layer',
      type: 'raster',
      source: {
        type: 'raster',
        tiles: [
          `https://t0.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tk}`
        ],
        tileSize: 256
      }
    })
  })

  return map
}

/**
 * 注册地图点击事件
 * @param map Mapmost 地图实例
 * @param callback 点击时触发的回调，返回经纬度
 */
export function clickMap(
  map: Mapmost.Map,
  callback: (lngLat: { lng: number; lat: number }) => void
): void {
  map.on('click', (event: Mapmost.MapMouseEvent) => {
    const { lng, lat } = event.lngLat
    callback({ lng, lat })
  })
}

export function addOrMoveMarker({ lng, lat }: { lng: number; lat: number }, map: any, marker: any) {
  if (!marker) {
    marker = new Mapmost.Marker({ color: 'skyblue' }).setLngLat([lng, lat]).addTo(map)
  } else {
    marker.remove()
    marker = new Mapmost.Marker({ color: 'skyblue' }).setLngLat([lng, lat]).addTo(map)
    marker.setLngLat([lng, lat])
  }
  return marker
}

/**
 * 模糊搜索 POI 地点，支持“浙北大厦”等关键词
 * @param keyword 搜索关键字
 * @param count 返回数量
 */
export async function searchAddressList(
  keyword: string,
  count = 5
): Promise<{ name: string; address: string; lng: number; lat: number }[]> {
  try {
    const query = {
      keyWord: `${keyword}`,
      specify: '湖州',
      level: '12',
      mapBound: '-180,-90,180,90', // 不限定地图范围（让它返回全省匹配结果）
      queryType: '1',
      start: '0',
      count: String(count)
    }

    const queryStr = encodeURIComponent(JSON.stringify(query))
    const url = `http://api.tianditu.gov.cn/v2/search?postStr=${queryStr}&type=query&tk=${tk}`

    const response = await fetch(url)
    const data = await response.json()

    if (data.pois && Array.isArray(data.pois)) {
      return data.pois.map((item: any) => {
        const [lng, lat] = item.lonlat.split(',')
        return {
          ...item,
          lng,
          lat
        }
      })
    } else {
      return []
    }
  } catch (error) {
    console.error('地址搜索失败:', error)
    return []
  }
}

export const addMapImage = (map: Mapmost.Map, icon: string, iconName: string) => {
  map.loadImage(icon, (error, image) => {
    if (error) throw error
    map.addImage(iconName, image)
  })
}

export const initPointLayer = (map: Mapmost.Map, imageArr, layerName = 'points') => {
  map.addSource(layerName, {
    type: 'geojson',
    data: { type: 'FeatureCollection', features: [] },
    cluster: true,
    clusterMaxZoom: 14,
    clusterRadius: 50
  })

  // 添加聚类层
  map.addLayer({
    id: 'cluster-icon',
    type: 'symbol',
    source: layerName,
    filter: ['has', 'point_count'],
    layout: {
      'icon-image': 'cluster-icon',
      'icon-size': 0.5,
      'text-field': '{point_count}',
      'text-size': 12,
      'text-anchor': 'center'
    },
    paint: {
      'text-color': '#fff'
    }
  })

  // 点击获取聚类点信息
  map.on('click', 'cluster-icon', function (e) {
    const features = map.queryRenderedFeatures(e.point, {
      layers: ['cluster-icon']
    })
    const clusterId = features[0].properties.cluster_id
    const pointCount = features[0].properties.point_count
    const clusterSource = map.getSource(layerName)
    clusterSource.getClusterLeaves(clusterId, pointCount, 0, function (error, features) {
      // 在控制台输出
      if (error) {
        console.log(error)
      } else {
        console.log('Cluster leaves:', features)
      }
    })
  })

  // 添加未聚类的点层，根据状态设置图标
  map.addLayer({
    id: 'unclustered-point',
    type: 'symbol',
    source: layerName,
    filter: ['!has', 'point_count'],
    layout: {
      'text-field': ['get', 'name'],
      'text-size': 12,
      'text-line-height': 1.3,
      'text-writing-mode': ['horizontal'],
      'text-offset': [0, 2], // 偏移 数组 都为正数的时候，表示右下
      'icon-image': ['match', ['get', 'status'], ...imageArr, /* default */ 'default-icon'],
      'icon-size': 0.5 // 根据需要调整图标大小
    },
    paint: {
      'text-color': '#fff',
      'text-halo-color': '#005FFF',
      'text-halo-width': 3
    }
  })
}

export const setPoint = (map: Mapmost.Map, data, layerName = 'points') => {
  if (data && Array.isArray(data)) {
    // 确保数据源正确设置并触发 Mapmost 聚类刷新
    const source = map.getSource(layerName)
    console.log(data)
    const features = data.map((d) => {
      return {
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [d.longitude, d.latitude]
        },
        properties: {
          status: d.status,
          name: d.name,
          id: d.id
        }
      }
    })
    if (source) {
      source.setData({
        type: 'FeatureCollection',
        features
      })
    }
  } else {
    console.log('无点位数据')
  }
}
