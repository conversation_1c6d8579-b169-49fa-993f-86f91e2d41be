import { ref } from 'vue'
import { exhibitionFieldConfig } from '@/config/business'
import type { ExhibitionFieldConfig } from '@/config/business'

/**
 * 标签配置处理 Hook
 * 提供标签类型识别、配置匹配等功能
 */
export const useLabelConfig = () => {
  const labelType = ref<string>('')
  const labelConfigData = ref<any[]>([])

  /**
   * 初始化标签类型配置
   * 根据字段配置匹配对应的标签类型
   */
  const initializeLabelType = (fieldConfigs: any[]): ExhibitionFieldConfig | null => {
    for (const config of exhibitionFieldConfig) {
      const matchedFields = config.fields
        .map((fieldCode: string) => fieldConfigs.find(f => f.code === fieldCode))
        .filter(Boolean)

      if (matchedFields.length > 0) {
        labelType.value = config.key as string
        labelConfigData.value = matchedFields
        return config
      }
    }
    
    labelType.value = ''
    labelConfigData.value = []
    return null
  }

  /**
   * 根据字段代码获取标签配置
   */
  const getLabelConfigByFields = (fieldCodes: string[]): ExhibitionFieldConfig | null => {
    for (const config of exhibitionFieldConfig) {
      const hasAllFields = config.fields.every(fieldCode => 
        fieldCodes.includes(fieldCode)
      )
      
      if (hasAllFields) {
        return config
      }
    }
    return null
  }

  /**
   * 获取标签类型的显示文本
   */
  const getLabelTypeText = (type?: string): string => {
    const targetType = type || labelType.value
    const config = exhibitionFieldConfig.find(c => c.key === targetType)
    return config?.label || '未知类型'
  }

  /**
   * 检查是否为有效的标签类型
   */
  const isValidLabelType = (type: string): boolean => {
    return exhibitionFieldConfig.some(config => config.key === type)
  }

  /**
   * 获取所有可用的标签类型选项
   */
  const getAllLabelTypeOptions = () => {
    return exhibitionFieldConfig.map(config => ({
      label: config.label,
      value: config.key,
      fields: config.fields
    }))
  }

  /**
   * 重置标签配置
   */
  const resetLabelConfig = () => {
    labelType.value = ''
    labelConfigData.value = []
  }

  return {
    // 响应式数据
    labelType,
    labelConfigData,
    
    // 方法
    initializeLabelType,
    getLabelConfigByFields,
    getLabelTypeText,
    isValidLabelType,
    getAllLabelTypeOptions,
    resetLabelConfig,
    
    // 常量
    exhibitionFieldConfig
  }
}
