/**
 * 字段配置提取 Composable
 * 提供统一的字段配置提取和处理逻辑
 */

import { computed, type ComputedRef } from 'vue'
import type { FieldConfig, Option, FieldExtConfig } from '@/components/FormField/types'

/**
 * 字段配置提取 Hook
 * @param field 字段配置对象
 * @returns 配置提取工具函数
 */
export function useFieldConfig(field: ComputedRef<FieldConfig> | FieldConfig) {
  const fieldConfig = computed(() => {
    return typeof field === 'object' && 'value' in field ? field.value : field
  })

  /**
   * 获取扩展配置值
   * @param name 配置名称
   * @param defaultValue 默认值
   * @returns 配置值
   */
  const getExtConfig = <T = any>(name: string, defaultValue?: T): T => {
    const config = fieldConfig.value.fieldConfExtDOList?.find((item: FieldExtConfig) => item.name === name)
    return config?.value ?? defaultValue
  }

  /**
   * 获取布尔类型配置
   * @param name 配置名称
   * @param defaultValue 默认值
   * @returns 布尔值
   */
  const getBooleanConfig = (name: string, defaultValue = false): boolean => {
    const value = getExtConfig(name)
    if (typeof value === 'boolean') return value
    if (typeof value === 'string') return value === '1' || value === 'true'
    if (typeof value === 'number') return value === 1
    return defaultValue
  }

  /**
   * 获取数字类型配置
   * @param name 配置名称
   * @param defaultValue 默认值
   * @returns 数字值
   */
  const getNumberConfig = (name: string, defaultValue = 0): number => {
    const value = getExtConfig(name)
    const num = Number(value)
    return isNaN(num) ? defaultValue : num
  }

  /**
   * 获取字符串类型配置
   * @param name 配置名称
   * @param defaultValue 默认值
   * @returns 字符串值
   */
  const getStringConfig = (name: string, defaultValue = ''): string => {
    const value = getExtConfig(name)
    return String(value || defaultValue)
  }

  /**
   * 获取选项配置
   * @param name 配置名称
   * @param fallbackOptions 备用选项
   * @returns 选项数组
   */
  const getOptionsConfig = (name: string, fallbackOptions: Option[] = []): Option[] => {
    const config = fieldConfig.value.fieldConfExtDOList?.find((item: FieldExtConfig) => item.name === name)
    if (config?.optionsJson && Array.isArray(config.optionsJson)) {
      return config.optionsJson
    }
    return fallbackOptions
  }

  /**
   * 获取 JSON 配置
   * @param name 配置名称
   * @param defaultValue 默认值
   * @returns 解析后的 JSON 对象
   */
  const getJsonConfig = <T = any>(name: string, defaultValue?: T): T => {
    const value = getExtConfig(name)
    if (typeof value === 'string') {
      try {
        return JSON.parse(value)
      } catch (error) {
        console.warn(`解析 JSON 配置失败: ${name}`, error)
      }
    }
    return value ?? defaultValue
  }

  /**
   * 检查配置是否存在
   * @param name 配置名称
   * @returns 是否存在
   */
  const hasConfig = (name: string): boolean => {
    return fieldConfig.value.fieldConfExtDOList?.some((item: FieldExtConfig) => item.name === name) ?? false
  }

  /**
   * 获取所有扩展配置
   * @returns 扩展配置数组
   */
  const getAllExtConfigs = (): FieldExtConfig[] => {
    return fieldConfig.value.fieldConfExtDOList || []
  }

  /**
   * 获取字段基础信息
   */
  const fieldInfo = computed(() => ({
    code: fieldConfig.value.code,
    name: fieldConfig.value.name,
    fieldType: fieldConfig.value.fieldType,
    remark: fieldConfig.value.remark,
    length: (fieldConfig.value as any).length
  }))

  /**
   * 获取占位符文本
   * @param prefix 前缀文本，默认为 "请输入"
   * @returns 占位符文本
   */
  const getPlaceholder = (prefix = '请输入'): string => {
    const customPlaceholder = getStringConfig('hint')
    if (customPlaceholder) return customPlaceholder || prefix
    return fieldConfig?.value?.remark || `${prefix}${fieldConfig?.value?.name || ''}`
  }

  /**
   * 获取验证规则相关配置
   */
  const validationConfig = computed(() => ({
    required: getBooleanConfig('required'),
    minLength: getNumberConfig('minLength'),
    maxLength: getNumberConfig('maxLength', (fieldConfig.value as any).length),
    pattern: getStringConfig('pattern'),
    customValidator: getStringConfig('customValidator')
  }))

  /**
   * 获取样式相关配置
   */
  const styleConfig = computed(() => ({
    width: getStringConfig('width', '100%'),
    size: getStringConfig('size', 'default'),
    disabled: getBooleanConfig('disabled'),
    readonly: getBooleanConfig('readonly')
  }))

  return {
    fieldConfig,
    fieldInfo,
    getExtConfig,
    getBooleanConfig,
    getNumberConfig,
    getStringConfig,
    getOptionsConfig,
    getJsonConfig,
    hasConfig,
    getAllExtConfigs,
    getPlaceholder,
    validationConfig,
    styleConfig
  }
}

/**
 * 字段类型判断工具
 * @param fieldType 字段类型
 * @returns 类型判断函数集合
 */
export function useFieldTypeUtils(fieldType: number) {
  const isText = () => fieldType === 1
  const isNumber = () => fieldType === 2
  const isRadio = () => fieldType === 3
  const isCheckbox = () => fieldType === 4
  const isDate = () => fieldType === 5
  const isDateRange = () => fieldType === 6
  const isAddress = () => fieldType === 7
  const isRegion = () => fieldType === 8
  const isTag = () => fieldType === 9
  const isAttachment = () => fieldType === 10

  const isSelectType = () => isRadio() || isCheckbox()
  const isDateType = () => isDate() || isDateRange()

  return {
    isText,
    isNumber,
    isRadio,
    isCheckbox,
    isDate,
    isDateRange,
    isAddress,
    isRegion,
    isTag,
    isAttachment,
    isSelectType,
    isDateType
  }
}
