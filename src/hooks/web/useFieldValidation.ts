/**
 * 字段验证 Composable
 * 提供统一的字段验证逻辑
 */

import { computed, type ComputedRef } from 'vue'
import type { FieldConfig, FieldValidationRule } from '@/components/FormField/types'
import { useFieldConfig } from '@/hooks/web/useFieldConfig'

/**
 * 字段验证 Hook
 * @param field 字段配置
 * @returns 验证相关工具函数
 */
export function useFieldValidation(field: ComputedRef<FieldConfig> | FieldConfig) {
  const { getStringConfig, getBooleanConfig, getNumberConfig } = useFieldConfig(field)

  /**
   * 生成基础验证规则
   */
  const baseRules = computed((): FieldValidationRule[] => {
    const rules: FieldValidationRule[] = []
    
    // 必填验证
    if (getBooleanConfig('required')) {
      rules.push({
        required: true,
        message: `请输入${typeof field === 'object' && 'value' in field ? field.value.name : field.name}`,
        trigger: 'blur'
      })
    }

    return rules
  })

  /**
   * 生成文本字段验证规则
   */
  const textRules = computed((): FieldValidationRule[] => {
    const rules = [...baseRules.value]
    
    // 长度验证
    const minLength = getNumberConfig('minLength')
    const maxLength = getNumberConfig('maxLength')
    
    if (minLength > 0 || maxLength > 0) {
      rules.push({
        validator: (rule, value, callback) => {
          if (!value) return callback()
          
          const length = String(value).length
          if (minLength > 0 && length < minLength) {
            return callback(new Error(`最少输入${minLength}个字符`))
          }
          if (maxLength > 0 && length > maxLength) {
            return callback(new Error(`最多输入${maxLength}个字符`))
          }
          callback()
        },
        trigger: 'blur'
      })
    }

    // 正则验证
    const pattern = getStringConfig('pattern')
    if (pattern) {
      rules.push({
        validator: (rule, value, callback) => {
          if (!value) return callback()
          
          try {
            const regex = new RegExp(pattern)
            if (!regex.test(String(value))) {
              return callback(new Error('输入格式不正确'))
            }
            callback()
          } catch (error) {
            console.warn('正则表达式格式错误:', pattern)
            callback()
          }
        },
        trigger: 'blur'
      })
    }

    return rules
  })

  /**
   * 生成数字字段验证规则
   */
  const numberRules = computed((): FieldValidationRule[] => {
    const rules = [...baseRules.value]
    
    // 数值范围验证
    const minValue = getNumberConfig('minValue')
    const maxValue = getNumberConfig('maxValue')
    
    if (minValue !== 0 || maxValue !== 0) {
      rules.push({
        validator: (rule, value, callback) => {
          if (value === null || value === undefined || value === '') return callback()
          
          const num = Number(value)
          if (isNaN(num)) {
            return callback(new Error('请输入有效数字'))
          }
          
          if (minValue !== 0 && num < minValue) {
            return callback(new Error(`数值不能小于${minValue}`))
          }
          if (maxValue !== 0 && num > maxValue) {
            return callback(new Error(`数值不能大于${maxValue}`))
          }
          callback()
        },
        trigger: 'blur'
      })
    }

    return rules
  })

  /**
   * 生成选择字段验证规则
   */
  const selectRules = computed((): FieldValidationRule[] => {
    const rules = [...baseRules.value]
    
    // 多选数量限制
    const minSelect = getNumberConfig('minSelect')
    const maxSelect = getNumberConfig('maxSelect')
    
    if (minSelect > 0 || maxSelect > 0) {
      rules.push({
        validator: (rule, value, callback) => {
          if (!Array.isArray(value)) return callback()
          
          const length = value.length
          if (minSelect > 0 && length < minSelect) {
            return callback(new Error(`至少选择${minSelect}项`))
          }
          if (maxSelect > 0 && length > maxSelect) {
            return callback(new Error(`最多选择${maxSelect}项`))
          }
          callback()
        },
        trigger: 'change'
      })
    }

    return rules
  })

  /**
   * 生成日期字段验证规则
   */
  const dateRules = computed((): FieldValidationRule[] => {
    const rules = [...baseRules.value]
    
    // 日期范围验证
    const minDate = getStringConfig('minDate')
    const maxDate = getStringConfig('maxDate')
    
    if (minDate || maxDate) {
      rules.push({
        validator: (rule, value, callback) => {
          if (!value) return callback()
          
          const date = new Date(value)
          if (isNaN(date.getTime())) {
            return callback(new Error('请选择有效日期'))
          }
          
          if (minDate) {
            const min = new Date(minDate)
            if (date < min) {
              return callback(new Error(`日期不能早于${minDate}`))
            }
          }
          
          if (maxDate) {
            const max = new Date(maxDate)
            if (date > max) {
              return callback(new Error(`日期不能晚于${maxDate}`))
            }
          }
          
          callback()
        },
        trigger: 'change'
      })
    }

    return rules
  })

  /**
   * 生成文件字段验证规则
   */
  const fileRules = computed((): FieldValidationRule[] => {
    const rules = [...baseRules.value]
    
    // 文件数量验证
    const maxFiles = getNumberConfig('countLimit', 5)
    
    if (maxFiles > 0) {
      rules.push({
        validator: (rule, value, callback) => {
          if (!Array.isArray(value)) return callback()
          
          if (value.length > maxFiles) {
            return callback(new Error(`最多上传${maxFiles}个文件`))
          }
          callback()
        },
        trigger: 'change'
      })
    }

    return rules
  })

  /**
   * 根据字段类型获取对应的验证规则
   */
  const getRulesByFieldType = (fieldType: number): FieldValidationRule[] => {
    switch (fieldType) {
      case 1: // TEXT
        return textRules.value
      case 2: // NUMBER
        return numberRules.value
      case 3: // RADIO
      case 4: // CHECKBOX
        return selectRules.value
      case 5: // DATE
      case 6: // DATE_RANGE
        return dateRules.value
      case 10: // ATTACHMENT
        return fileRules.value
      default:
        return baseRules.value
    }
  }

  /**
   * 自定义验证器
   */
  const createCustomValidator = (validatorCode: string) => {
    return (rule: any, value: any, callback: any) => {
      try {
        // 这里可以根据实际需求实现自定义验证器的执行逻辑
        // 例如：通过 eval 或者预定义的验证器映射来执行
        console.log('执行自定义验证器:', validatorCode, value)
        callback()
      } catch (error) {
        console.error('自定义验证器执行失败:', error)
        callback(new Error('验证失败'))
      }
    }
  }

  return {
    baseRules,
    textRules,
    numberRules,
    selectRules,
    dateRules,
    fileRules,
    getRulesByFieldType,
    createCustomValidator
  }
}
