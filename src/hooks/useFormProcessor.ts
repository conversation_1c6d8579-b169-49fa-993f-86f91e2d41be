import { ref, reactive } from 'vue'
import { FormRules } from 'element-plus'
import { FieldType } from '@/config/constants/enums/field'
import { BusinessType } from '@/config/constants/enums/business'
import {
  validatePatternMapNumber,
  validateUSCC,
  createRequiredRule,
  createRegexRule
} from '@/utils/formRules'

/**
 * 表单处理 Hook
 * 提供表单数据初始化、字段处理、验证规则生成等功能
 */
export const useFormProcessor = () => {
  const formData = ref<Record<string, any>>({})
  const formRules = reactive<FormRules>({})
  const fieldOptionsMap = ref(new Map<string, any[]>())

  // 字段类型常量
  const FIELD_TYPES = {
    CHECKBOX: FieldType.CHECKBOX,
    RADIO: FieldType.RADIO,
    DATE_RANGE: FieldType.DATE_RANGE,
    TEXT: FieldType.TEXT,
    NUMBER: FieldType.NUMBER
  } as const

  /**
   * 处理字段组配置
   */
  const processFieldGroups = (
    filteredData: any[], 
    disabledKeys: string[] = [],
    fetchFieldOptions?: (field: any) => void
  ) => {
    filteredData.forEach((group) => {
      group.fields.forEach((field: any) => {
        // 处理系统字段禁用状态
        if (field.bizType === BusinessType.SYSTEM.toString()) {
          field.fieldConfExtDOList = field.fieldConfExtDOList || []
          field.fieldConfExtDOList.push({
            name: 'disabled',
            value: disabledKeys.includes(field.code),
          })
        }

        // 修正 fieldConfExtDOList 的 type 字段类型
        if (Array.isArray(field.fieldConfExtDOList)) {
          field.fieldConfExtDOList.forEach((item: any) => {
            item.type = item.type == null ? 0 : Number(item.type)
          })
        }

        // 获取单选/多选字段选项
        if (
          (field.fieldType === FIELD_TYPES.RADIO || field.fieldType === FIELD_TYPES.CHECKBOX) &&
          fetchFieldOptions
        ) {
          fetchFieldOptions(field)
        }
      })
    })

    return filteredData
  }

  /**
   * 获取字段值（优先级：store > businessData > 默认值）
   */
  const getFieldValue = (field: any, storeFormData: any, businessData: any): any => {
    if (storeFormData && Object.prototype.hasOwnProperty.call(storeFormData, field.code)) {
      return storeFormData[field.code]
    }
    if (businessData && businessData[field.code] !== undefined) {
      return businessData[field.code]
    }
    return ''
  }

  /**
   * 设置表单字段值（根据字段类型处理）
   */
  const setFormFieldValue = (
    field: any, 
    fieldValue: any, 
    storeFormData: any, 
    businessData: any,
    targetFormData: Record<string, any> = formData.value
  ) => {
    const { code, fieldType, fieldConfExtDOList } = field

    switch (fieldType) {
      case FIELD_TYPES.CHECKBOX:
        targetFormData[code] = fieldValue ? String(fieldValue).split(',') : []
        break

      case FIELD_TYPES.RADIO:
        targetFormData[code] = fieldValue?.toString() || ''
        break

      case FIELD_TYPES.DATE_RANGE:
        const code2 = fieldConfExtDOList?.find((item: any) => item.name === 'code2')?.value || ''
        const code2Value = (storeFormData?.[code2]) || businessData?.[code2] || ''
        targetFormData[code2] = code2Value
        targetFormData[code] = [fieldValue, code2Value]
        break

      default:
        targetFormData[code] = fieldValue || ''
    }
  }

  /**
   * 初始化表单数据
   */
  const initializeFormData = (
    filteredData: any[], 
    storeFormData: any = {}, 
    businessData: any = {},
    targetFormData?: Record<string, any>
  ) => {
    const target = targetFormData || formData.value
    
    // 重置表单数据
    Object.keys(target).forEach(key => delete target[key])

    filteredData.forEach((group) => {
      group.fields.forEach((field: any) => {
        const fieldValue = getFieldValue(field, storeFormData, businessData)
        setFormFieldValue(field, fieldValue, storeFormData, businessData, target)
      })
    })

    return target
  }

  /**
   * 生成表单验证规则
   */
  const generateFormRules = (fields: any[]): FormRules => {
    return fields.reduce((rules: FormRules, field) => {
      const ruleArr: any[] = []

      // 必填验证
      if (field.required) ruleArr.push(createRequiredRule(field.name))

      // 自定义正则验证
      if (field.fieldType === FIELD_TYPES.TEXT) {
        const { required, fieldConfExtObj } = field
        const { regex, prompt = '格式不正确', dataValidation } = fieldConfExtObj || {}
        
        if (dataValidation === '1' && regex) {
          ruleArr.push(createRegexRule(regex, prompt, required))
        } else if (dataValidation === '3') {
          ruleArr.push({
            validator: (_rule: any, value: any, callback: any) => {
              if (!required && !value) return callback()
              if (!validateUSCC(value)) {
                callback(new Error(prompt))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          })
        } else if (dataValidation && validatePatternMapNumber[dataValidation]) {
          const regex = validatePatternMapNumber[dataValidation]
          ruleArr.push(createRegexRule(regex, prompt, required))
        }
      }

      if (ruleArr.length) rules[field.code] = ruleArr
      return rules
    }, {})
  }

  /**
   * 生成表单验证规则并应用到当前表单
   */
  const generateFormValidationRules = (filteredData: any[]) => {
    const allFields = filteredData
      .flatMap((group: any) => group.fields)
      .map((field: any) => ({
        ...field,
        fieldConfExtDOList:
          field.fieldConfExtDOList?.map((item: any) => ({
            ...item,
            type: item.type == null ? 0 : Number(item.type)
          })) || []
      }))

    const rules = generateFormRules(allFields)
    Object.assign(formRules, rules)
    return rules
  }

  /**
   * 处理表单提交数据
   */
  const processSubmitData = (fieldGroups: any[], sourceFormData: Record<string, any> = formData.value) => {
    const businessJson: Record<string, any> = {}

    fieldGroups.forEach((group) => {
      group.fields.forEach((field: any) => {
        if (field.fieldType === FIELD_TYPES.DATE_RANGE) {
          const val = sourceFormData[field.code]
          if (Array.isArray(val) && val.length === 2) {
            businessJson[field.code] = val[0]
            const code2 = field.fieldConfExtDOList?.find((item: any) => item.name === 'code2')?.value
            if (code2) {
              businessJson[code2] = val[1]
            }
          }
        } else if (field.fieldType === FIELD_TYPES.CHECKBOX) {
          businessJson[field.code] = Array.isArray(sourceFormData[field.code])
            ? sourceFormData[field.code].join(',')
            : ''
        } else {
          businessJson[field.code] = sourceFormData[field.code]
        }
      })
    })

    return businessJson
  }

  /**
   * 重置表单数据
   */
  const resetFormData = () => {
    Object.keys(formData.value).forEach(key => delete formData.value[key])
    Object.keys(formRules).forEach(key => delete formRules[key])
  }

  return {
    // 响应式数据
    formData,
    formRules,
    fieldOptionsMap,
    
    // 常量
    FIELD_TYPES,
    
    // 方法
    processFieldGroups,
    getFieldValue,
    setFormFieldValue,
    initializeFormData,
    generateFormRules,
    generateFormValidationRules,
    processSubmitData,
    resetFormData
  }
}
