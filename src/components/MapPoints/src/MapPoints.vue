<template>
  <div class="space-y-4">
    <!-- 地址 + 经纬度 -->
    <div v-if="showFind" class="absolute z-2 m-1 flex items-center gap-2">
      <span>
        <el-autocomplete
          v-model="searchText"
          :fetch-suggestions="querySearch"
          placeholder="请输入地址"
          class="w-64"
          :trigger-on-focus="false"
          @select="handleSelect"
        >
          <template #suffix>
            <el-icon><Search /></el-icon>
          </template>

          <template #default="{ item }">
            <div class="value">{{ item.value }}</div>
            <div class="link mb-2 border-b-2 border-gray-500 text-[8px]/2">{{ item.address }}</div>
          </template>
        </el-autocomplete>
      </span>
      <span class="text-gray-500 bg-white border rounded py-1 px-4">
        <span class="text-sm/[25px]">经纬度：</span>
        <el-input readonly v-model="lngValue" size="small" class="w-16" placeholder="经度" />
        <el-input readonly v-model="latValue" size="small" class="w-16" placeholder="纬度" />
      </span>
    </div>
    <div
      ref="mapContainer"
      class=" h-[500px] rounded shadow-md border border-gray-300"
      :class="{ 'h-[700px]': setHeight }"
    ></div>
  </div>
</template>
<!--class="w-[950px] h-[500px] rounded shadow-md border border-gray-300"-->
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import Mapmost from '@mapmost/mapmost-webgl'
import siteIcon from '@/views/EquipmentManagement/SiteAdministration/assets/siteIcon.png'
import { initMap, clickMap, addOrMoveMarker, center, searchAddressList } from '@/utils/mapmostUtils'
import { Search } from '@element-plus/icons-vue'

let map: Mapmost.Map | null = null
const mapContainer = ref<HTMLDivElement | null>(null)

const props = withDefaults(
  defineProps<{
    modelValue: { lng: number; lat: number }
    showFind: boolean
    setHeight : boolean
    pointDisplay : boolean
  }>(),
  {
    showFind: false,
    setHeight : false,
    pointDisplay : true,
  }
)

const lngValue = computed(() => props.modelValue?.lng || center[0])
const latValue = computed(() => props.modelValue?.lat || center[1])
const emit = defineEmits<{
  (e: 'update:modelValue', value: { lng: number; lat: number }): void
}>()

let marker: Mapmost.Marker | null = null

// 地址搜索输入
const searchText = ref('')
const searching = ref(false)

const querySearch = async (queryString: string, cb: (results: any[]) => void) => {
  if (!queryString.trim()) return cb([])

  searching.value = true
  const result = await searchAddressList(queryString)
  searching.value = false

  cb(
    result.map((item) => ({
      value: item.name, // 展示名称
      ...item // 保留经纬度
    }))
  )
}

const handleSelect = (item: { name: string; lng: number; lat: number }) => {
  emit('update:modelValue', { lng: item.lng, lat: item.lat })
  if (map) {
    map.flyTo({ center: [item.lng, item.lat], zoom: 14 })
    marker = addOrMoveMarker({ lng: item.lng, lat: item.lat }, map, marker)
  }
}

onMounted(() => {
  if (mapContainer.value) {
    map = initMap(mapContainer.value)
    map.on('load', () => {
      initPointLayer(['site', 'siteIcon'])
      // marker = props.pointDisplay? addOrMoveMarker({ lng: center[0], lat: center[1] }, map, marker) : ''

      clickMap(map, ({ lng, lat }) => {
        emit('update:modelValue', { lng, lat })
        marker = addOrMoveMarker({ lng, lat }, map, marker)
      })
    })
  }
})

function reset() {
  emit('update:modelValue', { lng: center[0], lat: center[1] })
  if (marker) {
    marker = addOrMoveMarker({ lng: center[0], lat: center[1] }, map, marker)
    map.flyTo({ center, zoom: 14 })
    searchText.value = ''
  }
}
 const setPoint = (data, layerName = 'points') => {
  if (data && Array.isArray(data)) {
    // 确保数据源和图层已初始化
    initPointLayer(['site', 'siteIcon'], layerName)

    // 确保数据源正确设置并触发 Mapmost 聚类刷新
    const source = map.getSource(layerName)
    const features = data.map((d) => {
      return {
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [d.longitude, d.latitude]
        },
        properties: {
          status: d.status,
          name: d.name,
          id: d.id
        }
      }
    })
    console.log('features', features)
    if (source) {
      source.setData({
        type: 'FeatureCollection',
        features
      })
    }
  } else {
    console.log('无点位数据')
  }
}

 const initPointLayer = ( imageArr, layerName = 'points') => {
  // 检查source是否已存在
  if (!map.getSource(layerName)) {
    map.addSource(layerName, {
      type: 'geojson',
      data: { type: 'FeatureCollection', features: [] },
      cluster: true,
      clusterMaxZoom: 14,
      clusterRadius: 50
    })

    // 添加未聚类的点位图层
    map.addLayer({
      id: `${layerName}-unclustered`,
      type: 'circle',
      source: layerName,
      filter: ['!', ['has', 'point_count']],
      paint: {
        'circle-radius': 8,
        'circle-color': '#11b4da',
        'circle-stroke-width': 1,
        'circle-stroke-color': '#fff'
      }
    })

    // 添加聚类点位图层
    map.addLayer({
      id: `${layerName}-clusters`,
      type: 'circle',
      source: layerName,
      filter: ['has', 'point_count'],
      paint: {
        'circle-radius': [
          'step',
          ['get', 'point_count'],
          20,
          100, 30,
          750, 40
        ],
        'circle-color': [
          'step',
          ['get', 'point_count'],
          '#51bbd6',
          100, '#f1f075',
          750, '#f28cb1'
        ]
      }
    })

    // 添加聚类数量文字图层 - 使用默认字体
    map.addLayer({
      id: `${layerName}-cluster-count`,
      type: 'symbol',
      source: layerName,
      filter: ['has', 'point_count'],
      layout: {
        'text-field': '{point_count_abbreviated}',
        'text-size': 12
      },
      paint: {
        'text-color': '#ffffff'
      }
    })
  }
}
defineExpose({ reset ,setPoint})
</script>

<style scoped lang="scss">
.el-input {
  width: auto;
}
.el-autocomplete-suggestion li {
  line-height: 20px;
}
</style>
