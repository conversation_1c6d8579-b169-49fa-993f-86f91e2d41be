/**
 * 字段组件工厂
 * 统一管理字段组件的注册和获取
 */

import { defineAsyncComponent, type Component } from 'vue'
import { FieldType } from '@/config/constants/enums/field'

// 字段组件映射类型
export type FieldComponentMap = Record<number, Component>

// 懒加载字段组件
const TextFieldComponent = defineAsyncComponent(() => import('../components/TextFieldComponent.vue'))
const NumberFieldComponent = defineAsyncComponent(() => import('../components/NumberFieldComponent.vue'))
const SelectFieldComponent = defineAsyncComponent(() => import('../components/SelectFieldComponent.vue'))
const DateFieldComponent = defineAsyncComponent(() => import('../components/DateFieldComponent.vue'))
const DateRangeFieldComponent = defineAsyncComponent(() => import('../components/DateRangeFieldComponent.vue'))
const FileFieldComponent = defineAsyncComponent(() => import('../components/FileFieldComponent.vue'))
const TagFieldComponent = defineAsyncComponent(() => import('../components/TagFieldComponent.vue'))
const RegionFieldComponent = defineAsyncComponent(() => import('../../AMapSelector/src/AMapSelector.vue'))

/**
 * 默认字段组件映射
 */
export const defaultFieldComponentMap: FieldComponentMap = {
  [FieldType.TEXT]: TextFieldComponent,
  [FieldType.NUMBER]: NumberFieldComponent,
  [FieldType.RADIO]: SelectFieldComponent,
  [FieldType.CHECKBOX]: SelectFieldComponent,
  [FieldType.DATE]: DateFieldComponent,
  [FieldType.DATE_RANGE]: DateRangeFieldComponent,
  [FieldType.ATTACHMENT]: FileFieldComponent,
  [FieldType.TAG]: TagFieldComponent,
  [FieldType.REGION]: RegionFieldComponent,
  [FieldType.ADDRESS]: RegionFieldComponent, // 地址选择复用地区组件
}

/**
 * 字段组件工厂类
 */
export class FieldComponentFactory {
  private componentMap: FieldComponentMap

constructor(customMap?: Partial<FieldComponentMap>) {
  this.componentMap = { ...defaultFieldComponentMap }
  if (customMap) {
    for (const key in customMap) {
      const comp = customMap[key as unknown as number]
      if (comp !== undefined) {
        this.componentMap[key as unknown as number] = comp
      }
      // 如果 comp 是 undefined，什么都不做，保留默认值
    }
  }
}

  /**
   * 根据字段类型获取对应的组件
   * @param fieldType 字段类型
   * @returns Vue 组件
   */
  getComponent(fieldType: number): Component {
    return this.componentMap[fieldType] || TextFieldComponent
  }

  /**
   * 注册自定义字段组件
   * @param fieldType 字段类型
   * @param component Vue 组件
   */
  registerComponent(fieldType: number, component: Component): void {
    this.componentMap[fieldType] = component
  }

  /**
   * 批量注册字段组件
   * @param componentMap 组件映射
   */
  registerComponents(componentMap: Partial<FieldComponentMap>): void {
    Object.assign(this.componentMap, componentMap)
  }

  /**
   * 获取所有已注册的组件映射
   * @returns 组件映射
   */
  getAllComponents(): FieldComponentMap {
    return { ...this.componentMap }
  }

  /**
   * 检查字段类型是否已注册组件
   * @param fieldType 字段类型
   * @returns 是否已注册
   */
  hasComponent(fieldType: number): boolean {
    return fieldType in this.componentMap
  }

  /**
   * 移除字段组件
   * @param fieldType 字段类型
   */
  removeComponent(fieldType: number): void {
    delete this.componentMap[fieldType]
  }
}

/**
 * 默认字段组件工厂实例
 */
export const fieldComponentFactory = new FieldComponentFactory()

/**
 * 获取字段组件的便捷函数
 * @param fieldType 字段类型
 * @returns Vue 组件
 */
export function getFieldComponent(fieldType: number): Component {
  return fieldComponentFactory.getComponent(fieldType)
}

/**
 * 注册字段组件的便捷函数
 * @param fieldType 字段类型
 * @param component Vue 组件
 */
export function registerFieldComponent(fieldType: number, component: Component): void {
  fieldComponentFactory.registerComponent(fieldType, component)
}

/**
 * 字段组件注册装饰器（用于类组件）
 * @param fieldType 字段类型
 */
export function FieldComponent(fieldType: number) {
  return function (target: any) {
    registerFieldComponent(fieldType, target)
    return target
  }
}

/**
 * 字段类型到组件名称的映射
 */
export const fieldTypeToComponentName: Record<number, string> = {
  [FieldType.TEXT]: 'TextFieldComponent',
  [FieldType.NUMBER]: 'NumberFieldComponent',
  [FieldType.RADIO]: 'SelectFieldComponent',
  [FieldType.CHECKBOX]: 'SelectFieldComponent',
  [FieldType.DATE]: 'DateFieldComponent',
  [FieldType.DATE_RANGE]: 'DateRangeFieldComponent',
  [FieldType.ATTACHMENT]: 'FileFieldComponent',
  [FieldType.TAG]: 'TagFieldComponent',
  [FieldType.REGION]: 'RegionFieldComponent',
  [FieldType.ADDRESS]: 'RegionFieldComponent',
}

/**
 * 获取字段组件名称
 * @param fieldType 字段类型
 * @returns 组件名称
 */
export function getFieldComponentName(fieldType: number): string {
  return fieldTypeToComponentName[fieldType] || 'TextFieldComponent'
}
