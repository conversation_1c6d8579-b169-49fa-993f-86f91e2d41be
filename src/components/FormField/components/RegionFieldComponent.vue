<template>
  <el-tree-select
    :data="treeData"
    :model-value="modelValue"
    :render-after-expand="false"
    :placeholder="placeholder"
    :clearable="clearable"
    :filterable="filterable"
    :multiple="multiple"
    :check-strictly="checkStrictly"
    :show-checkbox="showCheckbox"
    :node-key="nodeKey"
    :props="treeProps"
    class="w-full"
    @update:model-value="handleUpdate"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { SimpleFieldConfig, Option } from '../types'

interface Props {
  field: SimpleFieldConfig
  modelValue: any
  fieldOptions?: Option[]
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const handleUpdate = (value: any) => {
  emit('update:modelValue', value)
}

// 计算占位符
const placeholder = computed(() => {
  return props.field.remark || `请选择${props.field.name}`
})

// 是否可清空
const clearable = computed(() => {
  const clearableConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'clearable')
  return clearableConfig?.value !== '0' // 默认可清空
})

// 是否可搜索
const filterable = computed(() => {
  const filterableConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'filterable')
  return filterableConfig?.value === '1'
})

// 是否多选
const multiple = computed(() => {
  const multipleConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'multiple')
  return multipleConfig?.value === '1'
})

// 是否严格模式（父子节点不关联）
const checkStrictly = computed(() => {
  const strictlyConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'checkStrictly')
  return strictlyConfig?.value === '1'
})

// 是否显示复选框
const showCheckbox = computed(() => {
  return multiple.value
})

// 节点唯一标识
const nodeKey = computed(() => {
  const keyConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'nodeKey')
  return keyConfig?.value || 'value'
})

// 树形控件的配置
const treeProps = computed(() => {
  const labelConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'labelKey')
  const valueConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'valueKey')
  const childrenConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'childrenKey')

  return {
    label: labelConfig?.value || 'label',
    value: valueConfig?.value || 'value',
    children: childrenConfig?.value || 'children'
  }
})

// 树形数据 todo 需要调试
const treeData = computed(() => {
  // 优先使用字段配置中的数据
  const treeDataConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'treeData')
  if (treeDataConfig?.value) {
    try {
      return JSON.parse(treeDataConfig.value)
    } catch (error) {
      console.warn('解析树形数据失败:', error)
    }
  }

  // 使用传入的选项数据
  if (props.fieldOptions && props.fieldOptions.length > 0) {
    return props.fieldOptions
  }

  // 默认的地区数据结构示例
  return [
    {
      label: '北京市',
      value: '110000',
      children: [
        { label: '东城区', value: '110101' },
        { label: '西城区', value: '110102' },
        { label: '朝阳区', value: '110105' },
        { label: '丰台区', value: '110106' }
      ]
    },
    {
      label: '上海市',
      value: '310000',
      children: [
        { label: '黄浦区', value: '310101' },
        { label: '徐汇区', value: '310104' },
        { label: '长宁区', value: '310105' },
        { label: '静安区', value: '310106' }
      ]
    }
  ]
})
</script>
