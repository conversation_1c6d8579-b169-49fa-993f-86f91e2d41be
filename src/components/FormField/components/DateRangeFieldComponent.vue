<template>
  <el-date-picker
    :model-value="modelValue"
    type="datetimerange"
    :format="pickerFormat"
    value-format="x"
    :range-separator="rangeSeparator"
    :start-placeholder="startPlaceholder"
    :end-placeholder="endPlaceholder"
    :clearable="clearable"
    :disabled-date="disabledDate"
    class="w-full"
    @update:model-value="handleUpdate"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { SimpleFieldConfig } from '../types'
import { getTimePickerFormat } from '@/utils/datePicker'

interface Props {
  field: SimpleFieldConfig
  modelValue: any
  fieldOptions?: any[]
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const handleUpdate = (value: any) => {
  emit('update:modelValue', value)
}

// 是否可清空
const clearable = computed(() => {
  const clearableConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'clearable')
  return clearableConfig?.value !== '0' // 默认可清空
})

const pickerFormat = computed(() => getTimePickerFormat(props.field))

// 范围分隔符
const rangeSeparator = computed(() => {
  const separatorConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'rangeSeparator')
  return separatorConfig?.value || '至'
})

// 开始时间占位符
const startPlaceholder = computed(() => {
  const placeholderConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'startPlaceholder')
  return placeholderConfig?.value || '开始时间'
})

// 结束时间占位符
const endPlaceholder = computed(() => {
  const placeholderConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'endPlaceholder')
  return placeholderConfig?.value || '结束时间'
})

// 禁用日期函数
const disabledDate = computed(() => {
  const minDateConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'minDate')
  const maxDateConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'maxDate')

  if (!minDateConfig?.value && !maxDateConfig?.value) {
    return undefined
  }

  return (time: Date) => {
    const currentTime = time.getTime()

    if (minDateConfig?.value) {
      const minTime = new Date(minDateConfig.value).getTime()
      if (currentTime < minTime) return true
    }

    if (maxDateConfig?.value) {
      const maxTime = new Date(maxDateConfig.value).getTime()
      if (currentTime > maxTime) return true
    }

    return false
  }
})
</script>
