<template>
  <el-date-picker
    :model-value="modelValue"
    :type="pickerType"
    :format="pickerFormat"
    value-format="x"
    :placeholder="placeholder"
    :clearable="clearable"
    :disabled-date="disabledDate"
    class="w-full"
    @update:model-value="handleUpdate"
  />
</template>

<script setup lang="ts">
import type { SimpleFieldConfig } from '../types'
import { getTimePickerType, getTimePickerFormat } from '@/utils/datePicker'
import { useFieldConfig } from '@/hooks/web/useFieldConfig'

interface Props {
  field: SimpleFieldConfig
  modelValue: any
  fieldOptions?: any[]
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const handleUpdate = (value: any) => {
  emit('update:modelValue', value)
}
const { getPlaceholder } = useFieldConfig(computed(() => props.field))
// 计算占位符
const placeholder = computed(() => getPlaceholder())
// 是否可清空
const clearable = computed(() => {
  const clearableConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'clearable')
  return clearableConfig?.value !== '0' // 默认可清空
})

const pickerType = computed(() => getTimePickerType(props.field))
const pickerFormat = computed(() => getTimePickerFormat(props.field))

// 禁用日期函数
const disabledDate = computed(() => {
  const minDateConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'minDate')
  const maxDateConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'maxDate')

  if (!minDateConfig?.value && !maxDateConfig?.value) {
    return undefined
  }

  return (time: Date) => {
    const currentTime = time.getTime()

    if (minDateConfig?.value) {
      const minTime = new Date(minDateConfig.value).getTime()
      if (currentTime < minTime) return true
    }

    if (maxDateConfig?.value) {
      const maxTime = new Date(maxDateConfig.value).getTime()
      if (currentTime > maxTime) return true
    }

    return false
  }
})
</script>
