<template>
  <div class="tag-field-component">
    <el-input
      v-model="inputValue"
      :placeholder="placeholder"
      :maxlength="maxTagLength"
      @keyup.enter="addTag"
      @blur="addTag"
    >
      <template #append>
        <el-button @click="addTag" :disabled="!inputValue.trim()">
          添加
        </el-button>
      </template>
    </el-input>

    <div v-if="tags.length > 0" class="tags-container mt-2">
      <el-tag
        v-for="(tag, index) in tags"
        :key="index"
        :closable="!readonly"
        :type="tagType"
        :size="tagSize"
        @close="removeTag(index)"
        class="mr-1 mb-1"
      >
        {{ tag }}
      </el-tag>
    </div>

    <div v-if="showTagCount" class="tag-count-info mt-1 text-xs text-gray-500">
      已添加 {{ tags.length }} 个标签
      <span v-if="maxTags > 0">（最多 {{ maxTags }} 个）</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { SimpleFieldConfig } from '../types'

interface Props {
  field: SimpleFieldConfig
  modelValue: any
  fieldOptions?: any[]
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const inputValue = ref('')

// 计算占位符
const placeholder = computed(() => {
  return props.field.remark || `请输入${props.field.name}标签`
})

// 是否只读
const readonly = computed(() => {
  const readonlyConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'readonly')
  return readonlyConfig?.value === '1'
})

// 标签类型
const tagType = computed(() => {
  const typeConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'tagType')
  return typeConfig?.value || 'primary'
})

// 标签大小
const tagSize = computed(() => {
  const sizeConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'tagSize')
  return sizeConfig?.value || 'default'
})

// 最大标签数量
const maxTags = computed(() => {
  const maxTagsConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'maxTags')
  return maxTagsConfig?.value ? parseInt(maxTagsConfig.value, 10) : 0
})

// 单个标签最大长度
const maxTagLength = computed(() => {
  const maxLengthConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'maxTagLength')
  return maxLengthConfig?.value ? parseInt(maxLengthConfig.value, 10) : 20
})

// 是否显示标签数量
const showTagCount = computed(() => {
  const showCountConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'showTagCount')
  return showCountConfig?.value !== '0' // 默认显示
})

// 标签数组
const tags = computed({
  get: () => {
    if (Array.isArray(props.modelValue)) {
      return props.modelValue
    }
    if (typeof props.modelValue === 'string' && props.modelValue) {
      return props.modelValue.split(',').filter(tag => tag.trim())
    }
    return []
  },
  set: (value: string[]) => {
    emit('update:modelValue', value)
  }
})

// 添加标签
const addTag = () => {
  const trimmedValue = inputValue.value.trim()
  if (!trimmedValue) return

  // 检查是否已存在
  if (tags.value.includes(trimmedValue)) {
    ElMessage.warning('标签已存在')
    inputValue.value = ''
    return
  }

  // 检查数量限制
  if (maxTags.value > 0 && tags.value.length >= maxTags.value) {
    ElMessage.warning(`最多只能添加 ${maxTags.value} 个标签`)
    return
  }

  const newTags = [...tags.value, trimmedValue]
  tags.value = newTags
  inputValue.value = ''
}

// 移除标签
const removeTag = (index: number) => {
  if (readonly.value) return

  const newTags = tags.value.filter((_, i) => i !== index)
  tags.value = newTags
}
</script>

<style scoped>
.tag-field-component {
  width: 100%;
}

.tags-container {
  min-height: 32px;
  padding: 4px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  background-color: var(--el-fill-color-blank);
}

.tag-count-info {
  color: var(--el-text-color-regular);
  font-size: 12px;
}
</style>
