<template>
  <UploadFile
    :model-value="modelValue"
    :file-type="fileTypes"
    :limit="fileLimit"
    :file-size="fileSize"
    :multiple="multiple"
    :accept="accept"
    class="min-w-80px"
    @update:model-value="handleUpdate"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { UploadFile } from '@/components/UploadFile'
import type { SimpleFieldConfig } from '../types'
import { FileFormatOptions } from '@/config/constants/enums/field'

interface Props {
  field: SimpleFieldConfig
  modelValue: any
  fieldOptions?: any[]
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const handleUpdate = (value: any) => {
  emit('update:modelValue', value)
}

// 计算文件类型数组
const fileTypes = computed(() => {
  const allowedTypesConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'allowedTypes')
  if (!allowedTypesConfig?.value) return ['doc', 'xls', 'ppt', 'txt', 'pdf']
  return String(allowedTypesConfig.value).split(',').map(type => type.trim())
})

// 计算文件数量限制
const fileLimit = computed(() => {
  const countLimitConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'countLimit')
  return countLimitConfig?.value || 5
})

// 计算文件大小限制
const fileSize = computed(() => {
  const sizeLimitConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'sizeLimit')
  return sizeLimitConfig?.value || 5
})

// 是否支持多文件上传
const multiple = computed(() => {
  const multipleConfig = props.field.fieldConfExtDOList?.find(item => item.name === 'multiple')
  return multipleConfig?.value !== '0' // 默认支持多文件
})

// 计算 accept 属性
const accept = computed(() => {
  const types = fileTypes.value
  const mimeTypes: string[] = []

  types.forEach(type => {
    const found = FileFormatOptions.find(opt => opt.value === type.toLowerCase())
    if (found) {
      mimeTypes.push(found.value)
    } else {
      mimeTypes.push(`.${type}`)
    }
  })
  return mimeTypes.join(',')
})
</script>
