<script setup lang="ts">
import { ref } from 'vue'
import { getDuty } from '@/api/system/user/profile'
import {
  getDutyRecordCreate,
  getDutyRecordToday,
  getDutyRecordPlatformList,
  getDutyRecordShift
} from '@/api/system/duty/duty.data'
import icon from '../assets/icon.png'
import dayjs from 'dayjs'

const handoverDate = ref(dayjs().format('YYYY-MM-DD'))
const currentTime = computed(() => dayjs().format('HH:mm'))

const takeOverdialogVisible = ref(false)
const handoverDialogVisible = ref(false)

interface StatusInfo {
  dutyStatus: number
  startTime: string
  endTime: string
  userId: number
}
const statusInfo = ref<StatusInfo>({
  dutyStatus: -1,
  startTime: '',
  endTime: '',
  userId: 0
})
const getData = async () => {
  const data = await getDuty()
  statusInfo.value = data
  if (statusInfo.value.dutyStatus === 0) {
    handleTakeOver()
  }
}

getData()

const message = useMessage()
const examRef = ref(null)
const confirmHandover = async () => {
  const isValid = await examRef.value?.validateForm()
  if (isValid) {
    await getDutyRecordCreate({
      ...statusInfo.value,
      dutyUserId: statusInfo.value.userId,
      shiftDetail: examForm.value
    })
    handoverDialogVisible.value = false
    message.success('交班记录提交成功！')
    await getData()
    location.reload()
  }
}

interface PlatformState {
  code: number
  name: string
  value: number
}

interface ShiftDetail {
  platformState: PlatformState[]
  exceptDesc: string
  frontDesc: string
  carrierDesc: string
  handDesc: string
  otherDesc: string
  dealDesc: string
  dealResult: string
  hygienicConditions: string
}
const examForm = ref<ShiftDetail>({
  platformState: [],
  exceptDesc: '',
  frontDesc: '',
  carrierDesc: '',
  handDesc: '',
  otherDesc: '',
  dealDesc: '',
  dealResult: '',
  hygienicConditions: ''
})
const dirData = ref<PlatformState[]>([])
const getDir = async () => {
  dirData.value = await getDutyRecordPlatformList()
  examForm.value.platformState = dirData.value
}
getDir()
const cancelHandover = () => {
  handoverDialogVisible.value = false
  message.success('已取消交班')
}

const todayList = ref([])
const handleTakeOver = async () => {
  todayList.value = await getDutyRecordToday()
  takeOverdialogVisible.value = true
}
const confirmTakeOver = async () => {
  await getDutyRecordCreate({
    ...statusInfo.value,
    dutyUserId: statusInfo.value.userId
  })
  message.success('接班成功！')
  takeOverdialogVisible.value = false
  await getData()
  location.reload()
}
const cancelTakeOver = () => {
  takeOverdialogVisible.value = false
  message.success('已取消接班')
}

// 将时间转换为秒数
const toSeconds = (time: string) => {
  const [h, m, s] = time.split(':').map(Number)
  return h * 3600 + m * 60 + s
}

const isNextDay = (startTime: string, endTime: string) => {
  return toSeconds(startTime) >= toSeconds(endTime) ? '次日' : ''
}

const getColorClass = (status: number) => {
  if (status === 1) {
    return ' bg-blue-500'
  } else if (status === 2) {
    return ' bg-orange-500'
  } else {
    return ' bg-gray-500'
  }
}

const getStatusText = (status: number) => {
  if (status === 1) {
    return '已完成交班'
  } else if (status === 2) {
    return '异常交班'
  } else {
    return '未打卡'
  }
}

interface PlatformState {
  code: number
  name: string
  value: number
}

interface ShiftDetail {
  platformState: PlatformState[]
  exceptDesc: string
  frontDesc: string
  carrierDesc: string
  handDesc: string
  otherDesc: string
  dealDesc: string
  dealResult: string
  hygienicConditions: string
}

const detailVisible = ref(false)
const shiftForm = ref<ShiftDetail>()
const handleDetail = async (dutyDetailId: number) => {
  const data = await getDutyRecordShift({ recordId: dutyDetailId })
  if (data) {
    shiftForm.value = data
    detailVisible.value = true
  }
}
</script>

<template>
  <el-button v-if="statusInfo.dutyStatus === 1" @click="handoverDialogVisible = true">
    <img class="size-[16px] mr-1" :src="icon" />
    交班</el-button
  >
  <el-button v-else-if="statusInfo.dutyStatus === 0" @click="handleTakeOver">
    <img class="size-[16px] mr-1" :src="icon" />
    接班</el-button
  >

  <Dialog v-model="handoverDialogVisible" title="交班" width="800px">
    <el-form label-width="250px">
      <!-- 日期选择 -->
      <el-form-item label="交班日期">
        <el-date-picker
          disabled
          v-model="handoverDate"
          type="date"
          placeholder="选择日期"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <!-- 交班时段 -->
      <el-form-item label="交班时段" required>
        {{ statusInfo.startTime }}-{{
          isNextDay(statusInfo.startTime, statusInfo.endTime) + statusInfo.endTime
        }}
      </el-form-item>

      <!-- 交班时间 -->
      <el-form-item label="当前交班时间">
        <el-text>{{ currentTime }}</el-text>
      </el-form-item>
      <ExamForm :form="examForm" ref="examRef" />

      <!-- 操作按钮 -->
      <el-form-item class="mt-6">
        <el-button type="primary" @click="confirmHandover">确认交班</el-button>
        <el-button @click="cancelHandover">取消</el-button>
      </el-form-item>
    </el-form>
  </Dialog>
  <Dialog v-model="takeOverdialogVisible" title="接班" width="600px">
    <el-form label-width="120px">
      <el-form-item label="今日排班时间">
        <el-text>
          {{ statusInfo.startTime }}-{{
            isNextDay(statusInfo.startTime, statusInfo.endTime) + statusInfo.endTime
          }}</el-text
        >
      </el-form-item>
    </el-form>
    <div class="container relative p-4 border-b border-gray-300">
      <div class="list">
        <template v-for="item in todayList" :key="item.dutyDetailId">
          <div class="entry flex items-center mb-2">
            <span
              class="inline-block size-3 rounded-full mr-2"
              :class="getColorClass(item.dutyStatus)"
            ></span>
            <span class="text-sm">
              {{ item.date }}
              {{ item.startTime }}-{{ isNextDay(item.startTime, item.endTime) + item.endTime }}
              {{ item.deptName ? item.deptName : ' ' }} - {{ item.dutyUserName }}
              {{ getStatusText(item.dutyStatus) }}
            </span>
            <el-button v-if="item.id" @click="handleDetail(item.id)" size="small"
              >查看记录</el-button
            >
          </div>
        </template>
      </div>
    </div>

    <div class="flex justify-center">
      <el-button type="primary" @click="confirmTakeOver">确认接班</el-button>
      <el-button @click="cancelTakeOver">暂不接班</el-button></div
    >
  </Dialog>
  <Dialog v-model="detailVisible" title="交接班记录" width="800px">
    <ExamForm :form="shiftForm" :disabled="true" />
  </Dialog>
</template>

<style scoped>
.container {
  text-align: center;
  padding: 20px;
}
</style>
