import { defineStore } from 'pinia'
import { store } from '../index'
import type {
  LabelFieldConfig,
  ExhibitionFieldConfig
} from '@/views/Exhibition/types'

/**
 * Exhibition 表单数据状态接口
 */
export interface ExhibitionFormState {
  // 当前表单数据
  formData: Record<string, any>
  // 表单渲染配置
  formRenderData: LabelFieldConfig[]
  // 当前配置类型
  dialogType: ExhibitionFieldConfig['key'] | undefined
  // 表单验证状态
  checkFlag: {
    type: boolean | undefined
    showText: string | undefined
  }
  // 弹窗显示状态
  dialogVisible: boolean
  // 表单历史记录（可选，用于撤销等功能）
  formHistory: Record<string, any>[]
}

/**
 * Exhibition Store
 * 管理展示模块的表单数据和状态
 */
export const useExhibitionStore = defineStore('exhibition', {
  state: (): ExhibitionFormState => ({
    // 当前表单的所有字段数据，键为字段名，值为字段值
    formData: {},

    // 表单渲染所需的字段配置信息，包含字段类型、验证规则等
    formRenderData: [],

    // 当前对话框的类型标识，用于区分不同的表单配置
    dialogType: undefined,

    // 表单验证状态信息
    checkFlag: {
      type: undefined,    // 验证结果：true-通过，false-失败，undefined-未验证
      showText: undefined // 验证结果显示文本
    },

    // 对话框的显示/隐藏状态
    dialogVisible: false,

    // 表单数据的历史记录，用于撤销功能，最多保存10条
    formHistory: []
  }),

  getters: {
    /**
     * 获取当前表单数据
     */
    getFormData(): Record<string, any> {
      return this.formData
    },

    /**
     * 获取表单渲染配置
     */
    getFormRenderData(): LabelFieldConfig[] {
      return this.formRenderData
    },

    /**
     * 获取当前对话框类型
     */
    getDialogType(): ExhibitionFieldConfig['key'] | undefined {
      return this.dialogType
    },

    /**
     * 获取验证状态
     */
    getCheckFlag(): { type: boolean | undefined; showText: string | undefined } {
      return this.checkFlag
    },

    /**
     * 获取弹窗显示状态
     */
    getDialogVisible(): boolean {
      return this.dialogVisible
    },

    /**
     * 检查表单是否有数据
     */
    hasFormData(): boolean {
      return Object.keys(this.formData).length > 0
    },

    /**
     * 获取表单历史记录数量
     */
    getHistoryCount(): number {
      return this.formHistory.length
    }
  },

  actions: {
    /**
     * 设置表单数据
     * @param data 表单数据
     */
    setFormData(data: Record<string, any>): void {
      // 保存历史记录
      if (Object.keys(this.formData).length > 0) {
        this.formHistory.push({ ...this.formData })
        // 限制历史记录数量，避免内存泄漏
        if (this.formHistory.length > 10) {
          this.formHistory.shift()
        }
      }
      this.formData = { ...data }
    },

    /**
     * 更新表单字段值
     * @param field 字段名
     * @param value 字段值
     */
    updateFormField(field: string, value: any): void {
      this.formData[field] = value
    },

    /**
     * 批量更新表单字段
     * @param updates 更新的字段对象
     */
    updateFormFields(updates: Record<string, any>): void {
      Object.assign(this.formData, updates)
    },

    /**
     * 设置表单渲染配置
     * @param renderData 渲染配置数据
     */
    setFormRenderData(renderData: LabelFieldConfig[]): void {
      this.formRenderData = [...renderData]
    },

    /**
     * 设置对话框类型
     * @param type 对话框类型
     */
    setDialogType(type: ExhibitionFieldConfig['key'] | undefined): void {
      this.dialogType = type
    },

    /**
     * 设置验证状态
     * @param checkFlag 验证状态
     */
    setCheckFlag(checkFlag: { type: boolean | undefined; showText: string | undefined }): void {
      this.checkFlag = { ...checkFlag }
    },

    /**
     * 设置弹窗显示状态
     * @param visible 是否显示
     */
    setDialogVisible(visible: boolean): void {
      this.dialogVisible = visible
    },

    /**
     * 重置表单数据
     */
    resetFormData(): void {
      this.formData = {}
      this.checkFlag = {
        type: undefined,
        showText: undefined
      }
    },

    /**
     * 重置所有状态
     */
    resetAll(): void {
      this.formData = {}
      this.formRenderData = []
      this.dialogType = undefined
      this.checkFlag = {
        type: undefined,
        showText: undefined
      }
      this.dialogVisible = false
    },

    /**
     * 初始化表单字段
     * @param fields 字段配置列表
     */
    initializeFormFields(fields: LabelFieldConfig[]): void {
      const initialData: Record<string, any> = {}
      fields.forEach(field => {
        initialData[field.code] = undefined
      })
      this.setFormData(initialData)
      this.setFormRenderData(fields)
    },

    /**
     * 撤销到上一个状态
     */
    undoFormData(): boolean {
      if (this.formHistory.length > 0) {
        const previousData = this.formHistory.pop()
        if (previousData) {
          this.formData = { ...previousData }
          return true
        }
      }
      return false
    },

    /**
     * 清空历史记录
     */
    clearHistory(): void {
      this.formHistory = []
    },

    /**
     * 获取提交数据
     */
    getSubmitData(): {type: ExhibitionFieldConfig['key'] | undefined} {
      return {
        type: this.dialogType,
      }
    }
  },

  // 持久化配置（可选）
  persist: {
    key: 'exhibition-form',
    storage: sessionStorage, // 使用 sessionStorage，页面关闭后清除
    paths: ['formData', 'dialogType'] // 只持久化部分状态
  }
})

/**
 * 在组件外使用 Exhibition Store
 */
export const useExhibitionStoreWithOut = () => {
  return useExhibitionStore(store)
}